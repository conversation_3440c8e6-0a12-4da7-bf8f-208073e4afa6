#!/usr/bin/env python3
"""真正的游戏界面覆盖工具"""

import sys
import json
import cv2
import numpy as np
import win32gui
import win32ui
import win32con
import win32api
from PIL import Image
from PyQt5.QtWidgets import (QApplication, QWidget, QVBoxLayout, QHBoxLayout,
                             QLabel, QSlider, QPushButton, QGroupBox, 
                             QGridLayout, QMessageBox, QSpinBox, QCheckBox)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QPixmap, QImage
import threading
import time

class GameOverlayDrawer:
    """游戏界面覆盖绘制器"""
    
    def __init__(self):
        self.game_hwnd = None
        self.config = None
        self.is_drawing = False
        self.draw_thread = None
        
    def set_game_window(self, hwnd):
        """设置游戏窗口"""
        self.game_hwnd = hwnd
        
    def set_config(self, config):
        """设置配置"""
        self.config = config
        
    def start_drawing(self):
        """开始绘制覆盖层"""
        if self.is_drawing:
            return
            
        self.is_drawing = True
        self.draw_thread = threading.Thread(target=self._draw_loop, daemon=True)
        self.draw_thread.start()
        
    def stop_drawing(self):
        """停止绘制覆盖层"""
        self.is_drawing = False
        if self.draw_thread:
            self.draw_thread.join(timeout=1)
            
    def _draw_loop(self):
        """绘制循环"""
        while self.is_drawing:
            try:
                if self.game_hwnd and self.config:
                    self._draw_overlay()
                time.sleep(0.1)  # 100ms刷新一次
            except Exception as e:
                print(f"绘制覆盖层失败: {e}")
                break
                
    def _draw_overlay(self):
        """绘制覆盖层到游戏窗口"""
        try:
            # 获取游戏窗口的设备上下文
            hwndDC = win32gui.GetWindowDC(self.game_hwnd)
            
            # 获取窗口尺寸
            rect = win32gui.GetWindowRect(self.game_hwnd)
            width = rect[2] - rect[0]
            height = rect[3] - rect[1]
            
            # 创建内存设备上下文
            mfcDC = win32ui.CreateDCFromHandle(hwndDC)
            
            # 绘制网格覆盖层
            self._draw_grid_overlay(mfcDC, width, height)
            
            # 释放设备上下文
            mfcDC.DeleteDC()
            win32gui.ReleaseDC(self.game_hwnd, hwndDC)
            
        except Exception as e:
            print(f"绘制失败: {e}")
            
    def _draw_grid_overlay(self, dc, width, height):
        """绘制网格覆盖层"""
        try:
            # 创建画笔
            yellow_pen = win32gui.CreatePen(win32con.PS_SOLID, 3, win32api.RGB(255, 255, 0))
            red_pen = win32gui.CreatePen(win32con.PS_SOLID, 2, win32api.RGB(255, 0, 0))
            green_pen = win32gui.CreatePen(win32con.PS_SOLID, 2, win32api.RGB(0, 255, 0))
            
            # 选择画笔
            old_pen = win32gui.SelectObject(dc.GetSafeHdc(), yellow_pen)
            
            # 计算网格区域
            grid_area = self.config["grid_area"]
            grid_left = int(grid_area["left"] * width)
            grid_top = int(grid_area["top"] * height)
            grid_right = int(grid_area["right"] * width)
            grid_bottom = int(grid_area["bottom"] * height)
            
            # 绘制网格区域边框（黄色）
            win32gui.Rectangle(dc.GetSafeHdc(), grid_left, grid_top, grid_right, grid_bottom)
            
            # 切换到红色画笔绘制结界项
            win32gui.SelectObject(dc.GetSafeHdc(), red_pen)
            
            layout = self.config["layout"]
            item_size = self.config["item_size"]
            
            item_width = int(item_size["width"] * width)
            item_height = int(item_size["height"] * height)
            spacing_h = int(layout["item_spacing"]["horizontal"] * width)
            spacing_v = int(layout["item_spacing"]["vertical"] * height)
            
            for row in range(layout["rows"]):
                for col in range(layout["columns"]):
                    x = grid_left + col * (item_width + spacing_h)
                    y = grid_top + row * (item_height + spacing_v)
                    
                    # 绘制结界项边框（红色）
                    win32gui.Rectangle(dc.GetSafeHdc(), x, y, x + item_width, y + item_height)
                    
                    # 绘制中心点（绿色）
                    win32gui.SelectObject(dc.GetSafeHdc(), green_pen)
                    center_x = x + item_width // 2
                    center_y = y + item_height // 2
                    win32gui.Ellipse(dc.GetSafeHdc(), center_x - 3, center_y - 3, center_x + 3, center_y + 3)
                    
                    # 切换回红色画笔
                    win32gui.SelectObject(dc.GetSafeHdc(), red_pen)
            
            # 恢复原画笔
            win32gui.SelectObject(dc.GetSafeHdc(), old_pen)
            
            # 删除创建的画笔
            win32gui.DeleteObject(yellow_pen)
            win32gui.DeleteObject(red_pen)
            win32gui.DeleteObject(green_pen)
            
        except Exception as e:
            print(f"绘制网格失败: {e}")

class RealGameOverlayController(QWidget):
    """真正的游戏界面覆盖控制器"""
    
    def __init__(self):
        super().__init__()
        self.game_window = None
        self.overlay_drawer = GameOverlayDrawer()
        self.config = self.get_default_config()
        self.setup_ui()
        
    def setup_ui(self):
        """设置界面"""
        self.setWindowTitle("游戏界面直接覆盖调整")
        self.setFixedSize(400, 700)
        self.setWindowFlags(Qt.WindowStaysOnTopHint)
        
        layout = QVBoxLayout()
        
        # 标题
        title = QLabel("游戏界面直接覆盖调整")
        title.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50; margin: 10px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 说明
        info = QLabel("直接在游戏界面上绘制网格覆盖层")
        info.setStyleSheet("color: #7f8c8d; margin: 5px;")
        info.setAlignment(Qt.AlignCenter)
        layout.addWidget(info)
        
        # 游戏窗口连接
        connection_group = self.create_connection_group()
        layout.addWidget(connection_group)
        
        # 覆盖控制
        overlay_group = self.create_overlay_group()
        layout.addWidget(overlay_group)
        
        # 网格配置
        config_group = self.create_config_group()
        layout.addWidget(config_group)
        
        # 操作按钮
        button_group = self.create_button_group()
        layout.addWidget(button_group)
        
        self.setLayout(layout)
        
    def create_connection_group(self):
        """创建连接组"""
        group = QGroupBox("游戏窗口")
        layout = QVBoxLayout()
        
        # 连接按钮
        self.connect_btn = QPushButton("🎮 连接游戏窗口")
        self.connect_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        self.connect_btn.clicked.connect(self.connect_game_window)
        layout.addWidget(self.connect_btn)
        
        # 状态显示
        self.status_label = QLabel("未连接游戏窗口")
        self.status_label.setStyleSheet("color: #e74c3c; font-weight: bold; padding: 5px;")
        self.status_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.status_label)
        
        group.setLayout(layout)
        return group
        
    def create_overlay_group(self):
        """创建覆盖控制组"""
        group = QGroupBox("覆盖层控制")
        layout = QVBoxLayout()
        
        # 覆盖开关
        self.overlay_enabled_cb = QCheckBox("启用游戏界面覆盖")
        self.overlay_enabled_cb.setStyleSheet("font-weight: bold; color: #2c3e50;")
        self.overlay_enabled_cb.stateChanged.connect(self.toggle_overlay)
        layout.addWidget(self.overlay_enabled_cb)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        self.start_overlay_btn = QPushButton("开始覆盖")
        self.start_overlay_btn.setEnabled(False)
        self.start_overlay_btn.setStyleSheet("background-color: #27ae60; color: white; font-weight: bold; padding: 8px;")
        self.start_overlay_btn.clicked.connect(self.start_overlay)
        button_layout.addWidget(self.start_overlay_btn)
        
        self.stop_overlay_btn = QPushButton("停止覆盖")
        self.stop_overlay_btn.setEnabled(False)
        self.stop_overlay_btn.setStyleSheet("background-color: #e74c3c; color: white; font-weight: bold; padding: 8px;")
        self.stop_overlay_btn.clicked.connect(self.stop_overlay)
        button_layout.addWidget(self.stop_overlay_btn)
        
        layout.addLayout(button_layout)
        
        # 状态显示
        self.overlay_status = QLabel("覆盖层未启动")
        self.overlay_status.setStyleSheet("color: #7f8c8d; padding: 5px;")
        self.overlay_status.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.overlay_status)
        
        group.setLayout(layout)
        return group
        
    def create_config_group(self):
        """创建配置组"""
        group = QGroupBox("网格配置 (实时调整)")
        layout = QVBoxLayout()
        
        # 网格区域
        area_group = QGroupBox("网格区域")
        area_layout = QGridLayout()
        
        # 左边界
        area_layout.addWidget(QLabel("左:"), 0, 0)
        self.left_slider = QSlider(Qt.Horizontal)
        self.left_slider.setRange(0, 50)
        self.left_slider.setValue(12)
        self.left_slider.valueChanged.connect(self.update_config)
        area_layout.addWidget(self.left_slider, 0, 1)
        self.left_label = QLabel("12%")
        area_layout.addWidget(self.left_label, 0, 2)
        
        # 上边界
        area_layout.addWidget(QLabel("上:"), 1, 0)
        self.top_slider = QSlider(Qt.Horizontal)
        self.top_slider.setRange(0, 50)
        self.top_slider.setValue(20)
        self.top_slider.valueChanged.connect(self.update_config)
        area_layout.addWidget(self.top_slider, 1, 1)
        self.top_label = QLabel("20%")
        area_layout.addWidget(self.top_label, 1, 2)
        
        # 右边界
        area_layout.addWidget(QLabel("右:"), 2, 0)
        self.right_slider = QSlider(Qt.Horizontal)
        self.right_slider.setRange(50, 100)
        self.right_slider.setValue(88)
        self.right_slider.valueChanged.connect(self.update_config)
        area_layout.addWidget(self.right_slider, 2, 1)
        self.right_label = QLabel("88%")
        area_layout.addWidget(self.right_label, 2, 2)
        
        # 下边界
        area_layout.addWidget(QLabel("下:"), 3, 0)
        self.bottom_slider = QSlider(Qt.Horizontal)
        self.bottom_slider.setRange(50, 100)
        self.bottom_slider.setValue(80)
        self.bottom_slider.valueChanged.connect(self.update_config)
        area_layout.addWidget(self.bottom_slider, 3, 1)
        self.bottom_label = QLabel("80%")
        area_layout.addWidget(self.bottom_label, 3, 2)
        
        area_group.setLayout(area_layout)
        layout.addWidget(area_group)
        
        # 网格布局
        grid_group = QGroupBox("网格布局")
        grid_layout = QGridLayout()
        
        grid_layout.addWidget(QLabel("行数:"), 0, 0)
        self.rows_spin = QSpinBox()
        self.rows_spin.setRange(1, 5)
        self.rows_spin.setValue(3)
        self.rows_spin.valueChanged.connect(self.update_config)
        grid_layout.addWidget(self.rows_spin, 0, 1)
        
        grid_layout.addWidget(QLabel("列数:"), 0, 2)
        self.columns_spin = QSpinBox()
        self.columns_spin.setRange(1, 5)
        self.columns_spin.setValue(3)
        self.columns_spin.valueChanged.connect(self.update_config)
        grid_layout.addWidget(self.columns_spin, 0, 3)
        
        grid_layout.addWidget(QLabel("水平间距:"), 1, 0)
        self.h_spacing_slider = QSlider(Qt.Horizontal)
        self.h_spacing_slider.setRange(0, 10)
        self.h_spacing_slider.setValue(2)
        self.h_spacing_slider.valueChanged.connect(self.update_config)
        grid_layout.addWidget(self.h_spacing_slider, 1, 1)
        self.h_spacing_label = QLabel("2%")
        grid_layout.addWidget(self.h_spacing_label, 1, 2)
        
        grid_layout.addWidget(QLabel("垂直间距:"), 2, 0)
        self.v_spacing_slider = QSlider(Qt.Horizontal)
        self.v_spacing_slider.setRange(0, 10)
        self.v_spacing_slider.setValue(3)
        self.v_spacing_slider.valueChanged.connect(self.update_config)
        grid_layout.addWidget(self.v_spacing_slider, 2, 1)
        self.v_spacing_label = QLabel("3%")
        grid_layout.addWidget(self.v_spacing_label, 2, 2)
        
        grid_group.setLayout(grid_layout)
        layout.addWidget(grid_group)
        
        # 结界项尺寸
        size_group = QGroupBox("结界项尺寸")
        size_layout = QGridLayout()
        
        size_layout.addWidget(QLabel("宽度:"), 0, 0)
        self.width_slider = QSlider(Qt.Horizontal)
        self.width_slider.setRange(10, 40)
        self.width_slider.setValue(22)
        self.width_slider.valueChanged.connect(self.update_config)
        size_layout.addWidget(self.width_slider, 0, 1)
        self.width_label = QLabel("22%")
        size_layout.addWidget(self.width_label, 0, 2)
        
        size_layout.addWidget(QLabel("高度:"), 1, 0)
        self.height_slider = QSlider(Qt.Horizontal)
        self.height_slider.setRange(10, 30)
        self.height_slider.setValue(15)
        self.height_slider.valueChanged.connect(self.update_config)
        size_layout.addWidget(self.height_slider, 1, 1)
        self.height_label = QLabel("15%")
        size_layout.addWidget(self.height_label, 1, 2)
        
        size_group.setLayout(size_layout)
        layout.addWidget(size_group)
        
        group.setLayout(layout)
        return group
        
    def create_button_group(self):
        """创建按钮组"""
        group = QGroupBox("操作")
        layout = QVBoxLayout()
        
        save_btn = QPushButton("💾 保存配置")
        save_btn.setStyleSheet("background-color: #27ae60; color: white; font-weight: bold; padding: 10px;")
        save_btn.clicked.connect(self.save_config)
        layout.addWidget(save_btn)
        
        reset_btn = QPushButton("🔄 重置默认")
        reset_btn.clicked.connect(self.reset_config)
        layout.addWidget(reset_btn)
        
        group.setLayout(layout)
        return group
        
    def get_default_config(self):
        """获取默认配置"""
        return {
            "grid_area": {
                "left": 0.12,
                "top": 0.20,
                "right": 0.88,
                "bottom": 0.80
            },
            "layout": {
                "rows": 3,
                "columns": 3,
                "item_spacing": {
                    "horizontal": 0.02,
                    "vertical": 0.03
                }
            },
            "item_size": {
                "width": 0.22,
                "height": 0.15
            }
        }
        
    def connect_game_window(self):
        """连接游戏窗口"""
        try:
            game_windows = self.find_game_windows()
            
            if not game_windows:
                QMessageBox.warning(self, "警告", "未找到游戏窗口！\n请确保阴阳师游戏正在运行。")
                return
                
            self.game_window = game_windows[0]
            self.overlay_drawer.set_game_window(self.game_window['hwnd'])
            
            self.status_label.setText(f"✅ 已连接: {self.game_window['title']}")
            self.status_label.setStyleSheet("color: #27ae60; font-weight: bold; padding: 5px;")
            
            self.start_overlay_btn.setEnabled(True)
            self.connect_btn.setText("🔄 重新连接")
            
            QMessageBox.information(self, "连接成功", 
                f"已连接游戏窗口！\n\n"
                f"现在可以启用覆盖层，直接在游戏界面上显示网格！")
                
        except Exception as e:
            QMessageBox.critical(self, "连接失败", f"连接游戏窗口失败: {e}")
            
    def find_game_windows(self):
        """查找游戏窗口"""
        windows = []
        game_titles = ["阴阳师", "Onmyoji", "网易"]
        
        def enum_callback(hwnd, windows_list):
            if win32gui.IsWindowVisible(hwnd):
                title = win32gui.GetWindowText(hwnd)
                if title and any(game_title in title for game_title in game_titles):
                    rect = win32gui.GetWindowRect(hwnd)
                    windows_list.append({
                        'hwnd': hwnd,
                        'title': title,
                        'rect': rect,
                        'width': rect[2] - rect[0],
                        'height': rect[3] - rect[1]
                    })
            return True
            
        win32gui.EnumWindows(enum_callback, windows)
        return windows
        
    def toggle_overlay(self, state):
        """切换覆盖层"""
        if state == Qt.Checked:
            self.start_overlay()
        else:
            self.stop_overlay()
            
    def start_overlay(self):
        """开始覆盖"""
        if not self.game_window:
            QMessageBox.warning(self, "警告", "请先连接游戏窗口！")
            return
            
        try:
            self.overlay_drawer.set_config(self.config)
            self.overlay_drawer.start_drawing()
            
            self.overlay_enabled_cb.setChecked(True)
            self.start_overlay_btn.setEnabled(False)
            self.stop_overlay_btn.setEnabled(True)
            self.overlay_status.setText("✅ 覆盖层正在运行")
            self.overlay_status.setStyleSheet("color: #27ae60; padding: 5px;")
            
            QMessageBox.information(self, "覆盖启动", 
                "覆盖层已启动！\n\n"
                "现在您可以在游戏界面上看到网格覆盖层，\n"
                "拖拽滑块可以实时调整网格位置！")
                
        except Exception as e:
            QMessageBox.critical(self, "启动失败", f"启动覆盖层失败: {e}")
            
    def stop_overlay(self):
        """停止覆盖"""
        try:
            self.overlay_drawer.stop_drawing()
            
            self.overlay_enabled_cb.setChecked(False)
            self.start_overlay_btn.setEnabled(True)
            self.stop_overlay_btn.setEnabled(False)
            self.overlay_status.setText("覆盖层已停止")
            self.overlay_status.setStyleSheet("color: #7f8c8d; padding: 5px;")
            
        except Exception as e:
            QMessageBox.critical(self, "停止失败", f"停止覆盖层失败: {e}")
            
    def update_config(self):
        """更新配置"""
        self.config = {
            "grid_area": {
                "left": self.left_slider.value() / 100.0,
                "top": self.top_slider.value() / 100.0,
                "right": self.right_slider.value() / 100.0,
                "bottom": self.bottom_slider.value() / 100.0
            },
            "layout": {
                "rows": self.rows_spin.value(),
                "columns": self.columns_spin.value(),
                "item_spacing": {
                    "horizontal": self.h_spacing_slider.value() / 100.0,
                    "vertical": self.v_spacing_slider.value() / 100.0
                }
            },
            "item_size": {
                "width": self.width_slider.value() / 100.0,
                "height": self.height_slider.value() / 100.0
            }
        }
        
        # 更新标签
        self.left_label.setText(f"{self.left_slider.value()}%")
        self.top_label.setText(f"{self.top_slider.value()}%")
        self.right_label.setText(f"{self.right_slider.value()}%")
        self.bottom_label.setText(f"{self.bottom_slider.value()}%")
        self.h_spacing_label.setText(f"{self.h_spacing_slider.value()}%")
        self.v_spacing_label.setText(f"{self.v_spacing_slider.value()}%")
        self.width_label.setText(f"{self.width_slider.value()}%")
        self.height_label.setText(f"{self.height_slider.value()}%")
        
        # 更新覆盖层配置
        self.overlay_drawer.set_config(self.config)
        
    def save_config(self):
        """保存配置"""
        try:
            with open("custom_grid_config.json", 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
                
            QMessageBox.information(self, "保存成功", 
                "网格配置已保存成功！\n\n"
                "文件: custom_grid_config.json\n"
                "重启程序后自动生效。")
        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"保存配置失败: {e}")
            
    def reset_config(self):
        """重置配置"""
        self.config = self.get_default_config()
        self.load_config_to_ui()
        self.update_config()
        
    def load_config_to_ui(self):
        """加载配置到界面"""
        config = self.config
        
        self.left_slider.setValue(int(config["grid_area"]["left"] * 100))
        self.top_slider.setValue(int(config["grid_area"]["top"] * 100))
        self.right_slider.setValue(int(config["grid_area"]["right"] * 100))
        self.bottom_slider.setValue(int(config["grid_area"]["bottom"] * 100))
        
        self.rows_spin.setValue(config["layout"]["rows"])
        self.columns_spin.setValue(config["layout"]["columns"])
        self.h_spacing_slider.setValue(int(config["layout"]["item_spacing"]["horizontal"] * 100))
        self.v_spacing_slider.setValue(int(config["layout"]["item_spacing"]["vertical"] * 100))
        
        self.width_slider.setValue(int(config["item_size"]["width"] * 100))
        self.height_slider.setValue(int(config["item_size"]["height"] * 100))

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    controller = RealGameOverlayController()
    controller.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
