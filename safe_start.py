#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""安全启动模式 - 禁用可能导致崩溃的功能"""

import sys
import os
import logging
import traceback

# 设置编码
if sys.platform.startswith('win'):
    import locale
    try:
        locale.setlocale(locale.LC_ALL, 'Chinese_China.utf8')
    except:
        try:
            locale.setlocale(locale.LC_ALL, 'zh_CN.UTF-8')
        except:
            pass

from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QIcon

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def setup_safe_application():
    """设置安全的应用程序"""
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("阴阳师自动化工具")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("OnmyojiBot")
    
    # 禁用高DPI缩放（可能导致问题）
    # app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    # app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    return app

def safe_main():
    """安全启动主函数"""
    app = None
    try:
        print("🚀 安全模式启动...")
        
        # 设置基础日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[logging.StreamHandler(sys.stdout)]
        )
        
        logging.info("🔧 安全模式启动中...")
        
        # 创建应用程序
        app = setup_safe_application()
        logging.info("✅ 应用程序创建成功")
        
        # 导入主窗口（延迟导入）
        logging.info("📦 导入主窗口模块...")
        from core.main_window import MainWindow
        logging.info("✅ 主窗口模块导入成功")
        
        # 创建主窗口
        logging.info("🏗️ 创建主窗口实例...")
        main_window = MainWindow()
        logging.info("✅ 主窗口实例创建成功")
        
        # 设置窗口属性
        main_window.setWindowTitle("阴阳师自动化工具 v2.0 (安全模式)")
        
        # 显示窗口
        logging.info("🖥️ 显示主窗口...")
        main_window.show()
        logging.info("✅ 主窗口显示成功")
        
        # 处理初始事件
        app.processEvents()
        logging.info("⚡ 初始事件处理完成")
        
        # 启动事件循环
        logging.info("🔄 启动事件循环...")
        exit_code = app.exec_()
        
        logging.info(f"🏁 程序正常结束，退出代码: {exit_code}")
        return exit_code
        
    except ImportError as e:
        error_msg = f"模块导入失败: {e}"
        print(f"❌ {error_msg}")
        logging.error(error_msg)
        
        if app is None:
            app = QApplication(sys.argv)
        QMessageBox.critical(None, "导入错误", f"{error_msg}\n\n请检查依赖安装")
        return 1
        
    except Exception as e:
        error_msg = f"程序启动失败: {e}"
        print(f"❌ {error_msg}")
        logging.error(error_msg)
        logging.error(f"详细错误: {traceback.format_exc()}")
        
        if app is None:
            try:
                app = QApplication(sys.argv)
            except:
                print("无法创建QApplication")
                return 1
        
        QMessageBox.critical(None, "启动错误", f"{error_msg}\n\n详细信息请查看控制台")
        return 1

if __name__ == "__main__":
    # 设置控制台编码
    if sys.platform.startswith('win'):
        try:
            import codecs
            sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
            sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())
        except:
            pass
    
    print("🛡️ 启动安全模式...")
    exit_code = safe_main()
    print(f"🏁 安全模式结束，退出代码: {exit_code}")
    sys.exit(exit_code)
