"""结界突破GUI界面"""

import logging
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QLineEdit, QPushButton, QCheckBox, QGroupBox,
                            QSpinBox, QTabWidget, QMessageBox, QComboBox,
                            QSlider, QRadioButton, QButtonGroup)
from PyQt5.QtCore import QThread, pyqtSignal, Qt
from PyQt5.QtGui import QIcon, QColor
import traceback
import threading
from typing import Optional

from core.gui_common import WindowFinderWidget, LogViewWidget, StatsWidget
from features.realm_raid.realm_raid_bot import RealmRaidBot
from features.realm_raid.realm_raid_config import (
    DEFAULT_REALM_RAID_SETTINGS,
    SELECTION_STRATEGIES
)

class RealmRaidThread(QThread):
    """后台运行结界突破的线程"""

    update_signal = pyqtSignal(str)  # 更新日志信号
    stats_signal = pyqtSignal(dict)  # 更新统计数据信号

    def __init__(self, bot, parent=None):
        super().__init__(parent)
        self.bot = bot
        self.running = False

    def run(self):
        """运行线程"""
        self.running = True
        try:
            # 开始结界突破
            logging.info("🧵 结界突破后台线程开始运行")
            self.update_signal.emit("🚀 开始结界突破...")
            self.bot.start_realm_raid()
            logging.info("✅ 结界突破流程正常结束")
        except Exception as e:
            error_msg = f"结界突破线程发生错误: {str(e)}"
            logging.error(f"❌ {error_msg}")
            logging.error(f"错误详情: {traceback.format_exc()}")
            self.update_signal.emit(f"❌ 发生错误: {str(e)}")
            self.update_signal.emit(f"错误详情: {traceback.format_exc()}")
        finally:
            self.running = False
            logging.info("🧵 结界突破后台线程结束")
            self.update_signal.emit("🛑 结界突破停止")

    def stop(self):
        """停止线程"""
        self.running = False
        if self.bot:
            self.bot.running = False
        self.update_signal.emit("停止结界突破")


class RealmRaidGUI(QWidget):
    """结界突破GUI界面类"""

    def __init__(self):
        """初始化结界突破GUI"""
        super().__init__()
        self.realm_raid_bot: Optional[RealmRaidBot] = None
        self.raid_thread: Optional[RealmRaidThread] = None
        self.is_running = False

        # 初始化界面
        self.setup_ui()

        # 连接信号
        self.setup_connections()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主布局
        main_layout = QHBoxLayout()
        self.setLayout(main_layout)

        # 左侧控制面板
        self.setup_control_panel(main_layout)

        # 右侧日志和统计面板
        self.setup_right_panel(main_layout)
    
    def setup_control_panel(self, main_layout):
        """设置控制面板"""
        # 控制面板
        control_widget = QWidget()
        control_widget.setFixedWidth(350)
        control_layout = QVBoxLayout()
        control_widget.setLayout(control_layout)

        # 窗口选择
        self.window_finder = WindowFinderWidget()
        control_layout.addWidget(self.window_finder)

        # 基本设置
        self.setup_basic_settings(control_layout)

        # 策略设置
        self.setup_strategy_settings(control_layout)

        # 安全设置
        self.setup_safety_settings(control_layout)

        # 控制按钮
        self.setup_control_buttons(control_layout)

        # 添加弹性空间
        control_layout.addStretch()

        main_layout.addWidget(control_widget)
    
    def setup_basic_settings(self, parent_layout):
        """设置基本设置"""
        basic_group = QGroupBox("基本设置")
        basic_layout = QVBoxLayout()
        basic_group.setLayout(basic_layout)

        # 最大突破次数
        max_raids_layout = QHBoxLayout()
        max_raids_layout.addWidget(QLabel("最大突破次数:"))
        self.max_raids_spin = QSpinBox()
        self.max_raids_spin.setRange(1, 50)
        self.max_raids_spin.setValue(DEFAULT_REALM_RAID_SETTINGS['max_raids_per_session'])
        max_raids_layout.addWidget(self.max_raids_spin)
        max_raids_layout.addStretch()
        basic_layout.addLayout(max_raids_layout)

        # 战斗超时时间
        timeout_layout = QHBoxLayout()
        timeout_layout.addWidget(QLabel("战斗超时(秒):"))
        self.battle_timeout_spin = QSpinBox()
        self.battle_timeout_spin.setRange(60, 300)
        self.battle_timeout_spin.setValue(DEFAULT_REALM_RAID_SETTINGS['battle_timeout'])
        timeout_layout.addWidget(self.battle_timeout_spin)
        timeout_layout.addStretch()
        basic_layout.addLayout(timeout_layout)

        # 自动战斗
        self.auto_battle_check = QCheckBox("使用自动战斗")
        self.auto_battle_check.setChecked(DEFAULT_REALM_RAID_SETTINGS['use_auto_battle'])
        basic_layout.addWidget(self.auto_battle_check)

        # 失败重试
        self.retry_defeat_check = QCheckBox("失败后重试")
        self.retry_defeat_check.setChecked(DEFAULT_REALM_RAID_SETTINGS['retry_on_defeat'])
        basic_layout.addWidget(self.retry_defeat_check)

        parent_layout.addWidget(basic_group)
    
    def setup_strategy_settings(self, parent_layout):
        """设置策略设置"""
        strategy_group = QGroupBox("选择策略")
        strategy_layout = QVBoxLayout()
        strategy_group.setLayout(strategy_layout)

        # 策略选择
        strategy_layout.addWidget(QLabel("选择策略:"))
        self.strategy_group = QButtonGroup()

        for i, (strategy, config) in enumerate(SELECTION_STRATEGIES.items()):
            radio = QRadioButton(config['description'])
            radio.setProperty("strategy", strategy)
            self.strategy_group.addButton(radio, i)
            strategy_layout.addWidget(radio)

            # 设置默认选中
            if strategy == DEFAULT_REALM_RAID_SETTINGS['target_strategy']:
                radio.setChecked(True)

        # 随机化顺序
        self.randomize_check = QCheckBox("随机化选择顺序")
        self.randomize_check.setChecked(DEFAULT_REALM_RAID_SETTINGS['randomize_order'])
        strategy_layout.addWidget(self.randomize_check)

        # 跳过冷却
        self.skip_cooldown_check = QCheckBox("跳过冷却中的结界")
        self.skip_cooldown_check.setChecked(DEFAULT_REALM_RAID_SETTINGS['skip_cooldown'])
        strategy_layout.addWidget(self.skip_cooldown_check)

        parent_layout.addWidget(strategy_group)
    
    def setup_safety_settings(self, parent_layout):
        """设置安全设置"""
        safety_group = QGroupBox("安全设置")
        safety_layout = QVBoxLayout()
        safety_group.setLayout(safety_layout)

        # 随机延迟
        delay_layout = QHBoxLayout()
        delay_layout.addWidget(QLabel("随机延迟范围(秒):"))

        self.min_delay_spin = QSpinBox()
        self.min_delay_spin.setRange(1, 10)
        self.min_delay_spin.setValue(int(DEFAULT_REALM_RAID_SETTINGS['random_delay_min']))
        delay_layout.addWidget(QLabel("最小:"))
        delay_layout.addWidget(self.min_delay_spin)

        self.max_delay_spin = QSpinBox()
        self.max_delay_spin.setRange(1, 20)
        self.max_delay_spin.setValue(int(DEFAULT_REALM_RAID_SETTINGS['random_delay_max']))
        delay_layout.addWidget(QLabel("最大:"))
        delay_layout.addWidget(self.max_delay_spin)
        delay_layout.addStretch()
        safety_layout.addLayout(delay_layout)

        # 休息设置
        break_layout = QHBoxLayout()
        break_layout.addWidget(QLabel("休息间隔(分钟):"))
        self.break_interval_spin = QSpinBox()
        self.break_interval_spin.setRange(10, 120)
        self.break_interval_spin.setValue(DEFAULT_REALM_RAID_SETTINGS['break_interval'] // 60)
        break_layout.addWidget(self.break_interval_spin)
        break_layout.addStretch()
        safety_layout.addLayout(break_layout)

        parent_layout.addWidget(safety_group)
    
    def setup_control_buttons(self, parent_layout):
        """设置控制按钮"""
        button_layout = QHBoxLayout()

        # 开始按钮
        self.start_button = QPushButton("开始突破")
        self.start_button.clicked.connect(self.start_raid)
        button_layout.addWidget(self.start_button)

        # 停止按钮
        self.stop_button = QPushButton("停止突破")
        self.stop_button.clicked.connect(self.stop_raid)
        self.stop_button.setEnabled(False)
        button_layout.addWidget(self.stop_button)

        # 测试按钮
        self.test_button = QPushButton("测试连接")
        self.test_button.clicked.connect(self.test_connection)
        button_layout.addWidget(self.test_button)

        button_layout.addStretch()
        parent_layout.addLayout(button_layout)
    
    def setup_right_panel(self, main_layout):
        """设置右侧面板"""
        right_widget = QWidget()
        right_layout = QVBoxLayout()
        right_widget.setLayout(right_layout)

        # 统计信息
        self.stats_widget = StatsWidget()
        right_layout.addWidget(self.stats_widget)

        # 日志显示
        self.log_widget = LogViewWidget()
        right_layout.addWidget(self.log_widget)

        main_layout.addWidget(right_widget)

    def setup_connections(self):
        """设置信号连接"""
        pass
    
    def get_selected_hwnd(self):
        """获取选中的窗口句柄"""
        try:
            hwnd = self.window_finder.get_selected_hwnd()
            if hwnd:
                logging.debug(f"🪟 获取到窗口句柄: {hwnd}")
            else:
                logging.warning("⚠️ 未选择任何窗口")
            return hwnd
        except Exception as e:
            logging.error(f"❌ 获取窗口句柄失败: {e}")
            return None

    def get_current_settings(self):
        """获取当前设置"""
        # 获取选中的策略
        selected_strategy = "smart_grid"  # 默认值
        for button in self.strategy_group.buttons():
            if button.isChecked():
                selected_strategy = button.property("strategy")
                break

        return {
            'max_raids_per_session': self.max_raids_spin.value(),
            'battle_timeout': self.battle_timeout_spin.value(),
            'use_auto_battle': self.auto_battle_check.isChecked(),
            'retry_on_defeat': self.retry_defeat_check.isChecked(),
            'target_strategy': selected_strategy,
            'randomize_order': self.randomize_check.isChecked(),
            'skip_cooldown': self.skip_cooldown_check.isChecked(),
            'random_delay_min': float(self.min_delay_spin.value()),
            'random_delay_max': float(self.max_delay_spin.value()),
            'break_interval': self.break_interval_spin.value() * 60,  # 转换为秒
        }
    
    def start_raid(self):
        """开始结界突破"""
        try:
            logging.info("🚀 用户点击开始突破按钮")

            # 检查窗口选择
            hwnd = self.get_selected_hwnd()
            if not hwnd:
                error_msg = "请先选择游戏窗口"
                logging.error(f"❌ 启动失败: {error_msg}")
                QMessageBox.critical(self, "错误", error_msg)
                return

            logging.info(f"✅ 已选择窗口句柄: {hwnd}")

            # 创建机器人实例
            logging.info("🤖 正在创建结界突破机器人实例...")
            self.realm_raid_bot = RealmRaidBot(hwnd)

            # 应用用户设置
            user_settings = self.get_current_settings()
            logging.info(f"⚙️ 应用用户设置: {user_settings}")
            self.realm_raid_bot.settings.update(user_settings)

            # 更新界面状态
            self.is_running = True
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            logging.info("🔄 界面状态已更新")

            # 启动突破线程
            logging.info("🧵 启动结界突破后台线程...")
            self.raid_thread = RealmRaidThread(self.realm_raid_bot)
            self.raid_thread.update_signal.connect(self.log_widget.addLog)
            self.raid_thread.stats_signal.connect(self.stats_widget.update_stats)
            self.raid_thread.finished.connect(self.on_raid_finished)
            self.raid_thread.start()

            self.log_widget.addLog("✅ 结界突破已启动")
            logging.info("✅ 结界突破启动成功")

        except Exception as e:
            error_msg = f"启动失败: {e}"
            logging.error(f"❌ {error_msg}")
            logging.error(f"错误详情: {traceback.format_exc()}")
            QMessageBox.critical(self, "错误", error_msg)
            self.reset_ui_state()

    def stop_raid(self):
        """停止结界突破"""
        try:
            logging.info("🛑 用户点击停止突破按钮")

            if self.raid_thread and self.raid_thread.isRunning():
                logging.info("⏹️ 正在停止结界突破线程...")
                self.raid_thread.stop()

                # 等待线程结束
                if self.raid_thread.wait(5000):  # 等待5秒
                    logging.info("✅ 结界突破线程已安全停止")
                else:
                    logging.warning("⚠️ 线程停止超时，强制终止")

            self.reset_ui_state()
            self.log_widget.addLog("🛑 结界突破已停止")
            logging.info("🛑 结界突破已停止")

        except Exception as e:
            error_msg = f"停止突破失败: {e}"
            logging.error(f"❌ {error_msg}")
            logging.error(f"错误详情: {traceback.format_exc()}")
            self.log_widget.addLog(f"❌ {error_msg}")

    def test_connection(self):
        """测试连接"""
        try:
            logging.info("🔧 用户点击测试连接按钮")

            hwnd = self.get_selected_hwnd()
            if not hwnd:
                error_msg = "请先选择游戏窗口"
                logging.error(f"❌ 测试连接失败: {error_msg}")
                QMessageBox.critical(self, "错误", error_msg)
                return

            logging.info(f"🔍 开始测试窗口连接，句柄: {hwnd}")

            # 创建临时机器人实例进行测试
            logging.info("🤖 创建临时机器人实例进行测试...")
            test_bot = RealmRaidBot(hwnd)

            # 测试截图功能
            logging.info("📸 测试截图功能...")
            screenshot = test_bot.take_screenshot()

            if screenshot is not None:
                success_msg = "连接测试成功！可以正常截图。"
                logging.info(f"✅ {success_msg}")
                self.log_widget.addLog(f"✅ {success_msg}")
                QMessageBox.information(self, "成功", success_msg)
            else:
                error_msg = "连接测试失败！无法截图。"
                logging.error(f"❌ {error_msg}")
                self.log_widget.addLog(f"❌ {error_msg}")
                QMessageBox.critical(self, "失败", error_msg)

        except Exception as e:
            error_msg = f"测试连接失败: {e}"
            logging.error(f"❌ {error_msg}")
            logging.error(f"错误详情: {traceback.format_exc()}")
            self.log_widget.addLog(f"❌ {error_msg}")
            QMessageBox.critical(self, "错误", error_msg)

    def on_raid_finished(self):
        """突破结束回调"""
        logging.info("🏁 结界突破流程结束回调")
        self.reset_ui_state()
        self.log_widget.addLog("🏁 结界突破流程结束")

    def reset_ui_state(self):
        """重置界面状态"""
        logging.debug("🔄 重置GUI界面状态")
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.is_running = False
    

