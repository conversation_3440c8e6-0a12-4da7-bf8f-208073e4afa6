import os
import json
import logging
from typing import Dict, Any, Optional

class ConfigManager:
    """配置管理器
    
    用于处理程序配置的读取、保存和管理
    """
    
    def __init__(self, config_file: str = 'config.json'):
        """初始化配置管理器
        
        Args:
            config_file: 配置文件路径，默认为config.json
        """
        self.config_file = config_file
        self.config = {}
        self.load_config()
        
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件
        
        Returns:
            Dict: 配置字典
        """
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
                logging.info(f"已加载配置文件: {self.config_file}")
            else:
                logging.info(f"配置文件不存在，将使用默认配置: {self.config_file}")
                self.config = {}
        except Exception as e:
            logging.error(f"加载配置文件失败: {str(e)}")
            self.config = {}
            
        return self.config
            
    def save_config(self) -> bool:
        """保存配置到文件
        
        Returns:
            bool: 是否保存成功
        """
        try:
            # 确保目录存在
            directory = os.path.dirname(self.config_file)
            if directory and not os.path.exists(directory):
                os.makedirs(directory)
                
            # 保存配置
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)
                
            logging.info(f"配置已保存到: {self.config_file}")
            return True
        except Exception as e:
            logging.error(f"保存配置失败: {str(e)}")
            return False
            
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置项
        
        Args:
            key: 配置项键名
            default: 默认值，如果键不存在则返回该值
            
        Returns:
            Any: 配置项值
        """
        return self.config.get(key, default)
        
    def set(self, key: str, value: Any) -> None:
        """设置配置项
        
        Args:
            key: 配置项键名
            value: 配置项值
        """
        self.config[key] = value
        
    def set_and_save(self, key: str, value: Any) -> bool:
        """设置配置项并立即保存
        
        Args:
            key: 配置项键名
            value: 配置项值
            
        Returns:
            bool: 是否保存成功
        """
        self.set(key, value)
        return self.save_config()

# 全局配置管理器实例
config_manager = ConfigManager() 