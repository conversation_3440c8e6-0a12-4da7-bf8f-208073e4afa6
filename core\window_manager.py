#!/usr/bin/env python3
"""窗口管理器"""

import win32gui
import win32con
import win32api
import win32process
import psutil
import logging
from typing import List, Dict, Optional, Tuple

class WindowManager:
    """窗口管理器类"""
    
    def __init__(self):
        self.game_window_titles = [
            "阴阳师-网易游戏",
            "阴阳师",
            "Onmyoji",
            "网易阴阳师",
            "阴阳师 - 网易游戏"
        ]
    
    def find_game_windows(self) -> List[Dict]:
        """查找游戏窗口"""
        windows = []
        
        def enum_windows_callback(hwnd, windows_list):
            if win32gui.IsWindowVisible(hwnd):
                window_title = win32gui.GetWindowText(hwnd)
                if window_title and self._is_game_window(window_title):
                    try:
                        # 获取窗口位置和大小
                        rect = win32gui.GetWindowRect(hwnd)
                        width = rect[2] - rect[0]
                        height = rect[3] - rect[1]
                        
                        # 获取进程信息
                        _, pid = win32process.GetWindowThreadProcessId(hwnd)
                        
                        window_info = {
                            'hwnd': hwnd,
                            'title': window_title,
                            'rect': rect,
                            'width': width,
                            'height': height,
                            'pid': pid,
                            'x': rect[0],
                            'y': rect[1]
                        }
                        windows_list.append(window_info)
                    except Exception as e:
                        logging.debug(f"获取窗口信息失败: {e}")
            return True
        
        try:
            win32gui.EnumWindows(enum_windows_callback, windows)
            logging.info(f"找到 {len(windows)} 个游戏窗口")
            return windows
        except Exception as e:
            logging.error(f"枚举窗口失败: {e}")
            return []
    
    def _is_game_window(self, title: str) -> bool:
        """判断是否为游戏窗口"""
        title_lower = title.lower()
        for game_title in self.game_window_titles:
            if game_title.lower() in title_lower:
                return True
        return False
    
    def get_window_info(self, hwnd: int) -> Optional[Dict]:
        """获取窗口信息"""
        try:
            if not win32gui.IsWindow(hwnd):
                return None
            
            title = win32gui.GetWindowText(hwnd)
            rect = win32gui.GetWindowRect(hwnd)
            width = rect[2] - rect[0]
            height = rect[3] - rect[1]
            
            _, pid = win32process.GetWindowThreadProcessId(hwnd)
            
            return {
                'hwnd': hwnd,
                'title': title,
                'rect': rect,
                'width': width,
                'height': height,
                'pid': pid,
                'x': rect[0],
                'y': rect[1],
                'visible': win32gui.IsWindowVisible(hwnd)
            }
        except Exception as e:
            logging.error(f"获取窗口信息失败: {e}")
            return None
    
    def bring_window_to_front(self, hwnd: int) -> bool:
        """将窗口置于前台"""
        try:
            if not win32gui.IsWindow(hwnd):
                return False
            
            # 如果窗口最小化，先恢复
            if win32gui.IsIconic(hwnd):
                win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
            
            # 将窗口置于前台
            win32gui.SetForegroundWindow(hwnd)
            win32gui.BringWindowToTop(hwnd)
            
            return True
        except Exception as e:
            logging.error(f"置于前台失败: {e}")
            return False
    
    def get_window_screenshot_area(self, hwnd: int) -> Optional[Tuple[int, int, int, int]]:
        """获取窗口截图区域"""
        try:
            window_info = self.get_window_info(hwnd)
            if not window_info:
                return None
            
            # 获取客户区域（排除标题栏和边框）
            client_rect = win32gui.GetClientRect(hwnd)
            client_point = win32gui.ClientToScreen(hwnd, (0, 0))
            
            left = client_point[0]
            top = client_point[1]
            right = left + client_rect[2]
            bottom = top + client_rect[3]
            
            return (left, top, right, bottom)
        except Exception as e:
            logging.error(f"获取截图区域失败: {e}")
            return None
    
    def is_window_valid(self, hwnd: int) -> bool:
        """检查窗口是否有效"""
        try:
            return win32gui.IsWindow(hwnd) and win32gui.IsWindowVisible(hwnd)
        except:
            return False
    
    def get_active_window(self) -> Optional[Dict]:
        """获取当前活动窗口"""
        try:
            hwnd = win32gui.GetForegroundWindow()
            return self.get_window_info(hwnd)
        except Exception as e:
            logging.error(f"获取活动窗口失败: {e}")
            return None
    
    def find_window_by_title(self, title: str) -> Optional[Dict]:
        """根据标题查找窗口"""
        try:
            hwnd = win32gui.FindWindow(None, title)
            if hwnd:
                return self.get_window_info(hwnd)
            return None
        except Exception as e:
            logging.error(f"查找窗口失败: {e}")
            return None
    
    def get_all_windows(self) -> List[Dict]:
        """获取所有可见窗口"""
        windows = []
        
        def enum_callback(hwnd, windows_list):
            if win32gui.IsWindowVisible(hwnd):
                title = win32gui.GetWindowText(hwnd)
                if title.strip():  # 只包含有标题的窗口
                    window_info = self.get_window_info(hwnd)
                    if window_info:
                        windows_list.append(window_info)
            return True
        
        try:
            win32gui.EnumWindows(enum_callback, windows)
            return windows
        except Exception as e:
            logging.error(f"枚举所有窗口失败: {e}")
            return []
    
    def resize_window(self, hwnd: int, width: int, height: int) -> bool:
        """调整窗口大小"""
        try:
            if not win32gui.IsWindow(hwnd):
                return False
            
            # 获取当前位置
            rect = win32gui.GetWindowRect(hwnd)
            x, y = rect[0], rect[1]
            
            # 调整大小
            win32gui.SetWindowPos(hwnd, 0, x, y, width, height, 
                                win32con.SWP_NOZORDER | win32con.SWP_NOACTIVATE)
            return True
        except Exception as e:
            logging.error(f"调整窗口大小失败: {e}")
            return False
    
    def move_window(self, hwnd: int, x: int, y: int) -> bool:
        """移动窗口位置"""
        try:
            if not win32gui.IsWindow(hwnd):
                return False
            
            # 获取当前大小
            rect = win32gui.GetWindowRect(hwnd)
            width = rect[2] - rect[0]
            height = rect[3] - rect[1]
            
            # 移动位置
            win32gui.SetWindowPos(hwnd, 0, x, y, width, height,
                                win32con.SWP_NOZORDER | win32con.SWP_NOACTIVATE)
            return True
        except Exception as e:
            logging.error(f"移动窗口失败: {e}")
            return False
