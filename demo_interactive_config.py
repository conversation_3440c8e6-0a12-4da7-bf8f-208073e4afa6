#!/usr/bin/env python3
"""交互式网格配置功能演示"""

import sys
import os
import time

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("🖱️  交互式网格配置功能演示")
    print("=" * 60)
    print()

def print_features():
    """打印功能特性"""
    print("✨ 主要特性:")
    print("  🖱️ 鼠标拖拽调整网格区域边界")
    print("  🔄 滚轮缩放结界项大小")
    print("  🎯 实时预览配置效果")
    print("  🎛️ 滑块微调间距参数")
    print("  🎨 快速预设配置")
    print()

def print_usage():
    """打印使用方法"""
    print("🎮 操作方法:")
    print("  1. 拖拽蓝色圆形手柄 → 调整网格区域边界")
    print("  2. 拖拽红色结界项 → 调整结界项尺寸")
    print("  3. 鼠标滚轮 → 缩放结界项大小")
    print("  4. 左侧滑块 → 微调间距参数")
    print("  5. 快速预设 → 一键应用常用配置")
    print()

def demo_launch_methods():
    """演示启动方法"""
    print("🚀 启动方法:")
    print("  方法一: 主界面 → 结界突破 → '🖱️ 拖拽配置'")
    print("  方法二: python features/realm_raid/interactive_grid_config.py")
    print("  方法三: python test_interactive_config.py")
    print()

def demo_interactive_features():
    """演示交互功能"""
    print("🎯 交互功能演示:")
    print("  ┌─ 网格区域调整 ─┐")
    print("  │ 🔵 ← 左边界    │")
    print("  │ 🔵 ← 上边界    │")
    print("  │ 🔵 ← 右边界    │")
    print("  │ 🔵 ← 下边界    │")
    print("  └─────────────────┘")
    print()
    print("  ┌─ 结界项调整 ─┐")
    print("  │ 🔴 🔴 🔴    │")
    print("  │ 0  1  2      │")
    print("  │ 🔴 🔴 🔴    │")
    print("  │ 3  4  5      │")
    print("  │ 🔴 🔴 🔴    │")
    print("  │ 6  7  8      │")
    print("  └─────────────────┘")
    print()

def demo_presets():
    """演示预设配置"""
    print("🎨 快速预设:")
    presets = {
        "标准 3×3": "适合大多数屏幕分辨率",
        "紧凑 3×3": "适合小屏幕或笔记本",
        "宽松 3×3": "适合大屏幕或高分辨率"
    }
    
    for name, desc in presets.items():
        print(f"  • {name}: {desc}")
    print()

def demo_advantages():
    """演示优势"""
    print("✅ 相比传统数值配置的优势:")
    advantages = [
        "直观可视: 所见即所得的配置体验",
        "操作简单: 鼠标拖拽比数值输入更直观",
        "实时反馈: 立即看到调整效果",
        "精确控制: 支持像素级精确调整",
        "用户友好: 降低配置门槛"
    ]
    
    for i, advantage in enumerate(advantages, 1):
        print(f"  {i}. {advantage}")
    print()

def launch_demo():
    """启动演示"""
    print("🎬 准备启动交互式配置演示...")
    print("   (按 Ctrl+C 可随时退出)")
    print()
    
    try:
        from PyQt5.QtWidgets import QApplication
        from features.realm_raid.interactive_grid_config import InteractiveGridConfigDialog
        
        app = QApplication(sys.argv)
        
        # 默认配置
        default_config = {
            "grid_area": {"left": 0.12, "top": 0.20, "right": 0.88, "bottom": 0.80},
            "layout": {"rows": 3, "columns": 3, "item_spacing": {"horizontal": 0.02, "vertical": 0.03}},
            "item_size": {"width": 0.22, "height": 0.15}
        }
        
        print("🎯 启动交互式网格配置界面...")
        dialog = InteractiveGridConfigDialog(None, default_config)
        
        result = dialog.exec_()
        
        if result == dialog.Accepted:
            config = dialog.get_config()
            print("✅ 演示完成！用户保存了配置")
            print("📊 配置摘要:")
            print(f"   网格区域: {config['grid_area']['left']:.2f}-{config['grid_area']['right']:.2f} × {config['grid_area']['top']:.2f}-{config['grid_area']['bottom']:.2f}")
            print(f"   结界尺寸: {config['item_size']['width']:.2f} × {config['item_size']['height']:.2f}")
            print(f"   网格布局: {config['layout']['rows']}×{config['layout']['columns']}")
        else:
            print("❌ 演示取消")
        
        app.quit()
        
    except ImportError:
        print("❌ 缺少依赖: 请安装 PyQt5")
        print("   pip install PyQt5")
    except Exception as e:
        print(f"❌ 演示失败: {e}")

def main():
    """主函数"""
    print_banner()
    print_features()
    print_usage()
    demo_launch_methods()
    demo_interactive_features()
    demo_presets()
    demo_advantages()
    
    # 询问是否启动演示
    try:
        choice = input("🎬 是否启动交互式配置演示? (y/n): ").lower().strip()
        if choice in ['y', 'yes', '是', '启动']:
            launch_demo()
        else:
            print("👋 演示结束，感谢体验！")
    except KeyboardInterrupt:
        print("\n👋 演示结束，感谢体验！")

if __name__ == "__main__":
    main()
