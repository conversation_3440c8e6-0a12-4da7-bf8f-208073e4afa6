#!/usr/bin/env python3
"""游戏界面覆盖调整工具"""

import sys
import json
import win32gui
import win32con
from PyQt5.QtWidgets import (QApplication, QWidget, QVBoxLayout, QHBoxLayout,
                             QLabel, QSlider, QPushButton, QGroupBox, 
                             QGridLayout, QMessageBox, QFrame, QSpinBox)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QPainter, QPen, QBrush, QColor, QFont

class GameOverlayWindow(QWidget):
    """游戏界面覆盖窗口"""
    
    def __init__(self, game_window_rect, config):
        super().__init__()
        self.game_rect = game_window_rect
        self.config = config
        self.setup_overlay()
    
    def setup_overlay(self):
        """设置覆盖窗口"""
        # 设置窗口属性
        self.setWindowFlags(
            Qt.WindowStaysOnTopHint |           # 始终置顶
            Qt.FramelessWindowHint |            # 无边框
            Qt.Tool                             # 工具窗口
        )
        
        # 设置窗口透明
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setAttribute(Qt.WA_TransparentForMouseEvents, False)  # 允许鼠标事件
        
        # 设置窗口位置和大小（覆盖游戏窗口）
        self.setGeometry(
            self.game_rect[0], 
            self.game_rect[1], 
            self.game_rect[2] - self.game_rect[0], 
            self.game_rect[3] - self.game_rect[1]
        )
        
        # 设置样式
        self.setStyleSheet("background-color: transparent;")
    
    def update_config(self, config):
        """更新配置"""
        self.config = config
        self.update()
    
    def paintEvent(self, event):
        """绘制覆盖层"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 获取窗口尺寸
        width = self.width()
        height = self.height()
        
        # 绘制半透明背景
        painter.fillRect(self.rect(), QColor(0, 0, 0, 30))
        
        # 绘制网格区域边框
        grid_area = self.config["grid_area"]
        grid_left = int(grid_area["left"] * width)
        grid_top = int(grid_area["top"] * height)
        grid_right = int(grid_area["right"] * width)
        grid_bottom = int(grid_area["bottom"] * height)
        
        # 黄色网格区域边框
        painter.setPen(QPen(QColor(255, 255, 0), 3))
        painter.drawRect(grid_left, grid_top, grid_right - grid_left, grid_bottom - grid_top)
        
        # 绘制网格区域标签
        painter.setPen(QPen(QColor(255, 255, 255), 2))
        painter.setFont(QFont("Arial", 12, QFont.Bold))
        painter.drawText(grid_left + 10, grid_top - 10, "网格区域")
        
        # 绘制结界项
        layout = self.config["layout"]
        item_size = self.config["item_size"]
        
        item_width = int(item_size["width"] * width)
        item_height = int(item_size["height"] * height)
        spacing_h = int(layout["item_spacing"]["horizontal"] * width)
        spacing_v = int(layout["item_spacing"]["vertical"] * height)
        
        for row in range(layout["rows"]):
            for col in range(layout["columns"]):
                x = grid_left + col * (item_width + spacing_h)
                y = grid_top + row * (item_height + spacing_v)
                
                # 绘制结界项边框（红色）
                painter.setPen(QPen(QColor(255, 0, 0), 2))
                painter.setBrush(QBrush(QColor(255, 0, 0, 50)))
                painter.drawRect(x, y, item_width, item_height)
                
                # 绘制中心点
                center_x = x + item_width // 2
                center_y = y + item_height // 2
                painter.setPen(QPen(QColor(0, 255, 0), 3))
                painter.drawEllipse(center_x - 3, center_y - 3, 6, 6)
                
                # 绘制索引
                index = row * layout["columns"] + col
                painter.setPen(QPen(QColor(255, 255, 255), 2))
                painter.setFont(QFont("Arial", 14, QFont.Bold))
                painter.drawText(x + 10, y + 25, str(index))
        
        # 绘制说明文字
        painter.setPen(QPen(QColor(255, 255, 255), 2))
        painter.setFont(QFont("Arial", 10))
        info_text = [
            "🟡 黄色边框: 网格区域",
            "🔴 红色矩形: 结界位置", 
            "🟢 绿色圆点: 中心点",
            "⚪ 白色数字: 索引编号",
            "",
            "按 ESC 键关闭覆盖层"
        ]
        
        y_offset = 30
        for line in info_text:
            painter.drawText(20, y_offset, line)
            y_offset += 20
    
    def keyPressEvent(self, event):
        """按键事件"""
        if event.key() == Qt.Key_Escape:
            self.close()
        super().keyPressEvent(event)

class GameOverlayController(QWidget):
    """游戏覆盖控制器"""
    
    config_changed = pyqtSignal(dict)
    
    def __init__(self):
        super().__init__()
        self.game_window = None
        self.overlay_window = None
        self.config = self.get_default_config()
        self.setup_ui()
    
    def setup_ui(self):
        """设置界面"""
        self.setWindowTitle("游戏界面网格覆盖调整")
        self.setFixedSize(400, 600)
        
        # 设置窗口始终置顶
        self.setWindowFlags(Qt.WindowStaysOnTopHint)
        
        layout = QVBoxLayout()
        
        # 标题
        title = QLabel("游戏界面网格覆盖调整")
        title.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50; margin: 10px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 游戏窗口连接
        connection_group = self.create_connection_group()
        layout.addWidget(connection_group)
        
        # 网格配置控制
        config_group = self.create_config_group()
        layout.addWidget(config_group)
        
        # 操作按钮
        button_group = self.create_button_group()
        layout.addWidget(button_group)
        
        self.setLayout(layout)
    
    def create_connection_group(self):
        """创建连接组"""
        group = QGroupBox("游戏窗口连接")
        layout = QVBoxLayout()
        
        # 连接按钮
        self.connect_btn = QPushButton("🎮 连接游戏窗口")
        self.connect_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        self.connect_btn.clicked.connect(self.connect_game_window)
        layout.addWidget(self.connect_btn)
        
        # 状态显示
        self.status_label = QLabel("未连接游戏窗口")
        self.status_label.setStyleSheet("color: #e74c3c; font-weight: bold; padding: 5px;")
        self.status_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.status_label)
        
        # 覆盖控制
        overlay_layout = QHBoxLayout()
        
        self.show_overlay_btn = QPushButton("显示覆盖层")
        self.show_overlay_btn.setEnabled(False)
        self.show_overlay_btn.clicked.connect(self.show_overlay)
        overlay_layout.addWidget(self.show_overlay_btn)
        
        self.hide_overlay_btn = QPushButton("隐藏覆盖层")
        self.hide_overlay_btn.setEnabled(False)
        self.hide_overlay_btn.clicked.connect(self.hide_overlay)
        overlay_layout.addWidget(self.hide_overlay_btn)
        
        layout.addLayout(overlay_layout)
        
        group.setLayout(layout)
        return group
    
    def create_config_group(self):
        """创建配置组"""
        group = QGroupBox("网格配置 (实时调整)")
        layout = QVBoxLayout()
        
        # 网格区域
        area_group = QGroupBox("网格区域")
        area_layout = QGridLayout()
        
        # 左边界
        area_layout.addWidget(QLabel("左:"), 0, 0)
        self.left_slider = QSlider(Qt.Horizontal)
        self.left_slider.setRange(0, 50)
        self.left_slider.setValue(12)
        self.left_slider.valueChanged.connect(self.update_config)
        area_layout.addWidget(self.left_slider, 0, 1)
        self.left_label = QLabel("12%")
        area_layout.addWidget(self.left_label, 0, 2)
        
        # 上边界
        area_layout.addWidget(QLabel("上:"), 1, 0)
        self.top_slider = QSlider(Qt.Horizontal)
        self.top_slider.setRange(0, 50)
        self.top_slider.setValue(20)
        self.top_slider.valueChanged.connect(self.update_config)
        area_layout.addWidget(self.top_slider, 1, 1)
        self.top_label = QLabel("20%")
        area_layout.addWidget(self.top_label, 1, 2)
        
        # 右边界
        area_layout.addWidget(QLabel("右:"), 2, 0)
        self.right_slider = QSlider(Qt.Horizontal)
        self.right_slider.setRange(50, 100)
        self.right_slider.setValue(88)
        self.right_slider.valueChanged.connect(self.update_config)
        area_layout.addWidget(self.right_slider, 2, 1)
        self.right_label = QLabel("88%")
        area_layout.addWidget(self.right_label, 2, 2)
        
        # 下边界
        area_layout.addWidget(QLabel("下:"), 3, 0)
        self.bottom_slider = QSlider(Qt.Horizontal)
        self.bottom_slider.setRange(50, 100)
        self.bottom_slider.setValue(80)
        self.bottom_slider.valueChanged.connect(self.update_config)
        area_layout.addWidget(self.bottom_slider, 3, 1)
        self.bottom_label = QLabel("80%")
        area_layout.addWidget(self.bottom_label, 3, 2)
        
        area_group.setLayout(area_layout)
        layout.addWidget(area_group)
        
        # 网格布局
        grid_group = QGroupBox("网格布局")
        grid_layout = QGridLayout()
        
        # 行数和列数
        grid_layout.addWidget(QLabel("行数:"), 0, 0)
        self.rows_spin = QSpinBox()
        self.rows_spin.setRange(1, 5)
        self.rows_spin.setValue(3)
        self.rows_spin.valueChanged.connect(self.update_config)
        grid_layout.addWidget(self.rows_spin, 0, 1)
        
        grid_layout.addWidget(QLabel("列数:"), 0, 2)
        self.columns_spin = QSpinBox()
        self.columns_spin.setRange(1, 5)
        self.columns_spin.setValue(3)
        self.columns_spin.valueChanged.connect(self.update_config)
        grid_layout.addWidget(self.columns_spin, 0, 3)
        
        # 间距
        grid_layout.addWidget(QLabel("水平间距:"), 1, 0)
        self.h_spacing_slider = QSlider(Qt.Horizontal)
        self.h_spacing_slider.setRange(0, 10)
        self.h_spacing_slider.setValue(2)
        self.h_spacing_slider.valueChanged.connect(self.update_config)
        grid_layout.addWidget(self.h_spacing_slider, 1, 1)
        self.h_spacing_label = QLabel("2%")
        grid_layout.addWidget(self.h_spacing_label, 1, 2)
        
        grid_layout.addWidget(QLabel("垂直间距:"), 2, 0)
        self.v_spacing_slider = QSlider(Qt.Horizontal)
        self.v_spacing_slider.setRange(0, 10)
        self.v_spacing_slider.setValue(3)
        self.v_spacing_slider.valueChanged.connect(self.update_config)
        grid_layout.addWidget(self.v_spacing_slider, 2, 1)
        self.v_spacing_label = QLabel("3%")
        grid_layout.addWidget(self.v_spacing_label, 2, 2)
        
        grid_group.setLayout(grid_layout)
        layout.addWidget(grid_group)
        
        # 结界项尺寸
        size_group = QGroupBox("结界项尺寸")
        size_layout = QGridLayout()
        
        size_layout.addWidget(QLabel("宽度:"), 0, 0)
        self.width_slider = QSlider(Qt.Horizontal)
        self.width_slider.setRange(10, 40)
        self.width_slider.setValue(22)
        self.width_slider.valueChanged.connect(self.update_config)
        size_layout.addWidget(self.width_slider, 0, 1)
        self.width_label = QLabel("22%")
        size_layout.addWidget(self.width_label, 0, 2)
        
        size_layout.addWidget(QLabel("高度:"), 1, 0)
        self.height_slider = QSlider(Qt.Horizontal)
        self.height_slider.setRange(10, 30)
        self.height_slider.setValue(15)
        self.height_slider.valueChanged.connect(self.update_config)
        size_layout.addWidget(self.height_slider, 1, 1)
        self.height_label = QLabel("15%")
        size_layout.addWidget(self.height_label, 1, 2)
        
        size_group.setLayout(size_layout)
        layout.addWidget(size_group)
        
        group.setLayout(layout)
        return group
    
    def create_button_group(self):
        """创建按钮组"""
        group = QGroupBox("操作")
        layout = QVBoxLayout()
        
        # 保存配置
        save_btn = QPushButton("💾 保存配置")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        save_btn.clicked.connect(self.save_config)
        layout.addWidget(save_btn)
        
        # 重置默认
        reset_btn = QPushButton("🔄 重置默认")
        reset_btn.clicked.connect(self.reset_config)
        layout.addWidget(reset_btn)
        
        group.setLayout(layout)
        return group
    
    def get_default_config(self):
        """获取默认配置"""
        return {
            "grid_area": {
                "left": 0.12,
                "top": 0.20,
                "right": 0.88,
                "bottom": 0.80
            },
            "layout": {
                "rows": 3,
                "columns": 3,
                "item_spacing": {
                    "horizontal": 0.02,
                    "vertical": 0.03
                }
            },
            "item_size": {
                "width": 0.22,
                "height": 0.15
            }
        }
    
    def connect_game_window(self):
        """连接游戏窗口"""
        try:
            # 查找游戏窗口
            game_windows = self.find_game_windows()
            
            if not game_windows:
                QMessageBox.warning(self, "警告", "未找到游戏窗口！\n请确保阴阳师游戏正在运行。")
                return
            
            # 选择第一个找到的游戏窗口
            self.game_window = game_windows[0]
            
            # 更新状态
            self.status_label.setText(f"✅ 已连接: {self.game_window['title']}")
            self.status_label.setStyleSheet("color: #27ae60; font-weight: bold; padding: 5px;")
            
            # 启用覆盖控制按钮
            self.show_overlay_btn.setEnabled(True)
            self.connect_btn.setText("🔄 重新连接")
            
            QMessageBox.information(self, "连接成功", 
                f"已成功连接游戏窗口！\n\n"
                f"窗口标题: {self.game_window['title']}\n"
                f"窗口大小: {self.game_window['width']}×{self.game_window['height']}\n\n"
                f"现在可以显示覆盖层进行调整了！")
            
        except Exception as e:
            QMessageBox.critical(self, "连接失败", f"连接游戏窗口失败: {e}")
    
    def find_game_windows(self):
        """查找游戏窗口"""
        windows = []
        game_titles = ["阴阳师", "Onmyoji", "网易"]
        
        def enum_callback(hwnd, windows_list):
            if win32gui.IsWindowVisible(hwnd):
                title = win32gui.GetWindowText(hwnd)
                if title and any(game_title in title for game_title in game_titles):
                    rect = win32gui.GetWindowRect(hwnd)
                    windows_list.append({
                        'hwnd': hwnd,
                        'title': title,
                        'rect': rect,
                        'width': rect[2] - rect[0],
                        'height': rect[3] - rect[1]
                    })
            return True
        
        win32gui.EnumWindows(enum_callback, windows)
        return windows
    
    def show_overlay(self):
        """显示覆盖层"""
        if not self.game_window:
            return
        
        try:
            # 创建覆盖窗口
            self.overlay_window = GameOverlayWindow(self.game_window['rect'], self.config)
            self.overlay_window.show()
            
            # 更新按钮状态
            self.show_overlay_btn.setEnabled(False)
            self.hide_overlay_btn.setEnabled(True)
            
            # 连接配置变更信号
            self.config_changed.connect(self.overlay_window.update_config)
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"显示覆盖层失败: {e}")
    
    def hide_overlay(self):
        """隐藏覆盖层"""
        if self.overlay_window:
            self.overlay_window.close()
            self.overlay_window = None
        
        # 更新按钮状态
        self.show_overlay_btn.setEnabled(True)
        self.hide_overlay_btn.setEnabled(False)
    
    def update_config(self):
        """更新配置"""
        self.config = {
            "grid_area": {
                "left": self.left_slider.value() / 100.0,
                "top": self.top_slider.value() / 100.0,
                "right": self.right_slider.value() / 100.0,
                "bottom": self.bottom_slider.value() / 100.0
            },
            "layout": {
                "rows": self.rows_spin.value(),
                "columns": self.columns_spin.value(),
                "item_spacing": {
                    "horizontal": self.h_spacing_slider.value() / 100.0,
                    "vertical": self.v_spacing_slider.value() / 100.0
                }
            },
            "item_size": {
                "width": self.width_slider.value() / 100.0,
                "height": self.height_slider.value() / 100.0
            }
        }
        
        # 更新标签
        self.left_label.setText(f"{self.left_slider.value()}%")
        self.top_label.setText(f"{self.top_slider.value()}%")
        self.right_label.setText(f"{self.right_slider.value()}%")
        self.bottom_label.setText(f"{self.bottom_slider.value()}%")
        self.h_spacing_label.setText(f"{self.h_spacing_slider.value()}%")
        self.v_spacing_label.setText(f"{self.v_spacing_slider.value()}%")
        self.width_label.setText(f"{self.width_slider.value()}%")
        self.height_label.setText(f"{self.height_slider.value()}%")
        
        # 发送配置变更信号
        self.config_changed.emit(self.config)
    
    def save_config(self):
        """保存配置"""
        try:
            with open("custom_grid_config.json", 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            
            QMessageBox.information(self, "保存成功", 
                "网格配置已保存成功！\n\n"
                "文件: custom_grid_config.json\n"
                "重启程序后自动生效。")
        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"保存配置失败: {e}")
    
    def reset_config(self):
        """重置配置"""
        self.config = self.get_default_config()
        self.load_config_to_ui()
        self.update_config()
    
    def load_config_to_ui(self):
        """加载配置到界面"""
        config = self.config
        
        self.left_slider.setValue(int(config["grid_area"]["left"] * 100))
        self.top_slider.setValue(int(config["grid_area"]["top"] * 100))
        self.right_slider.setValue(int(config["grid_area"]["right"] * 100))
        self.bottom_slider.setValue(int(config["grid_area"]["bottom"] * 100))
        
        self.rows_spin.setValue(config["layout"]["rows"])
        self.columns_spin.setValue(config["layout"]["columns"])
        self.h_spacing_slider.setValue(int(config["layout"]["item_spacing"]["horizontal"] * 100))
        self.v_spacing_slider.setValue(int(config["layout"]["item_spacing"]["vertical"] * 100))
        
        self.width_slider.setValue(int(config["item_size"]["width"] * 100))
        self.height_slider.setValue(int(config["item_size"]["height"] * 100))

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    controller = GameOverlayController()
    controller.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
