import logging
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QCheckBox, QGroupBox, 
                            QSpinBox, QTabWidget, QMessageBox, QToolTip, QComboBox)
from PyQt5.QtCore import QThread, pyqtSignal, Qt, QEvent
from PyQt5.QtGui import QIcon, QColor
import traceback
import json
import os
from typing import Callable, Dict, Any

from core.gui_common import WindowFinderWidget, LogViewWidget, StatsWidget
from features.explore.explore_bot import ExploreBot
from core.config_manager import config_manager

class ExploreThread(QThread):
    """后台运行探索脚本的线程"""
    
    update_signal = pyqtSignal(str)  # 更新日志信号
    stats_signal = pyqtSignal(dict)  # 更新统计数据信号
    
    def __init__(self, bot, parent=None):
        super().__init__(parent)
        self.bot = bot
        self.running = False
    
    def run(self):
        """运行线程"""
        self.running = True
        try:
            # 开始探索
            self.update_signal.emit("开始探索...")
            self.bot.start(callback=self.update_signal.emit)
        except Exception as e:
            self.update_signal.emit(f"发生错误: {str(e)}")
            self.update_signal.emit(traceback.format_exc())
        finally:
            self.running = False
            self.update_signal.emit("探索停止")
    
    def stop(self):
        """停止线程"""
        self.running = False
        if self.bot:
            self.bot.running = False
        self.update_signal.emit("停止探索")

class ExploreGUI(QWidget):
    """探索功能的GUI组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.bot = None
        self.thread = None
        self.initUI()
        
        # 加载操作模式设置（与通用设置同步）
        operation_mode = config_manager.get("operation_mode", "background")
        self.updateOperationModeUI(operation_mode)

        # 监听配置变化
        self.last_operation_mode = operation_mode
    
    def initUI(self):
        """初始化UI"""
        # 主布局
        layout = QVBoxLayout()
        
        # 创建窗口查找组件
        self.window_finder = WindowFinderWidget()
        self.window_finder.window_selected.connect(self.setGameWindow)
        layout.addWidget(self.window_finder)
        
        # 创建探索设置组
        settings_group = QGroupBox("探索设置")
        settings_layout = QVBoxLayout()
        
        # 操作模式设置
        operation_mode_layout = QHBoxLayout()
        
        # 模式切换按钮
        self.mode_btn = QPushButton("后台操作模式")
        self.mode_btn.setStyleSheet("background-color: #4CAF50; color: white;") # 默认绿色表示后台模式
        self.mode_btn.clicked.connect(self.toggleOperationMode)
        operation_mode_layout.addWidget(self.mode_btn)
        
        # 模式说明图标
        self.mode_info_label = QLabel("?")
        self.mode_info_label.setStyleSheet("color: blue; font-weight: bold; padding: 2px 5px;")
        self.mode_info_label.setToolTip(
            "操作模式说明：\n"
            "- 后台操作模式：可在游戏窗口非活动状态下运行，不干扰其他工作\n"
            "- 前台操作模式：可直观查看鼠标点击位置，便于调试和监控\n\n"
            "前台操作需要保持游戏窗口在最前端，后台操作可在后台执行"
        )
        self.mode_info_label.installEventFilter(self)
        operation_mode_layout.addWidget(self.mode_info_label)
        
        # 添加状态指示器
        self.mode_indicator = QLabel("●")
        self.mode_indicator.setStyleSheet("color: #4CAF50; font-size: 14px;") # 默认绿色表示后台模式
        operation_mode_layout.addWidget(self.mode_indicator)
        
        # 添加模式布局到设置
        settings_layout.addLayout(operation_mode_layout)
        
        # 章节选择设置
        chapter_layout = QHBoxLayout()
        chapter_layout.addWidget(QLabel("目标章节:"))
        
        # 章节下拉框
        self.chapter_combo = QComboBox()
        # 设置字体解决显示问题
        from PyQt5.QtGui import QFont
        font = QFont("微软雅黑", 12)  # 使用可用的中文字体
        if not font.exactMatch():
            font = QFont("宋体", 12)  # 备选字体
        self.chapter_combo.setFont(font)
        # 添加常用章节选项（使用数字显示，避免中文显示问题）
        for i in range(1, 31):  # 1-30章
            self.chapter_combo.addItem(f"第{i}章", i)
        self.chapter_combo.setCurrentIndex(27)  # 默认28章（索引27）
        self.chapter_combo.currentIndexChanged.connect(self.onChapterChanged)

        # 设置固定宽度确保文字完整显示（设置为1200px，应该非常明显的宽度变化）
        self.chapter_combo.setFixedWidth(1200)
        self.chapter_combo.setMinimumWidth(1200)  # 同时设置最小宽度
        self.chapter_combo.setMaximumWidth(1200)  # 同时设置最大宽度

        # 设置合适的样式确保宽度生效
        self.chapter_combo.setStyleSheet("""
            QComboBox {
                width: 400px;
                min-width: 400px;
                max-width: 400px;
                font-size: 13px;
                padding: 5px;
                border: 1px solid #ccc;
                border-radius: 4px;
                background-color: white;
            }
        """)

        print(f"🔧 DEBUG: 章节下拉框宽度已设置为1200px - 时间戳: 1753301605.2923455")
        print(f"🔧 DEBUG: 实际宽度检查 - sizeHint: {self.chapter_combo.sizeHint().width()}px")

        # 设置布局策略确保宽度生效
        from PyQt5.QtWidgets import QSizePolicy
        size_policy = QSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        self.chapter_combo.setSizePolicy(size_policy)
        chapter_layout.addWidget(self.chapter_combo)
        
        # 检测可用章节按钮（加宽显示）
        self.detect_chapters_btn = QPushButton("检测可用章节")
        self.detect_chapters_btn.clicked.connect(self.detectAvailableChapters)
        self.detect_chapters_btn.setToolTip("检测当前界面所有可用的章节")
        self.detect_chapters_btn.setFixedWidth(200)  # 设置固定宽度200px
        chapter_layout.addWidget(self.detect_chapters_btn)
        
        # OCR设置
        self.use_ocr_cb = QCheckBox("优先使用OCR识别")
        self.use_ocr_cb.setChecked(True)
        self.use_ocr_cb.setToolTip("使用文字识别技术自动识别章节号，识别失败时自动回退到图像模板")
        chapter_layout.addWidget(self.use_ocr_cb)
        
        settings_layout.addLayout(chapter_layout)
        
        # 自动重新开始
        auto_restart_layout = QHBoxLayout()
        self.auto_restart_cb = QCheckBox("自动重新开始探索")
        self.auto_restart_cb.setChecked(True)
        auto_restart_layout.addWidget(self.auto_restart_cb)
        settings_layout.addLayout(auto_restart_layout)
        
        # 自动使用体力
        auto_ap_layout = QHBoxLayout()
        self.auto_ap_cb = QCheckBox("自动使用体力")
        self.auto_ap_cb.setChecked(True)
        auto_ap_layout.addWidget(self.auto_ap_cb)
        settings_layout.addLayout(auto_ap_layout)
        
        # 自动前进
        auto_move_layout = QHBoxLayout()
        self.auto_move_cb = QCheckBox("自动前进")
        self.auto_move_cb.setChecked(True)
        auto_move_layout.addWidget(self.auto_move_cb)
        settings_layout.addLayout(auto_move_layout)
        
        # 无怪阈值
        threshold_layout = QHBoxLayout()
        threshold_layout.addWidget(QLabel("无怪阈值:"))
        self.threshold_spin = QSpinBox()
        self.threshold_spin.setRange(1, 100)
        self.threshold_spin.setValue(30)
        threshold_layout.addWidget(self.threshold_spin)
        settings_layout.addLayout(threshold_layout)
        
        # 延迟设置
        delay_layout = QHBoxLayout()
        delay_layout.addWidget(QLabel("点击延迟(秒):"))
        self.min_delay_spin = QSpinBox()
        self.min_delay_spin.setRange(1, 50)
        self.min_delay_spin.setValue(8)  # 0.8秒
        self.min_delay_spin.setSingleStep(1)
        delay_layout.addWidget(self.min_delay_spin)
        delay_layout.addWidget(QLabel("÷ 10 ~"))
        self.max_delay_spin = QSpinBox()
        self.max_delay_spin.setRange(1, 100)
        self.max_delay_spin.setValue(15)  # 1.5秒
        self.max_delay_spin.setSingleStep(1)
        delay_layout.addWidget(self.max_delay_spin)
        delay_layout.addWidget(QLabel("÷ 10"))
        settings_layout.addLayout(delay_layout)
        
        # 休息设置
        break_layout = QHBoxLayout()
        self.break_cb = QCheckBox("启用休息")
        self.break_cb.setChecked(True)
        break_layout.addWidget(self.break_cb)
        break_layout.addWidget(QLabel("间隔(分钟):"))
        self.break_interval_spin = QSpinBox()
        self.break_interval_spin.setRange(1, 120)
        self.break_interval_spin.setValue(30)
        break_layout.addWidget(self.break_interval_spin)
        break_layout.addWidget(QLabel("时长(分钟):"))
        self.break_duration_spin = QSpinBox()
        self.break_duration_spin.setRange(1, 30)
        self.break_duration_spin.setValue(3)
        break_layout.addWidget(self.break_duration_spin)
        settings_layout.addLayout(break_layout)
        
        settings_group.setLayout(settings_layout)
        layout.addWidget(settings_group)
        
        # 创建控制按钮
        control_layout = QHBoxLayout()
        
        self.start_btn = QPushButton("开始探索")
        self.start_btn.clicked.connect(self.startExplore)
        self.start_btn.setStyleSheet("background-color: #4CAF50; color: white;")
        control_layout.addWidget(self.start_btn)
        
        self.stop_btn = QPushButton("停止探索")
        self.stop_btn.clicked.connect(self.stopExplore)
        self.stop_btn.setStyleSheet("background-color: #f44336; color: white;")
        self.stop_btn.setEnabled(False)
        control_layout.addWidget(self.stop_btn)
        
        layout.addLayout(control_layout)
        
        # 创建统计组件
        self.stats_widget = StatsWidget()
        layout.addWidget(self.stats_widget)
        
        # 创建日志组件
        self.log_widget = LogViewWidget()
        layout.addWidget(self.log_widget)
        
        self.setLayout(layout)
    
    def setGameWindow(self, hwnd, title):
        """设置游戏窗口
        
        Args:
            hwnd: 窗口句柄
            title: 窗口标题
        """
        # 创建探索机器人
        self.bot = ExploreBot(hwnd)
        
        # 应用操作模式设置
        operation_mode = config_manager.get("operation_mode", "background")
        self.bot.enable_background_mode(operation_mode == "background")
        
        # 应用设置
        self.applySettings()
        
        self.log_widget.addLog(f"已设置游戏窗口: {hwnd} - {title}")
        self.log_widget.addLog(f"当前操作模式: {'后台' if operation_mode == 'background' else '前台'}")
        
        # 启用开始按钮
        self.start_btn.setEnabled(True)
    
    def applySettings(self):
        """应用设置到机器人"""
        if not self.bot:
            return

        # 重新应用操作模式设置（确保与通用设置同步）
        operation_mode = config_manager.get("operation_mode", "background")
        self.bot.enable_background_mode(operation_mode == "background")

        # 探索设置
        self.bot.auto_restart = self.auto_restart_cb.isChecked()
        self.bot.auto_use_ap = self.auto_ap_cb.isChecked()
        self.bot.move_forward = self.auto_move_cb.isChecked()
        self.bot.no_monster_threshold = self.threshold_spin.value()

        # 章节设置
        target_chapter = self.chapter_combo.currentData()
        if target_chapter:
            self.bot.set_target_chapter(target_chapter)
        self.bot.use_ocr_first = self.use_ocr_cb.isChecked()

        # 延迟设置
        self.bot.min_delay = self.min_delay_spin.value() / 10.0
        self.bot.max_delay = self.max_delay_spin.value() / 10.0

        # 休息设置
        self.bot.use_breaks = self.break_cb.isChecked()
        self.bot.break_interval = self.break_interval_spin.value() * 60  # 转换为秒
        self.bot.break_duration = self.break_duration_spin.value()
    
    def startExplore(self):
        """开始探索"""
        if not self.bot:
            QMessageBox.warning(self, "警告", "请先选择游戏窗口")
            return

        # 检查并同步操作模式
        self.checkAndSyncOperationMode()

        # 应用设置
        self.applySettings()
        
        # 创建并启动线程
        self.thread = ExploreThread(self.bot)
        self.thread.update_signal.connect(self.log_widget.addLog)
        self.thread.stats_signal.connect(self.stats_widget.updateStats)
        self.thread.finished.connect(self.onThreadFinished)
        
        # 开始统计跟踪
        self.stats_widget.startTracking()
        
        # 启动线程
        self.thread.start()
        
        # 更新按钮状态
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        
        # 定时更新统计数据
        self.stats_timer_id = self.startTimer(1000)  # 每秒更新一次
    
    def stopExplore(self):
        """停止探索"""
        if self.thread and self.thread.isRunning():
            self.thread.stop()
            self.thread.wait(3000)  # 等待最多3秒
            
            if self.thread.isRunning():
                self.log_widget.addLog("线程无法正常停止，强制终止")
                self.thread.terminate()
            
            self.onThreadFinished()
    
    def onThreadFinished(self):
        """线程结束处理"""
        # 停止统计跟踪
        self.stats_widget.stopTracking()
        
        # 停止定时器
        if hasattr(self, 'stats_timer_id'):
            self.killTimer(self.stats_timer_id)
        
        # 更新按钮状态
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
    
    def timerEvent(self, event):
        """定时器事件，用于更新统计数据"""
        if event.timerId() == self.stats_timer_id and self.bot:
            self.stats_widget.updateStats(self.bot.stats)
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        self.stopExplore()
        event.accept() 
    
    def eventFilter(self, obj, event):
        """事件过滤器，用于处理工具提示显示"""
        if obj == self.mode_info_label and event.type() == QEvent.MouseButtonPress:
            QToolTip.showText(event.globalPos(), self.mode_info_label.toolTip())
            return True
        return super().eventFilter(obj, event)
    
    def toggleOperationMode(self):
        """切换前台/后台操作模式"""
        # 获取当前模式
        is_background = config_manager.get("operation_mode", "background") == "background"

        # 切换模式
        new_mode = "foreground" if is_background else "background"
        config_manager.set_and_save("operation_mode", new_mode)

        # 更新UI
        self.updateOperationModeUI(new_mode)

        # 更新记录的模式
        self.last_operation_mode = new_mode

        # 如果有机器人实例，立即应用设置
        if self.bot:
            self.bot.enable_background_mode(new_mode == "background")
            status_text = "已切换到" + ("后台" if new_mode == "background" else "前台") + "操作模式"
            self.log_widget.addLog(status_text)
            self.log_widget.addLog("💡 提示：操作模式已同步到通用设置")

    def checkAndSyncOperationMode(self):
        """检查并同步操作模式设置"""
        # 获取当前配置中的操作模式
        current_mode = config_manager.get("operation_mode", "background")

        # 如果与上次记录的模式不同，说明在通用设置中被修改了
        if hasattr(self, 'last_operation_mode') and current_mode != self.last_operation_mode:
            self.log_widget.addLog(f"🔄 检测到操作模式变更: {self.last_operation_mode} → {current_mode}")
            self.updateOperationModeUI(current_mode)
            self.last_operation_mode = current_mode

            # 如果有机器人实例，立即应用
            if self.bot:
                self.bot.enable_background_mode(current_mode == "background")
                mode_text = "后台" if current_mode == "background" else "前台"
                self.log_widget.addLog(f"✅ 已同步到{mode_text}操作模式")

    def updateOperationModeUI(self, mode):
        """更新操作模式UI
        
        Args:
            mode: 操作模式，"background"或"foreground"
        """
        if mode == "background":
            self.mode_btn.setText("后台操作模式")
            self.mode_btn.setStyleSheet("background-color: #4CAF50; color: white;")
            self.mode_indicator.setStyleSheet("color: #4CAF50; font-size: 14px;")
        else:
            self.mode_btn.setText("前台操作模式")
            self.mode_btn.setStyleSheet("background-color: #2196F3; color: white;")
            self.mode_indicator.setStyleSheet("color: #2196F3; font-size: 14px;")
    
    def onChapterChanged(self):
        """章节选择改变时的处理"""
        if self.bot:
            target_chapter = self.chapter_combo.currentData()
            if target_chapter:
                self.bot.set_target_chapter(target_chapter)
                self.log_widget.addLog(f"目标章节已设置为: 第{target_chapter}章")
    
    def detectAvailableChapters(self):
        """检测可用章节"""
        if not self.bot:
            QMessageBox.warning(self, "警告", "请先选择游戏窗口")
            return
        
        try:
            self.log_widget.addLog("正在检测可用章节...")
            self.detect_chapters_btn.setEnabled(False)
            self.detect_chapters_btn.setText("检测中...")
            
            # 获取可用章节
            available_chapters = self.bot.get_available_chapters()
            
            if available_chapters:
                chapters_text = "、".join([f"第{ch}章" for ch in sorted(available_chapters)])
                self.log_widget.addLog(f"检测到可用章节: {chapters_text}")
                
                # 更新下拉框显示，高亮可用章节（不添加文字标记，避免显示问题）
                # 注释掉添加✓标记的代码，因为它会导致文字显示不完整
                # for i in range(self.chapter_combo.count()):
                #     chapter_num = self.chapter_combo.itemData(i)
                #     if chapter_num in available_chapters:
                #         # 为可用章节添加标记
                #         current_text = self.chapter_combo.itemText(i)
                #         if not current_text.endswith(" ✓"):
                #             self.chapter_combo.setItemText(i, current_text + " ✓")
                
            else:
                self.log_widget.addLog("未检测到任何章节，请确保游戏在章节选择界面")
                
        except Exception as e:
            self.log_widget.addLog(f"检测章节时出错: {str(e)}")
        finally:
            self.detect_chapters_btn.setEnabled(True)
            self.detect_chapters_btn.setText("检测可用章节") 