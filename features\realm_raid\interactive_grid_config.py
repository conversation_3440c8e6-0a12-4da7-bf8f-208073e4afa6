#!/usr/bin/env python3
"""交互式网格配置界面 - 专注于鼠标拖拽和缩放操作"""

import sys
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QGroupBox, QFrame, QMessageBox, 
                             QApplication, QSlider, QSpinBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QPainter, QPen, QBrush, QColor, QFont
import json
import os

class InteractiveGridConfigDialog(QDialog):
    """交互式网格配置对话框"""
    
    def __init__(self, parent=None, current_config=None):
        super().__init__(parent)
        self.current_config = current_config or self.get_default_config()
        self.setup_ui()
        self.load_config()
    
    def setup_ui(self):
        """设置界面"""
        self.setWindowTitle("交互式网格配置 - 鼠标拖拽调整")
        self.setFixedSize(800, 700)
        
        layout = QVBoxLayout()
        
        # 标题和说明
        title = QLabel("🖱️ 交互式网格配置")
        title.setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50; margin: 10px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 操作说明
        instructions = QLabel(
            "• 拖拽蓝色手柄调整网格区域边界\n"
            "• 拖拽红色结界项调整尺寸\n" 
            "• 使用鼠标滚轮缩放结界项\n"
            "• 使用下方滑块微调间距"
        )
        instructions.setStyleSheet("color: #7f8c8d; margin: 5px; padding: 10px; background-color: #ecf0f1; border-radius: 5px;")
        layout.addWidget(instructions)
        
        # 主要内容区域
        content_layout = QHBoxLayout()
        
        # 左侧控制面板
        control_panel = self.create_control_panel()
        content_layout.addWidget(control_panel)
        
        # 右侧预览区域
        preview_group = self.create_preview_group()
        content_layout.addWidget(preview_group)
        
        layout.addLayout(content_layout)
        
        # 按钮区域
        button_layout = self.create_button_layout()
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
    
    def create_control_panel(self):
        """创建控制面板"""
        group = QGroupBox("微调控制")
        group.setFixedWidth(250)
        layout = QVBoxLayout()
        
        # 网格布局控制
        grid_layout_group = QGroupBox("网格布局")
        grid_layout = QVBoxLayout()
        
        # 行数
        row_layout = QHBoxLayout()
        row_layout.addWidget(QLabel("行数:"))
        self.rows_spin = QSpinBox()
        self.rows_spin.setRange(1, 5)
        self.rows_spin.valueChanged.connect(self.update_preview)
        row_layout.addWidget(self.rows_spin)
        grid_layout.addLayout(row_layout)
        
        # 列数
        col_layout = QHBoxLayout()
        col_layout.addWidget(QLabel("列数:"))
        self.columns_spin = QSpinBox()
        self.columns_spin.setRange(1, 5)
        self.columns_spin.valueChanged.connect(self.update_preview)
        col_layout.addWidget(self.columns_spin)
        grid_layout.addLayout(col_layout)
        
        grid_layout_group.setLayout(grid_layout)
        layout.addWidget(grid_layout_group)
        
        # 间距控制
        spacing_group = QGroupBox("间距调整")
        spacing_layout = QVBoxLayout()
        
        # 水平间距
        h_spacing_layout = QVBoxLayout()
        h_spacing_layout.addWidget(QLabel("水平间距:"))
        self.h_spacing_slider = QSlider(Qt.Horizontal)
        self.h_spacing_slider.setRange(0, 100)  # 0-10%，以0.1%为单位
        self.h_spacing_slider.valueChanged.connect(self.on_h_spacing_changed)
        h_spacing_layout.addWidget(self.h_spacing_slider)
        self.h_spacing_label = QLabel("2.0%")
        h_spacing_layout.addWidget(self.h_spacing_label)
        spacing_layout.addLayout(h_spacing_layout)
        
        # 垂直间距
        v_spacing_layout = QVBoxLayout()
        v_spacing_layout.addWidget(QLabel("垂直间距:"))
        self.v_spacing_slider = QSlider(Qt.Horizontal)
        self.v_spacing_slider.setRange(0, 100)  # 0-10%，以0.1%为单位
        self.v_spacing_slider.valueChanged.connect(self.on_v_spacing_changed)
        v_spacing_layout.addWidget(self.v_spacing_slider)
        self.v_spacing_label = QLabel("3.0%")
        v_spacing_layout.addWidget(self.v_spacing_label)
        spacing_layout.addLayout(v_spacing_layout)
        
        spacing_group.setLayout(spacing_layout)
        layout.addWidget(spacing_group)
        
        # 快速预设
        preset_group = QGroupBox("快速预设")
        preset_layout = QVBoxLayout()
        
        preset_btn1 = QPushButton("标准 3×3")
        preset_btn1.clicked.connect(lambda: self.apply_preset("standard_3x3"))
        preset_layout.addWidget(preset_btn1)
        
        preset_btn2 = QPushButton("紧凑 3×3")
        preset_btn2.clicked.connect(lambda: self.apply_preset("compact_3x3"))
        preset_layout.addWidget(preset_btn2)
        
        preset_btn3 = QPushButton("宽松 3×3")
        preset_btn3.clicked.connect(lambda: self.apply_preset("loose_3x3"))
        preset_layout.addWidget(preset_btn3)
        
        preset_group.setLayout(preset_layout)
        layout.addWidget(preset_group)
        
        layout.addStretch()
        group.setLayout(layout)
        return group
    
    def create_preview_group(self):
        """创建预览组"""
        group = QGroupBox("实时预览")
        layout = QVBoxLayout()
        
        self.preview_widget = InteractiveGridPreviewWidget()
        self.preview_widget.setFixedSize(500, 400)
        
        # 设置配置变更回调
        self.preview_widget.set_config_changed_callback(self.on_preview_config_changed)
        
        layout.addWidget(self.preview_widget)
        
        group.setLayout(layout)
        return group
    
    def create_button_layout(self):
        """创建按钮布局"""
        layout = QHBoxLayout()
        
        # 重置按钮
        reset_btn = QPushButton("🔄 重置默认")
        reset_btn.clicked.connect(self.reset_to_default)
        layout.addWidget(reset_btn)
        
        layout.addStretch()
        
        # 取消按钮
        cancel_btn = QPushButton("❌ 取消")
        cancel_btn.clicked.connect(self.reject)
        layout.addWidget(cancel_btn)
        
        # 确定按钮
        ok_btn = QPushButton("✅ 保存配置")
        ok_btn.clicked.connect(self.accept)
        ok_btn.setStyleSheet("QPushButton { background-color: #27ae60; color: white; font-weight: bold; padding: 8px 16px; }")
        layout.addWidget(ok_btn)
        
        return layout
    
    def on_h_spacing_changed(self, value):
        """水平间距滑块变更"""
        spacing = value / 1000.0  # 转换为0-0.1的范围
        self.h_spacing_label.setText(f"{spacing*100:.1f}%")
        self.current_config["layout"]["item_spacing"]["horizontal"] = spacing
        self.update_preview()
    
    def on_v_spacing_changed(self, value):
        """垂直间距滑块变更"""
        spacing = value / 1000.0  # 转换为0-0.1的范围
        self.v_spacing_label.setText(f"{spacing*100:.1f}%")
        self.current_config["layout"]["item_spacing"]["vertical"] = spacing
        self.update_preview()
    
    def apply_preset(self, preset_name):
        """应用预设配置"""
        presets = {
            "standard_3x3": {
                "grid_area": {"left": 0.12, "top": 0.20, "right": 0.88, "bottom": 0.80},
                "layout": {"rows": 3, "columns": 3, "item_spacing": {"horizontal": 0.02, "vertical": 0.03}},
                "item_size": {"width": 0.22, "height": 0.15}
            },
            "compact_3x3": {
                "grid_area": {"left": 0.15, "top": 0.25, "right": 0.85, "bottom": 0.75},
                "layout": {"rows": 3, "columns": 3, "item_spacing": {"horizontal": 0.01, "vertical": 0.02}},
                "item_size": {"width": 0.18, "height": 0.12}
            },
            "loose_3x3": {
                "grid_area": {"left": 0.10, "top": 0.15, "right": 0.90, "bottom": 0.85},
                "layout": {"rows": 3, "columns": 3, "item_spacing": {"horizontal": 0.03, "vertical": 0.04}},
                "item_size": {"width": 0.25, "height": 0.18}
            }
        }
        
        if preset_name in presets:
            self.current_config = presets[preset_name]
            self.load_config()
    
    def get_default_config(self):
        """获取默认配置"""
        return {
            "grid_area": {
                "left": 0.12,
                "top": 0.20,
                "right": 0.88,
                "bottom": 0.80
            },
            "layout": {
                "rows": 3,
                "columns": 3,
                "item_spacing": {
                    "horizontal": 0.02,
                    "vertical": 0.03
                }
            },
            "item_size": {
                "width": 0.22,
                "height": 0.15
            }
        }
    
    def load_config(self):
        """加载配置到界面"""
        config = self.current_config
        
        # 网格布局
        self.rows_spin.setValue(config["layout"]["rows"])
        self.columns_spin.setValue(config["layout"]["columns"])
        
        # 间距滑块
        h_spacing = config["layout"]["item_spacing"]["horizontal"]
        v_spacing = config["layout"]["item_spacing"]["vertical"]
        
        self.h_spacing_slider.setValue(int(h_spacing * 1000))
        self.v_spacing_slider.setValue(int(v_spacing * 1000))
        
        self.h_spacing_label.setText(f"{h_spacing*100:.1f}%")
        self.v_spacing_label.setText(f"{v_spacing*100:.1f}%")
        
        self.update_preview()
    
    def get_config(self):
        """获取当前配置"""
        return self.current_config.copy()
    
    def update_preview(self):
        """更新预览"""
        # 更新配置中的行列数
        self.current_config["layout"]["rows"] = self.rows_spin.value()
        self.current_config["layout"]["columns"] = self.columns_spin.value()
        
        self.preview_widget.update_config(self.current_config)
    
    def on_preview_config_changed(self, config):
        """预览组件配置变更回调"""
        self.current_config = config
    
    def reset_to_default(self):
        """重置为默认配置"""
        self.current_config = self.get_default_config()
        self.load_config()


class InteractiveGridPreviewWidget(QFrame):
    """交互式网格预览组件"""
    
    def __init__(self):
        super().__init__()
        self.config = None
        self.setStyleSheet("border: 2px solid #bdc3c7; background-color: #ecf0f1; border-radius: 5px;")
        
        # 交互状态
        self.dragging = False
        self.drag_start_pos = None
        self.drag_target = None
        self.drag_handle = None
        
        # 鼠标跟踪
        self.setMouseTracking(True)
        
        # 缩放参数
        self.zoom_factor = 1.0
        self.min_zoom = 0.5
        self.max_zoom = 3.0
        
        # 拖拽手柄大小
        self.handle_size = 10
        
        # 回调函数
        self.config_changed_callback = None
    
    def set_config_changed_callback(self, callback):
        """设置配置变更回调函数"""
        self.config_changed_callback = callback
    
    def update_config(self, config):
        """更新配置"""
        self.config = config.copy()
        self.update()

    def wheelEvent(self, event):
        """鼠标滚轮事件 - 缩放结界项"""
        if self.config is None:
            return

        delta = event.angleDelta().y()
        zoom_in = delta > 0

        # 计算缩放步长
        zoom_step = 0.05
        if zoom_in:
            new_zoom = min(self.zoom_factor + zoom_step, self.max_zoom)
        else:
            new_zoom = max(self.zoom_factor - zoom_step, self.min_zoom)

        if new_zoom != self.zoom_factor:
            scale_ratio = new_zoom / self.zoom_factor
            self.zoom_factor = new_zoom

            # 缩放结界项尺寸
            current_width = self.config["item_size"]["width"]
            current_height = self.config["item_size"]["height"]

            new_width = max(0.05, min(0.5, current_width * scale_ratio))
            new_height = max(0.05, min(0.5, current_height * scale_ratio))

            self.config["item_size"]["width"] = new_width
            self.config["item_size"]["height"] = new_height

            if self.config_changed_callback:
                self.config_changed_callback(self.config)

            self.update()

    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() != Qt.LeftButton or self.config is None:
            return

        pos = event.pos()
        self.drag_start_pos = pos

        target, handle = self.get_drag_target(pos)
        if target:
            self.dragging = True
            self.drag_target = target
            self.drag_handle = handle
            self.setCursor(Qt.ClosedHandCursor)

    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if self.config is None:
            return

        pos = event.pos()

        if self.dragging and self.drag_start_pos:
            self.handle_drag(pos)
        else:
            target, handle = self.get_drag_target(pos)
            if target:
                if handle in ['left', 'right']:
                    self.setCursor(Qt.SizeHorCursor)
                elif handle in ['top', 'bottom']:
                    self.setCursor(Qt.SizeVerCursor)
                elif handle == 'item':
                    self.setCursor(Qt.SizeAllCursor)
                else:
                    self.setCursor(Qt.OpenHandCursor)
            else:
                self.setCursor(Qt.ArrowCursor)

    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton and self.dragging:
            self.dragging = False
            self.drag_start_pos = None
            self.drag_target = None
            self.drag_handle = None
            self.setCursor(Qt.ArrowCursor)

    def get_drag_target(self, pos):
        """获取拖拽目标"""
        if not self.config:
            return None, None

        width = self.width()
        height = self.height()

        # 网格区域坐标
        grid_area = self.config["grid_area"]
        grid_left = int(grid_area["left"] * width)
        grid_top = int(grid_area["top"] * height)
        grid_right = int(grid_area["right"] * width)
        grid_bottom = int(grid_area["bottom"] * height)

        handle_size = self.handle_size

        # 检测网格区域边界手柄
        if abs(pos.x() - grid_left) <= handle_size and grid_top <= pos.y() <= grid_bottom:
            return 'grid_area', 'left'
        if abs(pos.x() - grid_right) <= handle_size and grid_top <= pos.y() <= grid_bottom:
            return 'grid_area', 'right'
        if abs(pos.y() - grid_top) <= handle_size and grid_left <= pos.x() <= grid_right:
            return 'grid_area', 'top'
        if abs(pos.y() - grid_bottom) <= handle_size and grid_left <= pos.x() <= grid_right:
            return 'grid_area', 'bottom'

        # 检测结界项
        layout = self.config["layout"]
        item_size = self.config["item_size"]

        item_width = int(item_size["width"] * width)
        item_height = int(item_size["height"] * height)
        spacing_h = int(layout["item_spacing"]["horizontal"] * width)
        spacing_v = int(layout["item_spacing"]["vertical"] * height)

        for row in range(layout["rows"]):
            for col in range(layout["columns"]):
                x = grid_left + col * (item_width + spacing_h)
                y = grid_top + row * (item_height + spacing_v)

                if x <= pos.x() <= x + item_width and y <= pos.y() <= y + item_height:
                    return 'item_size', 'item'

        return None, None

    def handle_drag(self, pos):
        """处理拖拽操作"""
        if not self.drag_start_pos or not self.config:
            return

        width = self.width()
        height = self.height()

        dx = pos.x() - self.drag_start_pos.x()
        dy = pos.y() - self.drag_start_pos.y()

        dx_rel = dx / width
        dy_rel = dy / height

        if self.drag_target == 'grid_area':
            self.handle_grid_area_drag(dx_rel, dy_rel)
        elif self.drag_target == 'item_size':
            self.handle_item_size_drag(dx_rel, dy_rel)

        self.drag_start_pos = pos

        if self.config_changed_callback:
            self.config_changed_callback(self.config)

        self.update()

    def handle_grid_area_drag(self, dx_rel, dy_rel):
        """处理网格区域拖拽"""
        grid_area = self.config["grid_area"]

        if self.drag_handle == 'left':
            new_left = max(0.0, min(grid_area["right"] - 0.1, grid_area["left"] + dx_rel))
            grid_area["left"] = new_left
        elif self.drag_handle == 'right':
            new_right = max(grid_area["left"] + 0.1, min(1.0, grid_area["right"] + dx_rel))
            grid_area["right"] = new_right
        elif self.drag_handle == 'top':
            new_top = max(0.0, min(grid_area["bottom"] - 0.1, grid_area["top"] + dy_rel))
            grid_area["top"] = new_top
        elif self.drag_handle == 'bottom':
            new_bottom = max(grid_area["top"] + 0.1, min(1.0, grid_area["bottom"] + dy_rel))
            grid_area["bottom"] = new_bottom

    def handle_item_size_drag(self, dx_rel, dy_rel):
        """处理结界项尺寸拖拽"""
        item_size = self.config["item_size"]

        if abs(dx_rel) > abs(dy_rel):
            new_width = max(0.05, min(0.5, item_size["width"] + dx_rel))
            item_size["width"] = new_width
        else:
            new_height = max(0.05, min(0.5, item_size["height"] + dy_rel))
            item_size["height"] = new_height

    def paintEvent(self, event):
        """绘制预览"""
        super().paintEvent(event)

        if not self.config:
            return

        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        width = self.width()
        height = self.height()

        # 绘制背景网格
        self.draw_background_grid(painter, width, height)

        # 绘制网格区域
        grid_area = self.config["grid_area"]
        grid_left = int(grid_area["left"] * width)
        grid_top = int(grid_area["top"] * height)
        grid_right = int(grid_area["right"] * width)
        grid_bottom = int(grid_area["bottom"] * height)

        # 绘制网格区域边框
        painter.setPen(QPen(QColor("#3498db"), 3))
        painter.setBrush(QBrush(QColor("#3498db"), Qt.NoBrush))
        painter.drawRect(grid_left, grid_top, grid_right - grid_left, grid_bottom - grid_top)

        # 绘制网格区域拖拽手柄
        self.draw_grid_handles(painter, grid_left, grid_top, grid_right, grid_bottom)

        # 绘制结界项
        self.draw_grid_items(painter, grid_left, grid_top, width, height)

        # 绘制状态信息
        self.draw_status_info(painter, width, height)

    def draw_background_grid(self, painter, width, height):
        """绘制背景网格"""
        painter.setPen(QPen(QColor("#d5dbdb"), 1))

        # 绘制垂直线
        for i in range(1, 10):
            x = width * i / 10
            painter.drawLine(x, 0, x, height)

        # 绘制水平线
        for i in range(1, 10):
            y = height * i / 10
            painter.drawLine(0, y, width, y)

    def draw_grid_handles(self, painter, left, top, right, bottom):
        """绘制网格区域拖拽手柄"""
        handle_size = self.handle_size

        painter.setPen(QPen(QColor("#2980b9"), 2))
        painter.setBrush(QBrush(QColor("#3498db"), Qt.SolidPattern))

        # 左边界手柄
        painter.drawEllipse(left - handle_size//2, (top + bottom)//2 - handle_size//2,
                           handle_size, handle_size)

        # 右边界手柄
        painter.drawEllipse(right - handle_size//2, (top + bottom)//2 - handle_size//2,
                           handle_size, handle_size)

        # 上边界手柄
        painter.drawEllipse((left + right)//2 - handle_size//2, top - handle_size//2,
                           handle_size, handle_size)

        # 下边界手柄
        painter.drawEllipse((left + right)//2 - handle_size//2, bottom - handle_size//2,
                           handle_size, handle_size)

    def draw_grid_items(self, painter, grid_left, grid_top, width, height):
        """绘制结界项"""
        layout = self.config["layout"]
        item_size = self.config["item_size"]

        item_width = int(item_size["width"] * width)
        item_height = int(item_size["height"] * height)
        spacing_h = int(layout["item_spacing"]["horizontal"] * width)
        spacing_v = int(layout["item_spacing"]["vertical"] * height)

        painter.setPen(QPen(QColor("#e74c3c"), 2))
        painter.setBrush(QBrush(QColor("#e74c3c"), Qt.SolidPattern))

        for row in range(layout["rows"]):
            for col in range(layout["columns"]):
                x = grid_left + col * (item_width + spacing_h)
                y = grid_top + row * (item_height + spacing_v)

                # 绘制结界项
                painter.drawRect(x, y, item_width, item_height)

                # 绘制索引
                painter.setPen(QPen(QColor("white"), 2))
                painter.setFont(QFont("Arial", 12, QFont.Bold))
                index = row * layout["columns"] + col
                text_rect = painter.fontMetrics().boundingRect(str(index))
                text_x = x + (item_width - text_rect.width()) // 2
                text_y = y + (item_height + text_rect.height()) // 2
                painter.drawText(text_x, text_y, str(index))
                painter.setPen(QPen(QColor("#e74c3c"), 2))

    def draw_status_info(self, painter, width, height):
        """绘制状态信息"""
        painter.setPen(QPen(QColor("#7f8c8d"), 1))
        painter.setFont(QFont("Arial", 9))

        # 显示当前配置信息
        grid_area = self.config["grid_area"]
        item_size = self.config["item_size"]

        info_lines = [
            f"网格区域: {grid_area['left']:.2f}, {grid_area['top']:.2f}, {grid_area['right']:.2f}, {grid_area['bottom']:.2f}",
            f"结界尺寸: {item_size['width']:.2f} × {item_size['height']:.2f}",
            f"缩放倍数: {self.zoom_factor:.1f}x"
        ]

        y_offset = 15
        for i, line in enumerate(info_lines):
            painter.drawText(10, height - len(info_lines) * 15 + i * 15, line)


def test_interactive_grid_config():
    """测试交互式网格配置对话框"""
    app = QApplication(sys.argv)

    dialog = InteractiveGridConfigDialog()
    if dialog.exec_() == QDialog.Accepted:
        config = dialog.get_config()
        print("用户配置:")
        print(json.dumps(config, indent=2, ensure_ascii=False))

    sys.exit()


if __name__ == "__main__":
    test_interactive_grid_config()
