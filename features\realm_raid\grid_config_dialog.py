#!/usr/bin/env python3
"""3×3网格配置对话框"""

import sys
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                             QLabel, QDoubleSpinBox, QSpinBox, QPushButton, 
                             QGroupBox, QFrame, QMessageBox, QApplication)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QPainter, QPen, QBrush, QColor
import json
import os

class GridConfigDialog(QDialog):
    """3×3网格配置对话框"""
    
    def __init__(self, parent=None, current_config=None):
        super().__init__(parent)
        self.current_config = current_config or self.get_default_config()
        self.setup_ui()
        self.load_config()
    
    def setup_ui(self):
        """设置界面"""
        self.setWindowTitle("3×3网格配置 - 交互式调整")
        self.setFixedSize(700, 650)  # 增大窗口以适应更大的预览区域
        
        layout = QVBoxLayout()
        
        # 标题
        title = QLabel("结界突破 3×3网格 自定义配置")
        title.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50; margin: 10px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 网格区域配置
        grid_area_group = self.create_grid_area_group()
        layout.addWidget(grid_area_group)
        
        # 网格布局配置
        grid_layout_group = self.create_grid_layout_group()
        layout.addWidget(grid_layout_group)
        
        # 结界项尺寸配置
        item_size_group = self.create_item_size_group()
        layout.addWidget(item_size_group)
        
        # 预览区域
        preview_group = self.create_preview_group()
        layout.addWidget(preview_group)
        
        # 按钮区域
        button_layout = self.create_button_layout()
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
    
    def create_grid_area_group(self):
        """创建网格区域配置组"""
        group = QGroupBox("网格区域 (相对坐标 0.0-1.0)")
        layout = QGridLayout()
        
        # 左边界
        layout.addWidget(QLabel("左边界:"), 0, 0)
        self.left_spin = QDoubleSpinBox()
        self.left_spin.setRange(0.0, 1.0)
        self.left_spin.setSingleStep(0.01)
        self.left_spin.setDecimals(2)
        self.left_spin.valueChanged.connect(self.update_preview)
        layout.addWidget(self.left_spin, 0, 1)
        
        # 上边界
        layout.addWidget(QLabel("上边界:"), 0, 2)
        self.top_spin = QDoubleSpinBox()
        self.top_spin.setRange(0.0, 1.0)
        self.top_spin.setSingleStep(0.01)
        self.top_spin.setDecimals(2)
        self.top_spin.valueChanged.connect(self.update_preview)
        layout.addWidget(self.top_spin, 0, 3)
        
        # 右边界
        layout.addWidget(QLabel("右边界:"), 1, 0)
        self.right_spin = QDoubleSpinBox()
        self.right_spin.setRange(0.0, 1.0)
        self.right_spin.setSingleStep(0.01)
        self.right_spin.setDecimals(2)
        self.right_spin.valueChanged.connect(self.update_preview)
        layout.addWidget(self.right_spin, 1, 1)
        
        # 下边界
        layout.addWidget(QLabel("下边界:"), 1, 2)
        self.bottom_spin = QDoubleSpinBox()
        self.bottom_spin.setRange(0.0, 1.0)
        self.bottom_spin.setSingleStep(0.01)
        self.bottom_spin.setDecimals(2)
        self.bottom_spin.valueChanged.connect(self.update_preview)
        layout.addWidget(self.bottom_spin, 1, 3)
        
        group.setLayout(layout)
        return group
    
    def create_grid_layout_group(self):
        """创建网格布局配置组"""
        group = QGroupBox("网格布局")
        layout = QGridLayout()
        
        # 行数
        layout.addWidget(QLabel("行数:"), 0, 0)
        self.rows_spin = QSpinBox()
        self.rows_spin.setRange(1, 5)
        self.rows_spin.valueChanged.connect(self.update_preview)
        layout.addWidget(self.rows_spin, 0, 1)
        
        # 列数
        layout.addWidget(QLabel("列数:"), 0, 2)
        self.columns_spin = QSpinBox()
        self.columns_spin.setRange(1, 5)
        self.columns_spin.valueChanged.connect(self.update_preview)
        layout.addWidget(self.columns_spin, 0, 3)
        
        # 水平间距
        layout.addWidget(QLabel("水平间距:"), 1, 0)
        self.h_spacing_spin = QDoubleSpinBox()
        self.h_spacing_spin.setRange(0.0, 0.1)
        self.h_spacing_spin.setSingleStep(0.005)
        self.h_spacing_spin.setDecimals(3)
        self.h_spacing_spin.valueChanged.connect(self.update_preview)
        layout.addWidget(self.h_spacing_spin, 1, 1)
        
        # 垂直间距
        layout.addWidget(QLabel("垂直间距:"), 1, 2)
        self.v_spacing_spin = QDoubleSpinBox()
        self.v_spacing_spin.setRange(0.0, 0.1)
        self.v_spacing_spin.setSingleStep(0.005)
        self.v_spacing_spin.setDecimals(3)
        self.v_spacing_spin.valueChanged.connect(self.update_preview)
        layout.addWidget(self.v_spacing_spin, 1, 3)
        
        group.setLayout(layout)
        return group
    
    def create_item_size_group(self):
        """创建结界项尺寸配置组"""
        group = QGroupBox("结界项尺寸")
        layout = QGridLayout()
        
        # 宽度
        layout.addWidget(QLabel("宽度:"), 0, 0)
        self.width_spin = QDoubleSpinBox()
        self.width_spin.setRange(0.05, 0.5)
        self.width_spin.setSingleStep(0.01)
        self.width_spin.setDecimals(2)
        self.width_spin.valueChanged.connect(self.update_preview)
        layout.addWidget(self.width_spin, 0, 1)
        
        # 高度
        layout.addWidget(QLabel("高度:"), 0, 2)
        self.height_spin = QDoubleSpinBox()
        self.height_spin.setRange(0.05, 0.5)
        self.height_spin.setSingleStep(0.01)
        self.height_spin.setDecimals(2)
        self.height_spin.valueChanged.connect(self.update_preview)
        layout.addWidget(self.height_spin, 0, 3)
        
        group.setLayout(layout)
        return group
    
    def create_preview_group(self):
        """创建预览组"""
        group = QGroupBox("交互式预览 - 可拖拽调整")
        layout = QVBoxLayout()

        self.preview_widget = GridPreviewWidget()
        self.preview_widget.setFixedSize(400, 300)  # 增大预览区域

        # 设置配置变更回调
        self.preview_widget.set_config_changed_callback(self.on_preview_config_changed)

        layout.addWidget(self.preview_widget)

        group.setLayout(layout)
        return group
    
    def create_button_layout(self):
        """创建按钮布局"""
        layout = QHBoxLayout()
        
        # 重置按钮
        reset_btn = QPushButton("重置默认")
        reset_btn.clicked.connect(self.reset_to_default)
        layout.addWidget(reset_btn)
        
        # 预设按钮
        preset_btn = QPushButton("常用预设")
        preset_btn.clicked.connect(self.show_presets)
        layout.addWidget(preset_btn)
        
        layout.addStretch()
        
        # 取消按钮
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        layout.addWidget(cancel_btn)
        
        # 确定按钮
        ok_btn = QPushButton("确定")
        ok_btn.clicked.connect(self.accept)
        ok_btn.setStyleSheet("QPushButton { background-color: #3498db; color: white; font-weight: bold; }")
        layout.addWidget(ok_btn)
        
        return layout
    
    def get_default_config(self):
        """获取默认配置"""
        return {
            "grid_area": {
                "left": 0.12,
                "top": 0.20,
                "right": 0.88,
                "bottom": 0.80
            },
            "layout": {
                "rows": 3,
                "columns": 3,
                "item_spacing": {
                    "horizontal": 0.02,
                    "vertical": 0.03
                }
            },
            "item_size": {
                "width": 0.22,
                "height": 0.15
            }
        }
    
    def load_config(self):
        """加载配置到界面"""
        config = self.current_config
        
        # 网格区域
        self.left_spin.setValue(config["grid_area"]["left"])
        self.top_spin.setValue(config["grid_area"]["top"])
        self.right_spin.setValue(config["grid_area"]["right"])
        self.bottom_spin.setValue(config["grid_area"]["bottom"])
        
        # 网格布局
        self.rows_spin.setValue(config["layout"]["rows"])
        self.columns_spin.setValue(config["layout"]["columns"])
        self.h_spacing_spin.setValue(config["layout"]["item_spacing"]["horizontal"])
        self.v_spacing_spin.setValue(config["layout"]["item_spacing"]["vertical"])
        
        # 结界项尺寸
        self.width_spin.setValue(config["item_size"]["width"])
        self.height_spin.setValue(config["item_size"]["height"])
        
        self.update_preview()
    
    def get_config(self):
        """获取当前配置"""
        return {
            "grid_area": {
                "left": self.left_spin.value(),
                "top": self.top_spin.value(),
                "right": self.right_spin.value(),
                "bottom": self.bottom_spin.value()
            },
            "layout": {
                "rows": self.rows_spin.value(),
                "columns": self.columns_spin.value(),
                "item_spacing": {
                    "horizontal": self.h_spacing_spin.value(),
                    "vertical": self.v_spacing_spin.value()
                }
            },
            "item_size": {
                "width": self.width_spin.value(),
                "height": self.height_spin.value()
            }
        }
    
    def update_preview(self):
        """更新预览"""
        config = self.get_config()
        self.preview_widget.update_config(config)

    def on_preview_config_changed(self, config):
        """预览组件配置变更回调"""
        # 更新界面控件值（不触发信号）
        self.update_controls_from_config(config)

    def update_controls_from_config(self, config):
        """从配置更新控件值（不触发信号）"""
        # 临时断开信号连接
        self.disconnect_signals()

        try:
            # 网格区域
            self.left_spin.setValue(config["grid_area"]["left"])
            self.top_spin.setValue(config["grid_area"]["top"])
            self.right_spin.setValue(config["grid_area"]["right"])
            self.bottom_spin.setValue(config["grid_area"]["bottom"])

            # 网格布局
            self.rows_spin.setValue(config["layout"]["rows"])
            self.columns_spin.setValue(config["layout"]["columns"])
            self.h_spacing_spin.setValue(config["layout"]["item_spacing"]["horizontal"])
            self.v_spacing_spin.setValue(config["layout"]["item_spacing"]["vertical"])

            # 结界项尺寸
            self.width_spin.setValue(config["item_size"]["width"])
            self.height_spin.setValue(config["item_size"]["height"])

        finally:
            # 重新连接信号
            self.connect_signals()

    def disconnect_signals(self):
        """断开信号连接"""
        self.left_spin.valueChanged.disconnect()
        self.top_spin.valueChanged.disconnect()
        self.right_spin.valueChanged.disconnect()
        self.bottom_spin.valueChanged.disconnect()
        self.rows_spin.valueChanged.disconnect()
        self.columns_spin.valueChanged.disconnect()
        self.h_spacing_spin.valueChanged.disconnect()
        self.v_spacing_spin.valueChanged.disconnect()
        self.width_spin.valueChanged.disconnect()
        self.height_spin.valueChanged.disconnect()

    def connect_signals(self):
        """连接信号"""
        self.left_spin.valueChanged.connect(self.update_preview)
        self.top_spin.valueChanged.connect(self.update_preview)
        self.right_spin.valueChanged.connect(self.update_preview)
        self.bottom_spin.valueChanged.connect(self.update_preview)
        self.rows_spin.valueChanged.connect(self.update_preview)
        self.columns_spin.valueChanged.connect(self.update_preview)
        self.h_spacing_spin.valueChanged.connect(self.update_preview)
        self.v_spacing_spin.valueChanged.connect(self.update_preview)
        self.width_spin.valueChanged.connect(self.update_preview)
        self.height_spin.valueChanged.connect(self.update_preview)
    
    def reset_to_default(self):
        """重置为默认配置"""
        self.current_config = self.get_default_config()
        self.load_config()
    
    def show_presets(self):
        """显示预设配置"""
        # 这里可以添加预设配置选择
        QMessageBox.information(self, "预设配置", "预设配置功能开发中...")

class GridPreviewWidget(QFrame):
    """交互式网格预览组件 - 支持鼠标拖动和缩放"""

    def __init__(self):
        super().__init__()
        self.config = None
        self.setStyleSheet("border: 1px solid #bdc3c7; background-color: #ecf0f1;")

        # 交互状态
        self.dragging = False
        self.drag_start_pos = None
        self.drag_target = None  # 'grid_area', 'item_size', 'spacing'
        self.drag_handle = None  # 'left', 'right', 'top', 'bottom', 'item', 'spacing_h', 'spacing_v'

        # 鼠标跟踪
        self.setMouseTracking(True)

        # 缩放参数
        self.zoom_factor = 1.0
        self.min_zoom = 0.5
        self.max_zoom = 3.0

        # 拖拽手柄大小
        self.handle_size = 8

        # 回调函数
        self.config_changed_callback = None

    def set_config_changed_callback(self, callback):
        """设置配置变更回调函数"""
        self.config_changed_callback = callback

    def update_config(self, config):
        """更新配置"""
        self.config = config
        self.update()

    def wheelEvent(self, event):
        """鼠标滚轮事件 - 缩放"""
        if self.config is None:
            return

        # 获取滚轮增量
        delta = event.angleDelta().y()
        zoom_in = delta > 0

        # 计算新的缩放因子
        zoom_step = 0.1
        if zoom_in:
            new_zoom = min(self.zoom_factor + zoom_step, self.max_zoom)
        else:
            new_zoom = max(self.zoom_factor - zoom_step, self.min_zoom)

        # 应用缩放到结界项尺寸
        if new_zoom != self.zoom_factor:
            scale_ratio = new_zoom / self.zoom_factor
            self.zoom_factor = new_zoom

            # 缩放结界项尺寸
            current_width = self.config["item_size"]["width"]
            current_height = self.config["item_size"]["height"]

            new_width = max(0.05, min(0.5, current_width * scale_ratio))
            new_height = max(0.05, min(0.5, current_height * scale_ratio))

            self.config["item_size"]["width"] = new_width
            self.config["item_size"]["height"] = new_height

            # 通知配置变更
            if self.config_changed_callback:
                self.config_changed_callback(self.config)

            self.update()

    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() != Qt.LeftButton or self.config is None:
            return

        pos = event.pos()
        self.drag_start_pos = pos

        # 检测点击的目标
        target, handle = self.get_drag_target(pos)
        if target:
            self.dragging = True
            self.drag_target = target
            self.drag_handle = handle
            self.setCursor(Qt.ClosedHandCursor)

    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if self.config is None:
            return

        pos = event.pos()

        if self.dragging and self.drag_start_pos:
            # 执行拖拽操作
            self.handle_drag(pos)
        else:
            # 更新鼠标光标
            target, handle = self.get_drag_target(pos)
            if target:
                if handle in ['left', 'right']:
                    self.setCursor(Qt.SizeHorCursor)
                elif handle in ['top', 'bottom']:
                    self.setCursor(Qt.SizeVerCursor)
                elif handle == 'item':
                    self.setCursor(Qt.SizeAllCursor)
                else:
                    self.setCursor(Qt.OpenHandCursor)
            else:
                self.setCursor(Qt.ArrowCursor)

    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton and self.dragging:
            self.dragging = False
            self.drag_start_pos = None
            self.drag_target = None
            self.drag_handle = None
            self.setCursor(Qt.ArrowCursor)

    def get_drag_target(self, pos):
        """获取拖拽目标"""
        if not self.config:
            return None, None

        width = self.width()
        height = self.height()

        # 网格区域坐标
        grid_area = self.config["grid_area"]
        grid_left = int(grid_area["left"] * width)
        grid_top = int(grid_area["top"] * height)
        grid_right = int(grid_area["right"] * width)
        grid_bottom = int(grid_area["bottom"] * height)

        # 检测网格区域边界手柄
        handle_size = self.handle_size

        # 左边界
        if abs(pos.x() - grid_left) <= handle_size and grid_top <= pos.y() <= grid_bottom:
            return 'grid_area', 'left'

        # 右边界
        if abs(pos.x() - grid_right) <= handle_size and grid_top <= pos.y() <= grid_bottom:
            return 'grid_area', 'right'

        # 上边界
        if abs(pos.y() - grid_top) <= handle_size and grid_left <= pos.x() <= grid_right:
            return 'grid_area', 'top'

        # 下边界
        if abs(pos.y() - grid_bottom) <= handle_size and grid_left <= pos.x() <= grid_right:
            return 'grid_area', 'bottom'

        # 检测结界项
        layout = self.config["layout"]
        item_size = self.config["item_size"]

        item_width = int(item_size["width"] * width)
        item_height = int(item_size["height"] * height)
        spacing_h = int(layout["item_spacing"]["horizontal"] * width)
        spacing_v = int(layout["item_spacing"]["vertical"] * height)

        for row in range(layout["rows"]):
            for col in range(layout["columns"]):
                x = grid_left + col * (item_width + spacing_h)
                y = grid_top + row * (item_height + spacing_v)

                # 检测结界项区域
                if x <= pos.x() <= x + item_width and y <= pos.y() <= y + item_height:
                    return 'item_size', 'item'

        return None, None

    def handle_drag(self, pos):
        """处理拖拽操作"""
        if not self.drag_start_pos or not self.config:
            return

        width = self.width()
        height = self.height()

        # 计算移动距离
        dx = pos.x() - self.drag_start_pos.x()
        dy = pos.y() - self.drag_start_pos.y()

        # 转换为相对坐标
        dx_rel = dx / width
        dy_rel = dy / height

        if self.drag_target == 'grid_area':
            self.handle_grid_area_drag(dx_rel, dy_rel)
        elif self.drag_target == 'item_size':
            self.handle_item_size_drag(dx_rel, dy_rel)

        # 更新起始位置
        self.drag_start_pos = pos

        # 通知配置变更
        if self.config_changed_callback:
            self.config_changed_callback(self.config)

        self.update()

    def handle_grid_area_drag(self, dx_rel, dy_rel):
        """处理网格区域拖拽"""
        grid_area = self.config["grid_area"]

        if self.drag_handle == 'left':
            new_left = max(0.0, min(grid_area["right"] - 0.1, grid_area["left"] + dx_rel))
            grid_area["left"] = new_left
        elif self.drag_handle == 'right':
            new_right = max(grid_area["left"] + 0.1, min(1.0, grid_area["right"] + dx_rel))
            grid_area["right"] = new_right
        elif self.drag_handle == 'top':
            new_top = max(0.0, min(grid_area["bottom"] - 0.1, grid_area["top"] + dy_rel))
            grid_area["top"] = new_top
        elif self.drag_handle == 'bottom':
            new_bottom = max(grid_area["top"] + 0.1, min(1.0, grid_area["bottom"] + dy_rel))
            grid_area["bottom"] = new_bottom

    def handle_item_size_drag(self, dx_rel, dy_rel):
        """处理结界项尺寸拖拽"""
        item_size = self.config["item_size"]

        # 根据拖拽方向调整尺寸
        if abs(dx_rel) > abs(dy_rel):
            # 主要是水平拖拽，调整宽度
            new_width = max(0.05, min(0.5, item_size["width"] + dx_rel))
            item_size["width"] = new_width
        else:
            # 主要是垂直拖拽，调整高度
            new_height = max(0.05, min(0.5, item_size["height"] + dy_rel))
            item_size["height"] = new_height

    def paintEvent(self, event):
        """绘制预览"""
        super().paintEvent(event)

        if not self.config:
            return

        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # 获取组件尺寸
        width = self.width()
        height = self.height()

        # 绘制网格区域
        grid_area = self.config["grid_area"]
        grid_left = int(grid_area["left"] * width)
        grid_top = int(grid_area["top"] * height)
        grid_right = int(grid_area["right"] * width)
        grid_bottom = int(grid_area["bottom"] * height)

        # 绘制网格区域边框
        painter.setPen(QPen(QColor("#3498db"), 2))
        painter.drawRect(grid_left, grid_top, grid_right - grid_left, grid_bottom - grid_top)

        # 绘制网格区域拖拽手柄
        self.draw_grid_handles(painter, grid_left, grid_top, grid_right, grid_bottom)

        # 绘制结界项
        layout = self.config["layout"]
        item_size = self.config["item_size"]

        item_width = int(item_size["width"] * width)
        item_height = int(item_size["height"] * height)
        spacing_h = int(layout["item_spacing"]["horizontal"] * width)
        spacing_v = int(layout["item_spacing"]["vertical"] * height)

        painter.setPen(QPen(QColor("#e74c3c"), 1))
        painter.setBrush(QBrush(QColor("#e74c3c"), Qt.SolidPattern))

        for row in range(layout["rows"]):
            for col in range(layout["columns"]):
                x = grid_left + col * (item_width + spacing_h)
                y = grid_top + row * (item_height + spacing_v)

                painter.drawRect(x, y, item_width, item_height)

                # 绘制索引
                painter.setPen(QPen(QColor("white"), 1))
                index = row * layout["columns"] + col
                painter.drawText(x + item_width//2 - 5, y + item_height//2 + 5, str(index))
                painter.setPen(QPen(QColor("#e74c3c"), 1))

        # 绘制操作提示
        self.draw_instructions(painter, width, height)

    def draw_grid_handles(self, painter, left, top, right, bottom):
        """绘制网格区域拖拽手柄"""
        handle_size = self.handle_size

        # 设置手柄样式
        painter.setPen(QPen(QColor("#2980b9"), 2))
        painter.setBrush(QBrush(QColor("#3498db"), Qt.SolidPattern))

        # 左边界手柄
        painter.drawRect(left - handle_size//2, (top + bottom)//2 - handle_size//2,
                        handle_size, handle_size)

        # 右边界手柄
        painter.drawRect(right - handle_size//2, (top + bottom)//2 - handle_size//2,
                        handle_size, handle_size)

        # 上边界手柄
        painter.drawRect((left + right)//2 - handle_size//2, top - handle_size//2,
                        handle_size, handle_size)

        # 下边界手柄
        painter.drawRect((left + right)//2 - handle_size//2, bottom - handle_size//2,
                        handle_size, handle_size)

    def draw_instructions(self, painter, width, height):
        """绘制操作说明"""
        painter.setPen(QPen(QColor("#7f8c8d"), 1))

        instructions = [
            "🖱️ 拖拽蓝色手柄调整网格区域",
            "🖱️ 拖拽红色结界项调整尺寸",
            "🔄 滚轮缩放结界项大小"
        ]

        y_offset = 15
        for i, instruction in enumerate(instructions):
            painter.drawText(10, height - len(instructions) * 15 + i * 15, instruction)

def test_grid_config_dialog():
    """测试网格配置对话框"""
    app = QApplication(sys.argv)
    
    dialog = GridConfigDialog()
    if dialog.exec_() == QDialog.Accepted:
        config = dialog.get_config()
        print("用户配置:")
        print(json.dumps(config, indent=2, ensure_ascii=False))
    
    sys.exit()

if __name__ == "__main__":
    test_grid_config_dialog()
