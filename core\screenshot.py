import cv2
import numpy as np
import win32gui
import win32ui
import win32con
import win32api
from PIL import ImageGrab, Image
import logging
from typing import Tuple, Optional
import time

class Screenshot:
    """屏幕截图类
    
    提供游戏窗口截图功能，支持全屏和指定区域截图
    """
    
    def __init__(self, hwnd=None):
        """初始化截图器
        
        Args:
            hwnd: 窗口句柄，如果为None则需要后续设置
        """
        self.hwnd = hwnd
        self.window_rect = None
        self.logger = logging.getLogger('阴阳师辅助工具.截图')
        
        # 客户区相对于窗口左上角的偏移
        self._client_offset = (0, 0)
        
        if hwnd:
            self.update_window_rect()
            self.update_client_offset()
            
    def set_hwnd(self, hwnd):
        """设置窗口句柄
        
        Args:
            hwnd: 窗口句柄
            
        Returns:
            bool: 是否设置成功
        """
        if hwnd and win32gui.IsWindow(hwnd):
            self.hwnd = hwnd
            self.update_window_rect()
            self.update_client_offset()
            return True
        return False
            
    def update_window_rect(self):
        """获取并更新窗口矩形区域"""
        if not self.hwnd:
            self.logger.warning("未设置窗口句柄，无法获取窗口区域")
            return
            
        try:
            self.window_rect = win32gui.GetWindowRect(self.hwnd)
            left, top, right, bottom = self.window_rect
            width = right - left
            height = bottom - top
            self.logger.debug(f"更新窗口区域: 位置[{left},{top}], 尺寸[{width}x{height}]")
        except Exception as e:
            self.logger.error(f"获取窗口区域失败: {str(e)}")
            self.window_rect = None
            
    def update_client_offset(self):
        """更新客户区相对于窗口的偏移量"""
        if not self.hwnd:
            self.logger.warning("未设置窗口句柄，无法获取客户区偏移")
            self._client_offset = (0, 0)
            return
        
        try:
            # 获取窗口矩形
            window_rect = win32gui.GetWindowRect(self.hwnd)
            window_left, window_top, _, _ = window_rect
            
            # 获取客户区左上角在屏幕上的坐标
            client_left, client_top = win32gui.ClientToScreen(self.hwnd, (0, 0))
            
            # 计算偏移
            offset_x = client_left - window_left
            offset_y = client_top - window_top
            
            self._client_offset = (offset_x, offset_y)
            self.logger.debug(f"客户区偏移: [{offset_x},{offset_y}]")
        except Exception as e:
            self.logger.error(f"获取客户区偏移失败: {str(e)}")
            self._client_offset = (0, 0)
            
    def capture(self) -> Optional[np.ndarray]:
        """捕获窗口截图
        
        Returns:
            np.ndarray: 截图图像数据，如果失败则返回None
        """
        if not self.hwnd:
            self.logger.error("未设置窗口句柄，无法截图")
            return None
            
        if not self.window_rect:
            self.update_window_rect()
            if not self.window_rect:
                return None
        
        try:
            left, top, right, bottom = self.window_rect
            width = right - left
            height = bottom - top
            
            self.logger.debug(f"截图窗口: 位置[{left},{top}], 尺寸[{width}x{height}]")
            
            # 使用PIL截图
            screenshot = ImageGrab.grab((left, top, right, bottom))
            
            # 转换为OpenCV格式
            screenshot = np.array(screenshot)
            screenshot = cv2.cvtColor(screenshot, cv2.COLOR_RGB2BGR)
            
            self.logger.debug(f"截图完成: 尺寸[{screenshot.shape[1]}x{screenshot.shape[0]}]")
            return screenshot
        except Exception as e:
            self.logger.error(f"截图失败: {str(e)}")
            return None
    
    def capture_region(self, region: Tuple[int, int, int, int]) -> Optional[np.ndarray]:
        """捕获窗口内指定区域的截图
        
        Args:
            region: 区域坐标 (left, top, right, bottom)，相对于窗口左上角
            
        Returns:
            np.ndarray: 区域截图数据，如果失败则返回None
        """
        if not self.hwnd:
            self.logger.error("未设置窗口句柄，无法截图")
            return None
            
        if not self.window_rect:
            self.update_window_rect()
            if not self.window_rect:
                return None
        
        try:
            win_left, win_top, win_right, win_bottom = self.window_rect
            
            # 计算绝对坐标
            left = win_left + region[0]
            top = win_top + region[1]
            right = win_left + region[2]
            bottom = win_top + region[3]
            
            # 确保坐标有效
            left = max(win_left, left)
            top = max(win_top, top)
            right = min(win_right, right)
            bottom = min(win_bottom, bottom)
            
            if left >= right or top >= bottom:
                self.logger.error("无效的区域坐标")
                return None
                
            self.logger.debug(f"截图区域: 位置[{left},{top}], 尺寸[{right-left}x{bottom-top}]")
            
            # 使用PIL截图
            screenshot = ImageGrab.grab((left, top, right, bottom))
            
            # 转换为OpenCV格式
            screenshot = np.array(screenshot)
            screenshot = cv2.cvtColor(screenshot, cv2.COLOR_RGB2BGR)
            
            self.logger.debug(f"区域截图完成: 尺寸[{screenshot.shape[1]}x{screenshot.shape[0]}]")
            return screenshot
        except Exception as e:
            self.logger.error(f"区域截图失败: {str(e)}")
            return None
            
    def capture_client_area(self) -> Optional[np.ndarray]:
        """获取窗口客户区域截图
        
        Returns:
            np.ndarray: 截图图像数据，如果失败则返回None
        """
        if not self.hwnd:
            self.logger.error("未设置窗口句柄，无法截图")
            return None
        
        try:
            # 获取窗口客户区域
            client_rect = win32gui.GetClientRect(self.hwnd)
            if not client_rect:
                self.logger.error("获取客户区域失败")
                return None
            
            # 获取客户区在屏幕上的位置
            client_left, client_top = win32gui.ClientToScreen(self.hwnd, (0, 0))
            client_right, client_bottom = win32gui.ClientToScreen(self.hwnd, (client_rect[2], client_rect[3]))
            
            # 计算客户区域尺寸
            width = client_right - client_left
            height = client_bottom - client_top
            
            self.logger.debug(f"客户区区域: 位置[{client_left},{client_top}], 尺寸[{width}x{height}]")
            
            # 方法1: 使用PIL ImageGrab截图（首选）
            try:
                self.logger.debug("使用PIL ImageGrab截取客户区")
                screenshot = ImageGrab.grab((client_left, client_top, client_right, client_bottom))
                
                # 转换为OpenCV格式
                screenshot = np.array(screenshot)
                screenshot = cv2.cvtColor(screenshot, cv2.COLOR_RGB2BGR)
                
                self.logger.debug(f"PIL客户区截图完成: 尺寸[{screenshot.shape[1]}x{screenshot.shape[0]}]")
                return screenshot
            except Exception as e:
                self.logger.warning(f"PIL截取客户区失败: {str(e)}，尝试使用BitBlt方法")
            
            # 方法2: 使用BitBlt截图（备选）
            try:
                # 创建设备上下文
                hwnd_dc = win32gui.GetWindowDC(self.hwnd)
                mfc_dc = win32ui.CreateDCFromHandle(hwnd_dc)
                save_dc = mfc_dc.CreateCompatibleDC()
                
                # 创建位图对象
                save_bitmap = win32ui.CreateBitmap()
                save_bitmap.CreateCompatibleBitmap(mfc_dc, width, height)
                save_dc.SelectObject(save_bitmap)
                
                # 最多重试3次
                for attempt in range(3):
                    try:
                        # 复制客户区域到内存DC
                        result = win32gui.BitBlt(
                            save_dc.GetSafeHdc(), 0, 0, width, height,
                            mfc_dc.GetSafeHdc(), 0, 0, win32con.SRCCOPY
                        )
                        
                        if result:
                            # 获取位图信息
                            bmp_info = save_bitmap.GetInfo()
                            bmp_str = save_bitmap.GetBitmapBits(True)
                            
                            # 转换为numpy数组
                            img = np.frombuffer(bmp_str, dtype='uint8')
                            img.shape = (height, width, 4)
                            img = cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)
                            
                            # 清理资源
                            win32gui.DeleteObject(save_bitmap.GetHandle())
                            save_dc.DeleteDC()
                            mfc_dc.DeleteDC()
                            win32gui.ReleaseDC(self.hwnd, hwnd_dc)
                            
                            self.logger.debug(f"BitBlt客户区截图完成: 尺寸[{img.shape[1]}x{img.shape[0]}]")
                            return img
                            
                    except Exception as e:
                        self.logger.warning(f"BitBlt操作失败(尝试{attempt+1}/3): {str(e)}")
                        time.sleep(0.1)  # 短暂等待后重试
                
                # 如果所有重试都失败，清理资源
                try:
                    win32gui.DeleteObject(save_bitmap.GetHandle())
                    save_dc.DeleteDC()
                    mfc_dc.DeleteDC()
                    win32gui.ReleaseDC(self.hwnd, hwnd_dc)
                except:
                    pass
                
                self.logger.warning("BitBlt截取客户区所有尝试均失败")
                return None
            except Exception as e:
                self.logger.warning(f"BitBlt方法初始化失败: {str(e)}")
                return None
        
        except Exception as e:
            self.logger.error(f"客户区域截图失败: {str(e)}")
            return None
            
    def capture_with_method(self, method: str = "auto") -> Optional[np.ndarray]:
        """使用指定方法捕获窗口截图
        
        Args:
            method: 截图方法，可选值为"auto"、"grab"、"dc"
                - auto: 自动选择最佳方法
                - grab: 使用PIL的ImageGrab
                - dc: 使用设备上下文
                
        Returns:
            np.ndarray: 截图图像数据，如果失败则返回None
        """
        if method == "grab" or method == "auto":
            return self.capture()
        elif method == "dc":
            return self.capture_client_area()
        else:
            self.logger.error(f"不支持的截图方法: {method}")
            return None

    @property
    def client_offset(self):
        """获取客户区相对于窗口的偏移量
        
        Returns:
            tuple: 客户区偏移 (offset_x, offset_y)
        """
        return self._client_offset 