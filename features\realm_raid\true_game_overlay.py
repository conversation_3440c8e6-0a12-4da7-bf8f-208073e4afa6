#!/usr/bin/env python3
"""真正的游戏窗口覆盖工具"""

import sys
import json
import logging
import win32gui
from PyQt5.QtWidgets import (QApplication, QWidget, QVBoxLayout, QHBoxLayout,
                             QLabel, QSlider, QPushButton, QGroupBox,
                             QGridLayout, QMessageBox, QSpinBox)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QPainter, QPen, QBrush, QColor, QFont

class TransparentOverlay(QWidget):
    """透明覆盖窗口"""
    
    def __init__(self, game_rect, config):
        super().__init__()
        self.game_rect = game_rect
        self.config = config
        self.setup_overlay()
    
    def setup_overlay(self):
        """设置覆盖窗口"""
        # 设置窗口属性 - 关键是这些设置
        self.setWindowFlags(
            Qt.WindowStaysOnTopHint |           # 始终置顶
            Qt.FramelessWindowHint |            # 无边框
            Qt.Tool |                           # 工具窗口
            Qt.WindowTransparentForInput        # 透明输入（鼠标穿透）
        )
        
        # 设置窗口透明背景
        self.setAttribute(Qt.WA_TranslucentBackground, True)
        self.setAttribute(Qt.WA_NoSystemBackground, True)
        
        # 设置窗口位置和大小，完全覆盖游戏窗口
        self.setGeometry(
            self.game_rect[0],      # x
            self.game_rect[1],      # y  
            self.game_rect[2] - self.game_rect[0],  # width
            self.game_rect[3] - self.game_rect[1]   # height
        )
        
        # 显示窗口
        self.show()
        self.raise_()  # 提升到最前面
        self.activateWindow()  # 激活窗口
    
    def update_config(self, config):
        """更新配置"""
        self.config = config
        self.update()  # 重新绘制
    
    def paintEvent(self, event):
        """绘制覆盖层"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 获取窗口尺寸
        width = self.width()
        height = self.height()
        
        # 绘制网格区域边框
        grid_area = self.config["grid_area"]
        grid_left = int(grid_area["left"] * width)
        grid_top = int(grid_area["top"] * height)
        grid_right = int(grid_area["right"] * width)
        grid_bottom = int(grid_area["bottom"] * height)
        
        # 黄色网格区域边框 - 加粗显示
        painter.setPen(QPen(QColor(255, 255, 0), 4))
        painter.drawRect(grid_left, grid_top, grid_right - grid_left, grid_bottom - grid_top)
        
        # 绘制网格区域标签
        painter.setPen(QPen(QColor(255, 255, 255), 2))
        painter.setFont(QFont("Arial", 14, QFont.Bold))
        painter.drawText(grid_left + 10, grid_top - 15, "网格区域")
        
        # 绘制结界项
        layout = self.config["layout"]
        item_size = self.config["item_size"]
        
        item_width = int(item_size["width"] * width)
        item_height = int(item_size["height"] * height)
        spacing_h = int(layout["item_spacing"]["horizontal"] * width)
        spacing_v = int(layout["item_spacing"]["vertical"] * height)
        
        for row in range(layout["rows"]):
            for col in range(layout["columns"]):
                x = grid_left + col * (item_width + spacing_h)
                y = grid_top + row * (item_height + spacing_v)
                
                # 绘制结界项边框（红色，半透明填充）
                painter.setPen(QPen(QColor(255, 0, 0), 3))
                painter.setBrush(QBrush(QColor(255, 0, 0, 80)))  # 半透明红色
                painter.drawRect(x, y, item_width, item_height)
                
                # 绘制中心点（绿色）
                center_x = x + item_width // 2
                center_y = y + item_height // 2
                painter.setPen(QPen(QColor(0, 255, 0), 3))
                painter.setBrush(QBrush(QColor(0, 255, 0)))
                painter.drawEllipse(center_x - 5, center_y - 5, 10, 10)
                
                # 绘制索引（白色，加粗）
                index = row * layout["columns"] + col
                painter.setPen(QPen(QColor(255, 255, 255), 2))
                painter.setFont(QFont("Arial", 16, QFont.Bold))
                painter.drawText(x + 15, y + 30, str(index))
        
        # 绘制说明文字（左上角）
        painter.setPen(QPen(QColor(255, 255, 255), 2))
        painter.setFont(QFont("Arial", 12, QFont.Bold))
        info_text = [
            "🟡 黄色边框: 网格区域",
            "🔴 红色矩形: 结界位置", 
            "🟢 绿色圆点: 中心点",
            "⚪ 白色数字: 索引编号",
            "",
            "按 ESC 键关闭覆盖层"
        ]
        
        # 绘制半透明背景
        painter.setPen(QPen(QColor(0, 0, 0), 1))
        painter.setBrush(QBrush(QColor(0, 0, 0, 150)))
        painter.drawRect(10, 10, 280, 140)
        
        # 绘制说明文字
        painter.setPen(QPen(QColor(255, 255, 255), 2))
        y_offset = 30
        for line in info_text:
            painter.drawText(20, y_offset, line)
            y_offset += 20
    
    def keyPressEvent(self, event):
        """按键事件"""
        if event.key() == Qt.Key_Escape:
            self.close()
        super().keyPressEvent(event)

class TrueGameOverlayController(QWidget):
    """真正的游戏覆盖控制器"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent
        self.game_window = None
        self.overlay_window = None
        self.config = self.get_default_config()
        self.setup_ui()

        # 定时器用于更新覆盖窗口位置
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_overlay_position)

        # 如果有父窗口，尝试自动获取游戏窗口
        if self.parent_window and hasattr(self.parent_window, 'get_current_hwnd'):
            self.auto_connect_from_parent()

    def auto_connect_from_parent(self):
        """从父窗口自动连接游戏窗口"""
        try:
            hwnd = self.parent_window.get_current_hwnd()
            if hwnd:
                # 通过句柄获取窗口信息
                rect = win32gui.GetWindowRect(hwnd)
                title = win32gui.GetWindowText(hwnd)

                self.game_window = {
                    'hwnd': hwnd,
                    'title': title,
                    'rect': rect,
                    'width': rect[2] - rect[0],
                    'height': rect[3] - rect[1]
                }

                self.status_label.setText(f"✅ 已连接: {title}")
                self.status_label.setStyleSheet("""
                    color: #155724;
                    font-weight: bold;
                    padding: 8px;
                    background-color: #d4edda;
                    border-radius: 6px;
                    border: 1px solid #c3e6cb;
                    font-size: 12px;
                """)
                self.show_overlay_btn.setEnabled(True)
                self.connect_btn.setText("🔄 重新连接")

                QMessageBox.information(self, "自动连接成功",
                    f"已自动连接到游戏窗口！\n\n"
                    f"窗口: {title}\n"
                    f"句柄: {hwnd}\n"
                    f"大小: {self.game_window['width']}×{self.game_window['height']}\n\n"
                    f"现在可以直接显示覆盖层！")

        except Exception as e:
            logging.warning(f"自动连接游戏窗口失败: {e}")
            # 失败时不显示错误，用户可以手动连接
        
    def setup_ui(self):
        """设置界面"""
        self.setWindowTitle("真正的游戏窗口覆盖")
        self.setFixedSize(450, 700)  # 增大窗口尺寸
        self.setWindowFlags(Qt.WindowStaysOnTopHint)

        # 确保控制界面完全不透明
        self.setAttribute(Qt.WA_TranslucentBackground, False)
        self.setAttribute(Qt.WA_NoSystemBackground, False)
        self.setWindowOpacity(1.0)  # 完全不透明

        # 设置控制界面的样式（不透明）
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                font-family: 'Microsoft YaHei', Arial, sans-serif;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #495057;
                background-color: white;
            }
            QPushButton {
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
                min-height: 20px;
            }
            QPushButton:hover {
                opacity: 0.8;
            }
            QPushButton:pressed {
                opacity: 0.6;
            }
            QLabel {
                color: #495057;
                padding: 2px;
            }
            QSlider::groove:horizontal {
                border: 1px solid #bbb;
                background: white;
                height: 10px;
                border-radius: 4px;
            }
            QSlider::sub-page:horizontal {
                background: #3498db;
                border: 1px solid #777;
                height: 10px;
                border-radius: 4px;
            }
            QSlider::add-page:horizontal {
                background: #fff;
                border: 1px solid #777;
                height: 10px;
                border-radius: 4px;
            }
            QSlider::handle:horizontal {
                background: #3498db;
                border: 1px solid #5c5c5c;
                width: 18px;
                margin: -2px 0;
                border-radius: 3px;
            }
            QSpinBox {
                border: 1px solid #ced4da;
                border-radius: 4px;
                padding: 4px;
                background-color: white;
                min-width: 60px;
            }
        """)
        
        layout = QVBoxLayout()
        
        # 标题
        title = QLabel("🎯 真正的游戏窗口覆盖")
        title.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin: 15px;
            padding: 10px;
            background-color: #ecf0f1;
            border-radius: 8px;
        """)
        title.setAlignment(Qt.AlignCenter)
        title.setWordWrap(True)
        layout.addWidget(title)

        # 说明
        info = QLabel("✨ 直接在游戏窗口上显示透明覆盖层\n🎮 可以透过覆盖层正常操作游戏")
        info.setStyleSheet("""
            color: #7f8c8d;
            margin: 5px;
            padding: 8px;
            background-color: #f8f9fa;
            border-radius: 6px;
            font-size: 12px;
        """)
        info.setAlignment(Qt.AlignCenter)
        info.setWordWrap(True)
        layout.addWidget(info)
        
        # 游戏窗口连接
        connection_group = self.create_connection_group()
        layout.addWidget(connection_group)
        
        # 覆盖控制
        overlay_group = self.create_overlay_group()
        layout.addWidget(overlay_group)
        
        # 网格配置
        config_group = self.create_config_group()
        layout.addWidget(config_group)
        
        # 操作按钮
        button_group = self.create_button_group()
        layout.addWidget(button_group)
        
        self.setLayout(layout)
        
    def create_connection_group(self):
        """创建连接组"""
        group = QGroupBox("游戏窗口")
        layout = QVBoxLayout()
        
        # 连接按钮
        self.connect_btn = QPushButton("🎮 连接游戏窗口")
        self.connect_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 12px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
                min-height: 25px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        self.connect_btn.clicked.connect(self.connect_game_window)
        layout.addWidget(self.connect_btn)

        # 状态显示
        self.status_label = QLabel("❌ 未连接游戏窗口")
        self.status_label.setStyleSheet("""
            color: #e74c3c;
            font-weight: bold;
            padding: 8px;
            background-color: #fdf2f2;
            border-radius: 6px;
            border: 1px solid #f5c6cb;
            font-size: 12px;
        """)
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setWordWrap(True)
        layout.addWidget(self.status_label)

        # 调试信息按钮
        debug_btn = QPushButton("🔍 调试信息")
        debug_btn.setStyleSheet("""
            background-color: #95a5a6;
            color: white;
            font-weight: bold;
            padding: 8px;
            border-radius: 6px;
            font-size: 12px;
        """)
        debug_btn.clicked.connect(self.show_debug_info)
        layout.addWidget(debug_btn)
        
        group.setLayout(layout)
        return group
        
    def create_overlay_group(self):
        """创建覆盖控制组"""
        group = QGroupBox("覆盖层控制")
        layout = QVBoxLayout()
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        self.show_overlay_btn = QPushButton("🟢 显示覆盖")
        self.show_overlay_btn.setEnabled(False)
        self.show_overlay_btn.setStyleSheet("""
            background-color: #27ae60;
            color: white;
            font-weight: bold;
            padding: 10px;
            border-radius: 6px;
            font-size: 13px;
            min-height: 20px;
        """)
        self.show_overlay_btn.clicked.connect(self.show_overlay)
        button_layout.addWidget(self.show_overlay_btn)

        self.hide_overlay_btn = QPushButton("🔴 隐藏覆盖")
        self.hide_overlay_btn.setEnabled(False)
        self.hide_overlay_btn.setStyleSheet("""
            background-color: #e74c3c;
            color: white;
            font-weight: bold;
            padding: 10px;
            border-radius: 6px;
            font-size: 13px;
            min-height: 20px;
        """)
        self.hide_overlay_btn.clicked.connect(self.hide_overlay)
        button_layout.addWidget(self.hide_overlay_btn)
        
        layout.addLayout(button_layout)
        
        # 状态显示
        self.overlay_status = QLabel("⚪ 覆盖层未显示")
        self.overlay_status.setStyleSheet("""
            color: #7f8c8d;
            padding: 8px;
            background-color: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #dee2e6;
            font-size: 12px;
        """)
        self.overlay_status.setAlignment(Qt.AlignCenter)
        self.overlay_status.setWordWrap(True)
        layout.addWidget(self.overlay_status)

        # 说明
        help_text = QLabel("💡 覆盖层将直接显示在游戏窗口上\n🎮 可以透过覆盖层操作游戏\n⌨️ 按 ESC 键快速关闭覆盖层")
        help_text.setStyleSheet("""
            color: #6c757d;
            font-size: 11px;
            padding: 8px;
            background-color: #e9ecef;
            border-radius: 6px;
            line-height: 1.4;
        """)
        help_text.setAlignment(Qt.AlignCenter)
        help_text.setWordWrap(True)
        layout.addWidget(help_text)
        
        group.setLayout(layout)
        return group
        
    def create_config_group(self):
        """创建配置组"""
        group = QGroupBox("网格配置 (实时调整)")
        layout = QVBoxLayout()
        
        # 网格区域
        area_group = QGroupBox("网格区域")
        area_layout = QGridLayout()
        
        # 左边界
        area_layout.addWidget(QLabel("左:"), 0, 0)
        self.left_slider = QSlider(Qt.Horizontal)
        self.left_slider.setRange(0, 50)
        self.left_slider.setValue(12)
        self.left_slider.valueChanged.connect(self.update_config)
        area_layout.addWidget(self.left_slider, 0, 1)
        self.left_label = QLabel("12%")
        area_layout.addWidget(self.left_label, 0, 2)
        
        # 上边界
        area_layout.addWidget(QLabel("上:"), 1, 0)
        self.top_slider = QSlider(Qt.Horizontal)
        self.top_slider.setRange(0, 50)
        self.top_slider.setValue(20)
        self.top_slider.valueChanged.connect(self.update_config)
        area_layout.addWidget(self.top_slider, 1, 1)
        self.top_label = QLabel("20%")
        area_layout.addWidget(self.top_label, 1, 2)
        
        # 右边界
        area_layout.addWidget(QLabel("右:"), 2, 0)
        self.right_slider = QSlider(Qt.Horizontal)
        self.right_slider.setRange(50, 100)
        self.right_slider.setValue(88)
        self.right_slider.valueChanged.connect(self.update_config)
        area_layout.addWidget(self.right_slider, 2, 1)
        self.right_label = QLabel("88%")
        area_layout.addWidget(self.right_label, 2, 2)
        
        # 下边界
        area_layout.addWidget(QLabel("下:"), 3, 0)
        self.bottom_slider = QSlider(Qt.Horizontal)
        self.bottom_slider.setRange(50, 100)
        self.bottom_slider.setValue(80)
        self.bottom_slider.valueChanged.connect(self.update_config)
        area_layout.addWidget(self.bottom_slider, 3, 1)
        self.bottom_label = QLabel("80%")
        area_layout.addWidget(self.bottom_label, 3, 2)
        
        area_group.setLayout(area_layout)
        layout.addWidget(area_group)
        
        # 网格布局
        grid_group = QGroupBox("网格布局")
        grid_layout = QGridLayout()
        
        grid_layout.addWidget(QLabel("行数:"), 0, 0)
        self.rows_spin = QSpinBox()
        self.rows_spin.setRange(1, 5)
        self.rows_spin.setValue(3)
        self.rows_spin.valueChanged.connect(self.update_config)
        grid_layout.addWidget(self.rows_spin, 0, 1)
        
        grid_layout.addWidget(QLabel("列数:"), 0, 2)
        self.columns_spin = QSpinBox()
        self.columns_spin.setRange(1, 5)
        self.columns_spin.setValue(3)
        self.columns_spin.valueChanged.connect(self.update_config)
        grid_layout.addWidget(self.columns_spin, 0, 3)
        
        grid_layout.addWidget(QLabel("水平间距:"), 1, 0)
        self.h_spacing_slider = QSlider(Qt.Horizontal)
        self.h_spacing_slider.setRange(0, 10)
        self.h_spacing_slider.setValue(2)
        self.h_spacing_slider.valueChanged.connect(self.update_config)
        grid_layout.addWidget(self.h_spacing_slider, 1, 1)
        self.h_spacing_label = QLabel("2%")
        grid_layout.addWidget(self.h_spacing_label, 1, 2)
        
        grid_layout.addWidget(QLabel("垂直间距:"), 2, 0)
        self.v_spacing_slider = QSlider(Qt.Horizontal)
        self.v_spacing_slider.setRange(0, 10)
        self.v_spacing_slider.setValue(3)
        self.v_spacing_slider.valueChanged.connect(self.update_config)
        grid_layout.addWidget(self.v_spacing_slider, 2, 1)
        self.v_spacing_label = QLabel("3%")
        grid_layout.addWidget(self.v_spacing_label, 2, 2)
        
        grid_group.setLayout(grid_layout)
        layout.addWidget(grid_group)
        
        # 结界项尺寸
        size_group = QGroupBox("结界项尺寸")
        size_layout = QGridLayout()
        
        size_layout.addWidget(QLabel("宽度:"), 0, 0)
        self.width_slider = QSlider(Qt.Horizontal)
        self.width_slider.setRange(10, 40)
        self.width_slider.setValue(22)
        self.width_slider.valueChanged.connect(self.update_config)
        size_layout.addWidget(self.width_slider, 0, 1)
        self.width_label = QLabel("22%")
        size_layout.addWidget(self.width_label, 0, 2)
        
        size_layout.addWidget(QLabel("高度:"), 1, 0)
        self.height_slider = QSlider(Qt.Horizontal)
        self.height_slider.setRange(10, 30)
        self.height_slider.setValue(15)
        self.height_slider.valueChanged.connect(self.update_config)
        size_layout.addWidget(self.height_slider, 1, 1)
        self.height_label = QLabel("15%")
        size_layout.addWidget(self.height_label, 1, 2)
        
        size_group.setLayout(size_layout)
        layout.addWidget(size_group)
        
        group.setLayout(layout)
        return group
        
    def create_button_group(self):
        """创建按钮组"""
        group = QGroupBox("操作")
        layout = QVBoxLayout()
        
        save_btn = QPushButton("💾 保存配置")
        save_btn.setStyleSheet("""
            background-color: #27ae60;
            color: white;
            font-weight: bold;
            padding: 12px;
            border-radius: 6px;
            font-size: 14px;
            min-height: 25px;
        """)
        save_btn.clicked.connect(self.save_config)
        layout.addWidget(save_btn)

        reset_btn = QPushButton("🔄 重置默认")
        reset_btn.setStyleSheet("""
            background-color: #6c757d;
            color: white;
            font-weight: bold;
            padding: 10px;
            border-radius: 6px;
            font-size: 13px;
            min-height: 20px;
        """)
        reset_btn.clicked.connect(self.reset_config)
        layout.addWidget(reset_btn)
        
        group.setLayout(layout)
        return group
        
    def get_default_config(self):
        """获取默认配置"""
        return {
            "grid_area": {
                "left": 0.12,
                "top": 0.20,
                "right": 0.88,
                "bottom": 0.80
            },
            "layout": {
                "rows": 3,
                "columns": 3,
                "item_spacing": {
                    "horizontal": 0.02,
                    "vertical": 0.03
                }
            },
            "item_size": {
                "width": 0.22,
                "height": 0.15
            }
        }
        
    def connect_game_window(self):
        """连接游戏窗口"""
        try:
            # 首先尝试从父窗口获取已选择的窗口句柄
            if self.parent_window and hasattr(self.parent_window, 'get_current_hwnd'):
                hwnd = self.parent_window.get_current_hwnd()
                if hwnd:
                    try:
                        # 通过句柄获取窗口信息
                        rect = win32gui.GetWindowRect(hwnd)
                        title = win32gui.GetWindowText(hwnd)

                        self.game_window = {
                            'hwnd': hwnd,
                            'title': title,
                            'rect': rect,
                            'width': rect[2] - rect[0],
                            'height': rect[3] - rect[1]
                        }

                        self.status_label.setText(f"✅ 已连接: {title}")
                        self.status_label.setStyleSheet("color: #27ae60; font-weight: bold; padding: 5px;")
                        self.show_overlay_btn.setEnabled(True)
                        self.connect_btn.setText("🔄 重新连接")

                        QMessageBox.information(self, "连接成功",
                            f"✅ 已连接到主界面选择的游戏窗口！\n\n"
                            f"窗口: {title}\n"
                            f"句柄: {hwnd}\n"
                            f"大小: {self.game_window['width']}×{self.game_window['height']}\n\n"
                            f"🎯 现在可以显示覆盖层，直接在游戏窗口上显示网格！")
                        return
                    except Exception as e:
                        logging.warning(f"使用主界面窗口句柄失败: {e}")

            # 如果没有父窗口句柄，则搜索游戏窗口
            game_windows = self.find_game_windows()

            if not game_windows:
                QMessageBox.warning(self, "警告",
                    "未找到游戏窗口！\n\n"
                    "请确保：\n"
                    "1. 阴阳师游戏正在运行\n"
                    "2. 或在主界面左侧选择游戏窗口")
                return

            self.game_window = game_windows[0]

            self.status_label.setText(f"✅ 已连接: {self.game_window['title']}")
            self.status_label.setStyleSheet("""
                color: #155724;
                font-weight: bold;
                padding: 8px;
                background-color: #d4edda;
                border-radius: 6px;
                border: 1px solid #c3e6cb;
                font-size: 12px;
            """)

            self.show_overlay_btn.setEnabled(True)
            self.connect_btn.setText("🔄 重新连接")

            QMessageBox.information(self, "连接成功",
                f"已连接游戏窗口！\n\n"
                f"窗口: {self.game_window['title']}\n"
                f"句柄: {self.game_window['hwnd']}\n"
                f"大小: {self.game_window['width']}×{self.game_window['height']}\n\n"
                f"现在可以显示覆盖层，直接在游戏窗口上显示网格！")

        except Exception as e:
            QMessageBox.critical(self, "连接失败", f"连接游戏窗口失败: {e}")
            
    def find_game_windows(self):
        """查找游戏窗口"""
        windows = []
        game_titles = ["阴阳师", "Onmyoji", "网易"]
        
        def enum_callback(hwnd, windows_list):
            if win32gui.IsWindowVisible(hwnd):
                title = win32gui.GetWindowText(hwnd)
                if title and any(game_title in title for game_title in game_titles):
                    rect = win32gui.GetWindowRect(hwnd)
                    windows_list.append({
                        'hwnd': hwnd,
                        'title': title,
                        'rect': rect,
                        'width': rect[2] - rect[0],
                        'height': rect[3] - rect[1]
                    })
            return True
            
        win32gui.EnumWindows(enum_callback, windows)
        return windows
        
    def show_overlay(self):
        """显示覆盖层"""
        if not self.game_window:
            QMessageBox.warning(self, "警告", "请先连接游戏窗口！")
            return
            
        try:
            # 创建透明覆盖窗口
            self.overlay_window = TransparentOverlay(self.game_window['rect'], self.config)
            
            # 启动定时器更新位置
            self.update_timer.start(1000)  # 每秒检查一次窗口位置
            
            self.show_overlay_btn.setEnabled(False)
            self.hide_overlay_btn.setEnabled(True)
            self.overlay_status.setText("✅ 覆盖层正在显示")
            self.overlay_status.setStyleSheet("""
                color: #155724;
                padding: 8px;
                background-color: #d4edda;
                border-radius: 6px;
                border: 1px solid #c3e6cb;
                font-size: 12px;
            """)
            
            QMessageBox.information(self, "覆盖显示", 
                "覆盖层已显示在游戏窗口上！\n\n"
                "🎯 现在您可以直接在游戏窗口上看到网格！\n"
                "🎮 可以透过覆盖层正常操作游戏\n"
                "🔧 拖拽滑块可以实时调整网格\n"
                "⌨️ 按 ESC 键可以快速关闭覆盖层")
                
        except Exception as e:
            QMessageBox.critical(self, "显示失败", f"显示覆盖层失败: {e}")
            
    def hide_overlay(self):
        """隐藏覆盖层"""
        try:
            if self.overlay_window:
                self.overlay_window.close()
                self.overlay_window = None
                
            self.update_timer.stop()
            
            self.show_overlay_btn.setEnabled(True)
            self.hide_overlay_btn.setEnabled(False)
            self.overlay_status.setText("⚪ 覆盖层已隐藏")
            self.overlay_status.setStyleSheet("""
                color: #7f8c8d;
                padding: 8px;
                background-color: #f8f9fa;
                border-radius: 6px;
                border: 1px solid #dee2e6;
                font-size: 12px;
            """)
            
        except Exception as e:
            QMessageBox.critical(self, "隐藏失败", f"隐藏覆盖层失败: {e}")
            
    def update_overlay_position(self):
        """更新覆盖层位置"""
        if self.overlay_window and self.game_window:
            try:
                # 获取当前游戏窗口位置
                current_rect = win32gui.GetWindowRect(self.game_window['hwnd'])
                
                # 如果窗口位置改变，更新覆盖层位置
                if current_rect != self.game_window['rect']:
                    self.game_window['rect'] = current_rect
                    self.overlay_window.setGeometry(
                        current_rect[0],
                        current_rect[1],
                        current_rect[2] - current_rect[0],
                        current_rect[3] - current_rect[1]
                    )
            except:
                pass
                
    def update_config(self):
        """更新配置"""
        self.config = {
            "grid_area": {
                "left": self.left_slider.value() / 100.0,
                "top": self.top_slider.value() / 100.0,
                "right": self.right_slider.value() / 100.0,
                "bottom": self.bottom_slider.value() / 100.0
            },
            "layout": {
                "rows": self.rows_spin.value(),
                "columns": self.columns_spin.value(),
                "item_spacing": {
                    "horizontal": self.h_spacing_slider.value() / 100.0,
                    "vertical": self.v_spacing_slider.value() / 100.0
                }
            },
            "item_size": {
                "width": self.width_slider.value() / 100.0,
                "height": self.height_slider.value() / 100.0
            }
        }
        
        # 更新标签
        self.left_label.setText(f"{self.left_slider.value()}%")
        self.top_label.setText(f"{self.top_slider.value()}%")
        self.right_label.setText(f"{self.right_slider.value()}%")
        self.bottom_label.setText(f"{self.bottom_slider.value()}%")
        self.h_spacing_label.setText(f"{self.h_spacing_slider.value()}%")
        self.v_spacing_label.setText(f"{self.v_spacing_slider.value()}%")
        self.width_label.setText(f"{self.width_slider.value()}%")
        self.height_label.setText(f"{self.height_slider.value()}%")
        
        # 更新覆盖层
        if self.overlay_window:
            self.overlay_window.update_config(self.config)
        
    def save_config(self):
        """保存配置"""
        try:
            with open("custom_grid_config.json", 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
                
            QMessageBox.information(self, "保存成功", 
                "网格配置已保存成功！\n\n"
                "文件: custom_grid_config.json\n"
                "重启程序后自动生效。")
        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"保存配置失败: {e}")
            
    def reset_config(self):
        """重置配置"""
        self.config = self.get_default_config()
        self.load_config_to_ui()
        self.update_config()
        
    def load_config_to_ui(self):
        """加载配置到界面"""
        config = self.config
        
        self.left_slider.setValue(int(config["grid_area"]["left"] * 100))
        self.top_slider.setValue(int(config["grid_area"]["top"] * 100))
        self.right_slider.setValue(int(config["grid_area"]["right"] * 100))
        self.bottom_slider.setValue(int(config["grid_area"]["bottom"] * 100))
        
        self.rows_spin.setValue(config["layout"]["rows"])
        self.columns_spin.setValue(config["layout"]["columns"])
        self.h_spacing_slider.setValue(int(config["layout"]["item_spacing"]["horizontal"] * 100))
        self.v_spacing_slider.setValue(int(config["layout"]["item_spacing"]["vertical"] * 100))
        
        self.width_slider.setValue(int(config["item_size"]["width"] * 100))
        self.height_slider.setValue(int(config["item_size"]["height"] * 100))
        
    def show_debug_info(self):
        """显示调试信息"""
        debug_info = []

        # 父窗口信息
        if self.parent_window:
            debug_info.append("✅ 父窗口已连接")
            if hasattr(self.parent_window, 'get_current_hwnd'):
                hwnd = self.parent_window.get_current_hwnd()
                debug_info.append(f"📱 父窗口句柄: {hwnd}")
            else:
                debug_info.append("❌ 父窗口无get_current_hwnd方法")
        else:
            debug_info.append("❌ 无父窗口连接")

        # 游戏窗口信息
        if self.game_window:
            debug_info.append("✅ 游戏窗口已连接")
            debug_info.append(f"📱 窗口句柄: {self.game_window['hwnd']}")
            debug_info.append(f"🏷️ 窗口标题: {self.game_window['title']}")
            debug_info.append(f"📐 窗口大小: {self.game_window['width']}×{self.game_window['height']}")
            debug_info.append(f"📍 窗口位置: {self.game_window['rect']}")
        else:
            debug_info.append("❌ 游戏窗口未连接")

        # 覆盖层信息
        if self.overlay_window:
            debug_info.append("✅ 覆盖层正在显示")
            debug_info.append(f"📐 覆盖层大小: {self.overlay_window.width()}×{self.overlay_window.height()}")
            debug_info.append(f"📍 覆盖层位置: ({self.overlay_window.x()}, {self.overlay_window.y()})")
        else:
            debug_info.append("❌ 覆盖层未显示")

        # 搜索可用游戏窗口
        try:
            available_windows = self.find_game_windows()
            debug_info.append(f"\n🔍 搜索到 {len(available_windows)} 个游戏窗口:")
            for i, window in enumerate(available_windows):
                debug_info.append(f"  {i+1}. {window['title']} (句柄: {window['hwnd']})")
        except Exception as e:
            debug_info.append(f"❌ 搜索游戏窗口失败: {e}")

        QMessageBox.information(self, "调试信息", "\n".join(debug_info))

    def closeEvent(self, event):
        """关闭事件"""
        if self.overlay_window:
            self.hide_overlay()
        event.accept()

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    controller = TrueGameOverlayController()
    controller.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
