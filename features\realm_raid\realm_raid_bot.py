"""结界突破自动化机器人"""

import os
import time
import random
import logging
import math
import numpy as np
from typing import Tuple, List, Dict, Optional, Any
from core.base_bot import BaseBot
from core.image_utils import ImageUtils
from core.config_manager import config_manager
from core.ocr_utils import OCRUtils
from core.logging_config import get_session_log_file, create_session_logger
from features.realm_raid.realm_raid_config import (
    REALM_RAID_TEMPLATES,
    DEFAULT_REALM_RAID_SETTINGS,
    REALM_GRID_CONFIG,
    RAID_TICKET_CONFIG,
    SELECTION_STRATEGIES,
    BATTLE_RESULT_ACTIONS,
    ERROR_HANDLING
)

class RealmGridLocator:
    """3x3网格定位器"""

    def __init__(self, grid_config, screen_resolution):
        self.config = grid_config
        self.screen_width, self.screen_height = screen_resolution
        self.grid_positions = self._calculate_grid_positions()

    def _calculate_grid_positions(self):
        """计算3x3网格的所有位置"""
        positions = {}

        grid_area = self.config["grid_area"]
        layout = self.config["layout"]
        item_size = self.config["item_size"]

        # 计算网格区域像素坐标
        grid_left = int(grid_area["left"] * self.screen_width)
        grid_top = int(grid_area["top"] * self.screen_height)

        # 计算单个结界项像素尺寸
        item_width = int(item_size["width"] * self.screen_width)
        item_height = int(item_size["height"] * self.screen_height)

        # 计算间距
        spacing_h = int(layout["item_spacing"]["horizontal"] * self.screen_width)
        spacing_v = int(layout["item_spacing"]["vertical"] * self.screen_height)

        # 生成3x3网格坐标
        for row in range(layout["rows"]):
            for col in range(layout["columns"]):
                # 计算当前格子的位置
                x = grid_left + col * (item_width + spacing_h)
                y = grid_top + row * (item_height + spacing_v)

                # 计算网格索引（0-8）
                grid_index = row * layout["columns"] + col

                # 存储格子信息
                grid_key = f"r{row}c{col}"
                positions[grid_key] = {
                    "index": grid_index,    # 0-8的索引
                    "row": row,             # 0-2
                    "col": col,             # 0-2
                    "rect": {
                        "left": x,
                        "top": y,
                        "right": x + item_width,
                        "bottom": y + item_height
                    },
                    "center": {
                        "x": x + item_width // 2,
                        "y": y + item_height // 2
                    },
                    "click_area": {
                        # 可点击区域（稍微缩小避免误点）
                        "left": x + 8,
                        "top": y + 8,
                        "right": x + item_width - 8,
                        "bottom": y + item_height - 8
                    }
                }

        return positions

    def get_realm_by_index(self, index):
        """根据索引（0-8）获取结界位置"""
        for position in self.grid_positions.values():
            if position["index"] == index:
                return position
        return None

    def get_all_positions_ordered(self):
        """获取按索引排序的所有位置"""
        positions = list(self.grid_positions.values())
        return sorted(positions, key=lambda x: x["index"])

    def get_click_position(self, row, col=None):
        """获取指定结界的点击坐标"""
        if col is None:
            # 如果只提供了一个参数，当作索引处理
            position = self.get_realm_by_index(row)
        else:
            grid_key = f"r{row}c{col}"
            position = self.grid_positions.get(grid_key)

        if position:
            return position["center"]["x"], position["center"]["y"]
        return None


class RaidTicketDetector:
    """突破券检测器"""

    def __init__(self, bot, ticket_config):
        self.bot = bot
        self.config = ticket_config
        self.last_ticket_count = None
        self.last_check_time = 0

    def get_current_ticket_count(self):
        """获取当前突破券数量"""
        try:
            # 避免频繁检测，缓存结果
            current_time = time.time()
            if (current_time - self.last_check_time < 5 and
                self.last_ticket_count is not None):
                logging.debug(f"使用缓存的突破券数量: {self.last_ticket_count}")
                return self.last_ticket_count

            logging.debug("检测突破券数量...")

            # 截取突破券显示区域
            ticket_area = self.config["ticket_area"]
            window_rect = self.bot.get_window_rect()
            if not window_rect:
                logging.error("无法获取窗口尺寸")
                return None

            ticket_image = self.bot.take_screenshot_area(
                int(ticket_area["left"] * window_rect[2]),
                int(ticket_area["top"] * window_rect[3]),
                int((ticket_area["right"] - ticket_area["left"]) * window_rect[2]),
                int((ticket_area["bottom"] - ticket_area["top"]) * window_rect[3])
            )

            # 使用OCR识别数字
            ticket_count = self.extract_ticket_number(ticket_image)

            # 更新缓存
            self.last_ticket_count = ticket_count
            self.last_check_time = current_time

            if ticket_count is not None:
                logging.info(f"🎫 当前突破券数量: {ticket_count}")
            else:
                logging.warning("⚠️ 无法识别突破券数量")

            return ticket_count

        except Exception as e:
            logging.error(f"❌ 检测突破券数量失败: {e}")
            return None

    def extract_ticket_number(self, ticket_image):
        """从图像中提取突破券数字"""
        try:
            # 图像预处理（如果需要）
            if self.config["ocr_config"]["preprocessing"]:
                ticket_image = self.preprocess_ticket_image(ticket_image)

            # OCR识别
            ocr_result = self.bot.ocr_utils.extract_text(ticket_image)

            # 提取数字
            if self.config["ocr_config"]["number_only"]:
                # 只保留数字字符
                numbers = ''.join(filter(str.isdigit, ocr_result))
                if numbers:
                    return int(numbers)
            else:
                # 尝试从文本中提取数字
                import re
                numbers = re.findall(r'\d+', ocr_result)
                if numbers:
                    return int(numbers[0])

            return None

        except Exception as e:
            logging.error(f"提取突破券数字失败: {e}")
            return None

    def has_sufficient_tickets(self, required_count=1):
        """检查是否有足够的突破券"""
        current_count = self.get_current_ticket_count()
        if current_count is None:
            logging.warning("⚠️ 无法检测突破券数量，假设有足够的券")
            return True

        sufficient = current_count >= required_count
        if sufficient:
            logging.debug(f"✅ 突破券充足: {current_count}/{required_count}")
        else:
            logging.warning(f"🎫 突破券不足: {current_count}/{required_count}")

        return sufficient


class RealmStateDetector:
    """结界状态检测器"""

    def __init__(self, grid_locator, bot):
        self.grid_locator = grid_locator
        self.bot = bot

    def scan_all_realms(self):
        """扫描所有网格位置的结界状态"""
        logging.info("🔍 开始扫描3x3网格中的结界...")
        realm_states = []

        for i, position in enumerate(self.grid_locator.get_all_positions_ordered()):
            logging.debug(f"扫描网格位置 {i} (第{position['row']}行第{position['col']}列)")
            realm_info = self.detect_realm_at_position(position)
            if realm_info:
                status = "冷却中" if realm_info['in_cooldown'] else "可用"
                level_info = f"等级{realm_info['level']}" if realm_info['level'] else "未知等级"
                logging.debug(f"  └─ 发现结界: {status}, {level_info}")
                realm_states.append(realm_info)
            else:
                logging.debug(f"  └─ 位置{i}: 无结界")

        logging.info(f"📊 扫描完成，发现 {len(realm_states)} 个结界")
        return realm_states

    def detect_realm_at_position(self, position):
        """检测指定位置的结界状态"""
        rect = position["rect"]

        # 截取结界区域的图像
        try:
            realm_image = self.bot.take_screenshot_area(
                rect["left"], rect["top"],
                rect["right"] - rect["left"],
                rect["bottom"] - rect["top"]
            )
        except Exception as e:
            logging.error(f"截取结界区域失败: {e}")
            return None

        # 分析结界状态
        realm_info = {
            "position": position,
            "row": position["row"],
            "col": position["col"],
            "index": position["index"],
            "available": False,
            "in_cooldown": False,
            "level": None,
            "owner_name": None
        }

        # 检查是否有结界存在
        if self.is_realm_present(realm_image):
            realm_info["available"] = True

            # 检查冷却状态
            realm_info["in_cooldown"] = self.detect_cooldown(realm_image)

            # 如果不在冷却中，进一步分析
            if not realm_info["in_cooldown"]:
                realm_info["level"] = self.detect_realm_level(realm_image)
                realm_info["owner_name"] = self.detect_owner_name(realm_image)

        return realm_info if realm_info["available"] else None

    def is_realm_present(self, realm_image):
        """检查该位置是否有结界"""
        try:
            # 检查图像是否不是纯色（表示有内容）
            img_array = np.array(realm_image)
            if len(img_array.shape) == 3:
                # 计算颜色方差，如果方差很小说明是纯色背景
                variance = np.var(img_array)
                return variance > 100  # 阈值可调整
            return True
        except:
            return True  # 默认认为有结界

    def detect_cooldown(self, realm_image):
        """检测结界是否在冷却中"""
        try:
            # 简单的灰度检测
            img_array = np.array(realm_image)
            if len(img_array.shape) == 3:
                gray = np.mean(img_array, axis=2)
                # 如果整体偏灰，可能在冷却中
                avg_brightness = np.mean(gray)
                return avg_brightness < 80  # 阈值可调整
            return False
        except:
            return False

    def detect_realm_level(self, realm_image):
        """检测结界等级"""
        try:
            level_text = self.bot.ocr_utils.extract_text(realm_image)
            # 提取数字
            import re
            numbers = re.findall(r'\d+', level_text)
            if numbers:
                level = int(numbers[0])
                return level if 1 <= level <= 6 else None
        except:
            pass
        return None

    def detect_owner_name(self, realm_image):
        """检测结界主人名称"""
        try:
            name_text = self.bot.ocr_utils.extract_text(realm_image)
            return name_text.strip() if name_text else None
        except:
            return None


class GridBasedRealmSelector:
    """基于3x3网格的结界选择器"""

    def __init__(self, grid_locator, state_detector, ticket_detector):
        self.grid_locator = grid_locator
        self.state_detector = state_detector
        self.ticket_detector = ticket_detector
        self.selection_history = []

    def select_target_realm(self, strategy="smart_grid"):
        """基于3x3网格的结界选择"""
        logging.info(f"🎯 开始选择目标结界，策略: {strategy}")

        # 首先检查突破券
        logging.debug("检查突破券数量...")
        if not self.ticket_detector.has_sufficient_tickets():
            logging.warning("🎫 突破券不足，无法进行突破")
            return None

        # 扫描所有9个网格位置
        all_realms = self.state_detector.scan_all_realms()

        # 过滤可用结界
        available_realms = [
            realm for realm in all_realms
            if realm["available"] and not realm["in_cooldown"]
        ]

        logging.info(f"📋 过滤结果: {len(all_realms)}个结界中有{len(available_realms)}个可用")

        if not available_realms:
            logging.warning("⚠️ 没有可用的结界")
            return None

        # 显示可用结界信息
        for i, realm in enumerate(available_realms):
            level_info = f"等级{realm['level']}" if realm['level'] else "未知等级"
            logging.debug(f"  可用结界{i+1}: 索引{realm['index']}, {level_info}")

        # 根据策略选择
        selected = self.apply_grid_strategy(available_realms, strategy)
        if selected:
            level_info = f"等级{selected['level']}" if selected['level'] else "未知等级"
            logging.info(f"✅ 已选择目标: 索引{selected['index']} (第{selected['row']}行第{selected['col']}列), {level_info}")

        return selected

    def apply_grid_strategy(self, available_realms, strategy):
        """应用3x3网格选择策略"""
        logging.debug(f"应用选择策略: {strategy}")

        if strategy == "random":
            logging.debug("使用完全随机策略")
            return random.choice(available_realms)
        elif strategy == "top_left_priority":
            # 优先选择左上角（索引小的）
            logging.debug("使用左上角优先策略")
            return min(available_realms, key=lambda x: x["index"])
        elif strategy == "center_out":
            # 从中心向外选择（索引4是中心）
            logging.debug("使用中心向外策略")
            center_index = 4
            return min(available_realms,
                      key=lambda x: abs(x["index"] - center_index))
        elif strategy == "corners_first":
            # 优先选择四个角（索引0,2,6,8）
            logging.debug("使用角落优先策略")
            corner_indices = {0, 2, 6, 8}
            corner_realms = [r for r in available_realms
                           if r["index"] in corner_indices]
            if corner_realms:
                logging.debug(f"找到{len(corner_realms)}个角落结界")
                return random.choice(corner_realms)
            else:
                logging.debug("无角落结界，使用随机选择")
                return random.choice(available_realms)
        elif strategy == "smart_grid":
            # 智能3x3网格策略
            logging.debug("使用智能网格策略")
            return self.smart_3x3_selection(available_realms)
        else:
            # 默认随机选择
            logging.debug(f"未知策略{strategy}，使用随机选择")
            return random.choice(available_realms)

    def smart_3x3_selection(self, available_realms):
        """智能3x3网格选择算法"""
        logging.debug("执行智能网格选择算法...")

        # 按优先级分组
        priority_groups = {
            "high": [],    # 高优先级：角落位置
            "medium": [],  # 中优先级：边缘位置
            "low": []      # 低优先级：中心位置
        }

        for realm in available_realms:
            index = realm["index"]

            if index in {0, 2, 6, 8}:  # 四个角
                priority_groups["high"].append(realm)
            elif index in {1, 3, 5, 7}:  # 四条边的中点
                priority_groups["medium"].append(realm)
            else:  # 中心位置（索引4）
                priority_groups["low"].append(realm)

        # 显示分组情况
        logging.debug(f"优先级分组: 高{len(priority_groups['high'])}个, "
                     f"中{len(priority_groups['medium'])}个, "
                     f"低{len(priority_groups['low'])}个")

        # 选择策略：60%高优先级，30%中优先级，10%低优先级
        rand = random.random()

        if rand < 0.6 and priority_groups["high"]:
            logging.debug("选择高优先级结界(角落)")
            selected = random.choice(priority_groups["high"])
        elif rand < 0.9 and priority_groups["medium"]:
            logging.debug("选择中优先级结界(边缘)")
            selected = random.choice(priority_groups["medium"])
        elif priority_groups["low"]:
            logging.debug("选择低优先级结界(中心)")
            selected = random.choice(priority_groups["low"])
        else:
            # 如果没有对应优先级的选项，随机选择
            logging.debug("无对应优先级选项，随机选择")
            selected = random.choice(available_realms)

        # 记录选择历史
        self.selection_history.append(selected)
        if len(self.selection_history) > 15:
            self.selection_history.pop(0)

        logging.debug(f"智能选择结果: 索引{selected['index']}")
        return selected


class RealmRaidBot(BaseBot):
    """阴阳师结界突破自动化类

    自动进行结界突破，包括：
    - 导航到结界突破界面
    - 智能选择目标结界
    - 自动战斗
    - 处理战斗结果
    - 循环执行
    """

    def __init__(self, hwnd=None, base_resolution: Tuple[int, int] = (1920, 1080)):
        """初始化结界突破机器人

        Args:
            hwnd: 游戏窗口句柄
            base_resolution: 基准分辨率，默认为1920x1080
        """
        # 如果提供了窗口句柄，直接使用
        if hwnd:
            logging.debug(f"使用窗口句柄: {hwnd}")

        super().__init__(hwnd, base_resolution)

        # 从配置中获取操作模式设置
        operation_mode = config_manager.get("operation_mode", "background")
        self.enable_background_mode(operation_mode == "background")
        logging.info(f"结界突破机器人已设置为{'后台' if operation_mode == 'background' else '前台'}模式")

        # 加载配置
        self.settings = DEFAULT_REALM_RAID_SETTINGS.copy()
        self.load_user_settings()

        # 初始化OCR工具
        self.ocr_utils = OCRUtils()

        # 初始化组件
        window_rect = self.get_window_rect()
        if window_rect:
            screen_resolution = (window_rect[2], window_rect[3])
        else:
            screen_resolution = base_resolution

        self.grid_locator = RealmGridLocator(REALM_GRID_CONFIG, screen_resolution)
        self.ticket_detector = RaidTicketDetector(self, RAID_TICKET_CONFIG)
        self.state_detector = RealmStateDetector(self.grid_locator, self)
        self.realm_selector = GridBasedRealmSelector(
            self.grid_locator, self.state_detector, self.ticket_detector
        )

        # 状态变量
        self.current_raids = 0
        self.consecutive_errors = 0
        self.last_break_time = time.time()
        self.available_realms = []
        self.current_target = None

        # 统计信息
        self.stats = {
            'total_raids': 0,
            'victories': 0,
            'defeats': 0,
            'errors': 0,
            'start_time': time.time()
        }

        # 加载模板
        self.load_templates()

        # 创建会话日志
        session_log_file = get_session_log_file(log_dir="logs", prefix="realm_raid")
        self.session_logger = create_session_logger(session_log_file, "realm_raid_session")

        logging.info("结界突破机器人初始化完成")
        self.session_logger.info("=== 新的结界突破会话开始 ===")

    def load_user_settings(self):
        """加载用户自定义设置"""
        try:
            user_settings = config_manager.get("realm_raid_settings", {})
            self.settings.update(user_settings)
            logging.info(f"已加载用户设置: {len(user_settings)} 项配置")
        except Exception as e:
            logging.warning(f"加载用户设置失败: {e}")

    def load_templates(self):
        """加载所有模板"""
        try:
            for name, template_config in REALM_RAID_TEMPLATES.items():
                if os.path.exists(template_config["path"]):
                    self.load_template(
                        name,
                        template_config["path"],
                        template_config.get("rel_click_point", (0.5, 0.5)),
                        template_config.get("threshold", 0.8)
                    )
                    logging.debug(f"已加载模板: {name}")
                else:
                    logging.warning(f"模板文件不存在: {template_config['path']}")
        except Exception as e:
            logging.error(f"加载模板失败: {e}")

    def start_realm_raid(self) -> bool:
        """开始结界突破主流程"""
        try:
            logging.info("开始结界突破流程")
            self.running = True
            self.stats['start_time'] = time.time()

            # 导航到结界突破界面
            if not self.navigate_to_realm_raid():
                logging.error("无法导航到结界突破界面")
                return False

            # 主循环
            logging.info(f"🔄 开始主循环，目标突破次数: {self.settings['max_raids_per_session']}")
            while (self.running and
                   self.current_raids < self.settings['max_raids_per_session'] and
                   self.consecutive_errors < 5):

                logging.info(f"📍 第 {self.current_raids + 1} 轮突破开始...")

                # 检查是否需要休息
                if self.should_take_break():
                    self.take_break()

                # 执行一次突破
                if self.execute_single_raid():
                    self.current_raids += 1
                    self.consecutive_errors = 0
                    total_time = time.time() - self.stats['start_time']
                    avg_time = total_time / self.current_raids if self.current_raids > 0 else 0
                    logging.info(f"✅ 完成第 {self.current_raids} 次突破 (平均用时: {avg_time:.1f}秒/次)")
                else:
                    self.consecutive_errors += 1
                    logging.warning(f"❌ 第 {self.current_raids + 1} 次突破失败，连续错误次数: {self.consecutive_errors}")

                    if self.consecutive_errors >= 5:
                        logging.error("🚫 连续错误次数过多，停止执行")
                        break

                # 随机延迟
                if self.current_raids < self.settings['max_raids_per_session']:
                    logging.debug("⏱️ 执行随机延迟...")
                    self.random_delay()

            logging.info(f"结界突破流程结束，共完成 {self.current_raids} 次突破")
            self.print_statistics()
            return True

        except Exception as e:
            logging.error(f"结界突破流程异常: {e}")
            return False
        finally:
            self.running = False

    def navigate_to_realm_raid(self) -> bool:
        """导航到结界突破界面"""
        try:
            logging.info("检查是否已在结界突破界面")

            # 首先检查是否已经在结界突破界面
            if self.find_template("realm_list_area", timeout=3):
                logging.info("已在结界突破界面，无需导航")
                return True

            logging.info("不在结界突破界面，开始导航...")

            # 尝试点击阴阳寮按钮（如果在主界面）
            if self.find_template("guild_button", timeout=3):
                logging.info("检测到阴阳寮按钮，从主界面导航")
                if not self.click_template("guild_button", timeout=5):
                    logging.error("点击阴阳寮按钮失败")
                    return False
                self.wait(2)

                # 点击结界突破按钮
                if not self.click_template("realm_raid_button", timeout=10):
                    logging.error("找不到结界突破按钮")
                    return False
                self.wait(3)
            else:
                # 尝试直接点击结界突破按钮（如果已在阴阳寮界面）
                logging.info("未检测到阴阳寮按钮，尝试直接点击结界突破")
                if self.find_template("realm_raid_button", timeout=3):
                    if not self.click_template("realm_raid_button", timeout=5):
                        logging.error("点击结界突破按钮失败")
                        return False
                    self.wait(3)
                else:
                    logging.warning("未找到导航按钮，假设已在结界突破界面")

            # 验证是否成功进入结界突破界面
            if self.find_template("realm_list_area", timeout=5):
                logging.info("成功进入结界突破界面")
                return True
            else:
                logging.warning("无法确认结界突破界面，尝试继续执行")
                return True  # 假设已在正确界面

        except Exception as e:
            logging.error(f"导航到结界突破界面失败: {e}")
            return False

    def execute_single_raid(self) -> bool:
        """执行单次结界突破"""
        try:
            # 选择目标结界
            target = self.realm_selector.select_target_realm(
                self.settings.get('target_strategy', 'smart_grid')
            )

            if not target:
                logging.warning("没有找到合适的目标结界")
                return False

            self.current_target = target
            logging.info(f"选择目标结界: 索引{target['index']} (第{target['row']}行第{target['col']}列)")

            # 点击目标结界
            if not self.click_realm(target):
                logging.error("点击目标结界失败")
                return False

            # 开始挑战
            if not self.start_challenge():
                logging.error("开始挑战失败")
                return False

            # 执行战斗
            battle_result = self.execute_battle()

            # 处理战斗结果
            return self.handle_battle_result(battle_result)

        except Exception as e:
            logging.error(f"执行单次突破失败: {e}")
            self.stats['errors'] += 1
            return False

    def click_realm(self, target) -> bool:
        """点击目标结界"""
        try:
            logging.info(f"准备点击结界: 索引{target['index']} (第{target['row']}行第{target['col']}列)")

            # 获取点击坐标
            click_pos = self.grid_locator.get_click_position(target['index'])
            if not click_pos:
                logging.error("无法获取结界点击坐标")
                return False

            logging.debug(f"原始点击坐标: ({click_pos[0]}, {click_pos[1]})")

            # 添加随机偏移
            offset_x = random.randint(-5, 5)
            offset_y = random.randint(-5, 5)
            final_x = click_pos[0] + offset_x
            final_y = click_pos[1] + offset_y

            logging.info(f"点击结界坐标: ({final_x}, {final_y}) (偏移: {offset_x}, {offset_y})")

            # 执行点击
            self.click(final_x, final_y)
            self.wait(1.5)

            logging.info("结界点击完成，等待界面响应...")
            return True

        except Exception as e:
            logging.error(f"点击结界失败: {e}")
            return False

    def start_challenge(self) -> bool:
        """开始挑战"""
        try:
            logging.info("开始挑战流程...")

            # 等待挑战界面加载
            logging.debug("等待挑战界面加载...")
            self.wait(2)

            # 点击挑战按钮
            logging.info("查找挑战按钮...")
            if self.click_template("challenge_button", timeout=5):
                logging.info("成功点击挑战按钮")
                self.wait(1)

                # 点击进攻按钮
                logging.info("查找进攻按钮...")
                if self.click_template("attack_button", timeout=5):
                    logging.info("成功点击进攻按钮，挑战开始")
                    return True
                else:
                    logging.error("找不到进攻按钮")
                    return False
            else:
                logging.error("找不到挑战按钮")
                return False

        except Exception as e:
            logging.error(f"开始挑战失败: {e}")
            return False

    def execute_battle(self) -> str:
        """执行战斗"""
        try:
            logging.info("开始战斗")

            # 等待战斗开始
            if not self.wait_for_battle_start():
                return "timeout"

            # 启用自动战斗
            if self.settings.get('use_auto_battle', True):
                self.enable_auto_battle()

            # 等待战斗结束
            battle_result = self.wait_for_battle_end()

            logging.info(f"战斗结果: {battle_result}")
            return battle_result

        except Exception as e:
            logging.error(f"执行战斗失败: {e}")
            return "error"

    def wait_for_battle_start(self, timeout=30) -> bool:
        """等待战斗开始"""
        logging.info(f"等待战斗开始，最大等待时间: {timeout}秒")
        start_time = time.time()
        check_count = 0

        while time.time() - start_time < timeout:
            check_count += 1
            elapsed = time.time() - start_time

            if self.find_template("battle_start", timeout=1):
                logging.info(f"检测到战斗开始标识 (用时: {elapsed:.1f}秒)")
                return True

            # 检查是否有自动战斗按钮（表示已经在战斗中）
            if self.find_template("auto_battle", timeout=1):
                logging.info(f"检测到自动战斗按钮，战斗已开始 (用时: {elapsed:.1f}秒)")
                return True

            if check_count % 5 == 0:  # 每5次检查输出一次进度
                logging.debug(f"等待战斗开始中... ({elapsed:.1f}/{timeout}秒)")

            self.wait(1)

        logging.warning(f"等待战斗开始超时 ({timeout}秒)")
        return False

    def enable_auto_battle(self):
        """启用自动战斗"""
        try:
            logging.info("尝试启用自动战斗...")
            if self.click_template("auto_battle", timeout=5):
                logging.info("✅ 自动战斗已启用")
                self.wait(1)  # 等待自动战斗生效
            else:
                logging.warning("⚠️ 未找到自动战斗按钮，可能已经启用或界面异常")
        except Exception as e:
            logging.error(f"❌ 启用自动战斗失败: {e}")

    def wait_for_battle_end(self, timeout=180) -> str:
        """等待战斗结束"""
        logging.info(f"等待战斗结束，最大等待时间: {timeout}秒")
        start_time = time.time()
        check_count = 0

        while time.time() - start_time < timeout:
            check_count += 1
            elapsed = time.time() - start_time

            # 检查胜利
            if self.find_template("victory", timeout=1):
                logging.info(f"🎉 战斗胜利！(用时: {elapsed:.1f}秒)")
                self.stats['victories'] += 1
                return "victory"

            # 检查失败
            if self.find_template("defeat", timeout=1):
                logging.info(f"💔 战斗失败 (用时: {elapsed:.1f}秒)")
                self.stats['defeats'] += 1
                return "defeat"

            # 检查战斗结束标识
            if self.find_template("battle_end", timeout=1):
                logging.info("检测到战斗结束标识，进一步判断胜负...")
                # 进一步判断胜负
                self.wait(1)
                if self.find_template("victory", timeout=2):
                    logging.info(f"🎉 战斗胜利！(用时: {elapsed:.1f}秒)")
                    self.stats['victories'] += 1
                    return "victory"
                elif self.find_template("defeat", timeout=2):
                    logging.info(f"💔 战斗失败 (用时: {elapsed:.1f}秒)")
                    self.stats['defeats'] += 1
                    return "defeat"
                else:
                    logging.warning("战斗结束但无法判断胜负")
                    return "unknown"

            # 每30秒输出一次进度
            if check_count % 15 == 0:  # 每15次检查(30秒)输出一次
                logging.info(f"⚔️ 战斗进行中... ({elapsed:.0f}/{timeout}秒)")

            self.wait(2)

        logging.warning(f"⏰ 等待战斗结束超时 ({timeout}秒)")
        return "timeout"

    def handle_battle_result(self, result: str) -> bool:
        """处理战斗结果"""
        try:
            logging.info(f"处理战斗结果: {result}")

            if result == "victory":
                logging.info("🎉 处理胜利结果...")
                self.stats['total_raids'] += 1

                # 点击确认按钮
                logging.info("查找确认按钮...")
                if self.click_template("confirm_button", timeout=10):
                    logging.info("✅ 已点击确认按钮")
                    self.wait(2)
                else:
                    logging.warning("⚠️ 未找到确认按钮，尝试继续")

                logging.info("✅ 胜利结果处理完成，继续下一次突破")
                return True

            elif result == "defeat":
                logging.info("💔 处理失败结果...")
                self.stats['total_raids'] += 1

                # 点击确认按钮
                logging.info("查找确认按钮...")
                if self.click_template("confirm_button", timeout=10):
                    logging.info("✅ 已点击确认按钮")
                    self.wait(2)
                else:
                    logging.warning("⚠️ 未找到确认按钮，尝试继续")

                # 根据设置决定是否重试
                retry_setting = self.settings.get('retry_on_defeat', True)
                if retry_setting:
                    logging.info("⚠️ 失败后重试已启用，将跳过此次突破")
                    return False  # 跳过此次，继续下一个目标
                else:
                    logging.info("✅ 失败结果处理完成，继续下一次突破")
                    return True

            elif result == "timeout":
                logging.warning("⏰ 处理超时结果...")
                # 尝试强制退出战斗
                self.force_exit_battle()
                return False

            else:
                logging.warning(f"❓ 未知战斗结果: {result}")
                return False

        except Exception as e:
            logging.error(f"❌ 处理战斗结果失败: {e}")
            return False

    def force_exit_battle(self):
        """强制退出战斗"""
        try:
            # 尝试点击返回按钮
            if self.click_template("back_button", timeout=5):
                self.wait(2)

            # 如果还在战斗中，尝试其他退出方式
            # 这里可以添加更多的退出逻辑

        except Exception as e:
            logging.error(f"强制退出战斗失败: {e}")

    def should_take_break(self) -> bool:
        """检查是否需要休息"""
        current_time = time.time()
        return (current_time - self.last_break_time) > self.settings.get('break_interval', 1800)

    def take_break(self):
        """休息"""
        break_duration = self.settings.get('break_duration', 300)
        logging.info(f"😴 开始休息 {break_duration} 秒 ({break_duration//60}分{break_duration%60}秒)")

        # 分段显示休息进度
        for i in range(0, break_duration, 60):  # 每分钟显示一次进度
            remaining = break_duration - i
            if remaining >= 60:
                time.sleep(60)
                logging.info(f"😴 休息中... 剩余 {remaining//60} 分钟")
            else:
                time.sleep(remaining)
                break

        self.last_break_time = time.time()
        logging.info("😊 休息结束，继续执行")

    def random_delay(self):
        """随机延迟"""
        min_delay = self.settings.get('random_delay_min', 1.0)
        max_delay = self.settings.get('random_delay_max', 3.0)
        delay = random.uniform(min_delay, max_delay)

        logging.info(f"⏱️ 随机延迟 {delay:.1f} 秒 (范围: {min_delay}-{max_delay}秒)")
        time.sleep(delay)

    def print_statistics(self):
        """打印统计信息"""
        total_time = time.time() - self.stats['start_time']

        logging.info("📊 =============== 结界突破统计 ===============")
        logging.info(f"⏰ 总运行时间: {total_time//3600:.0f}小时{(total_time%3600)//60:.0f}分{total_time%60:.0f}秒")
        logging.info(f"🎯 总突破次数: {self.stats['total_raids']}")
        logging.info(f"🎉 胜利次数: {self.stats['victories']}")
        logging.info(f"💔 失败次数: {self.stats['defeats']}")
        logging.info(f"❌ 错误次数: {self.stats['errors']}")

        if self.stats['total_raids'] > 0:
            win_rate = self.stats['victories'] / self.stats['total_raids'] * 100
            logging.info(f"📈 胜率: {win_rate:.1f}%")

            avg_time_per_raid = total_time / self.stats['total_raids']
            logging.info(f"⚡ 平均用时: {avg_time_per_raid:.1f} 秒/次")

        if total_time > 0:
            raids_per_hour = self.stats['total_raids'] / (total_time / 3600)
            logging.info(f"🚀 平均效率: {raids_per_hour:.1f} 次/小时")

        logging.info("📊 ============================================")

        # 记录到会话日志
        if hasattr(self, 'session_logger'):
            self.session_logger.info("=== 会话结束统计 ===")
            self.session_logger.info(f"运行时间: {total_time:.1f}秒, 突破次数: {self.stats['total_raids']}")
            self.session_logger.info(f"胜利: {self.stats['victories']}, 失败: {self.stats['defeats']}")
            if self.stats['total_raids'] > 0:
                win_rate = self.stats['victories'] / self.stats['total_raids'] * 100
                self.session_logger.info(f"胜率: {win_rate:.1f}%")

    def stop(self):
        """停止结界突破"""
        logging.info("停止结界突破")
        self.running = False

    def start_realm_raid(self) -> bool:
        """开始结界突破主流程

        Returns:
            bool: 是否成功完成
        """
        try:
            logging.info("开始结界突破流程")
            self.running = True
            self.stats['start_time'] = time.time()

            # 导航到结界突破界面
            if not self.navigate_to_realm_raid():
                logging.error("无法导航到结界突破界面")
                return False

            # 主循环
            while (self.running and
                   self.current_raids < self.settings['max_raids_per_session'] and
                   self.consecutive_errors < ERROR_HANDLING.get('max_consecutive_errors', 5)):

                # 检查是否需要休息
                if self.should_take_break():
                    self.take_break()

                # 执行一次突破
                if self.execute_single_raid():
                    self.current_raids += 1
                    self.consecutive_errors = 0
                    logging.info(f"完成第 {self.current_raids} 次突破")
                else:
                    self.consecutive_errors += 1
                    logging.warning(f"突破失败，连续错误次数: {self.consecutive_errors}")

                    if self.consecutive_errors >= ERROR_HANDLING.get('max_consecutive_errors', 5):
                        logging.error("连续错误次数过多，停止执行")
                        break

                # 随机延迟
                self.random_delay()

            logging.info(f"结界突破流程结束，共完成 {self.current_raids} 次突破")
            self.print_statistics()
            return True

        except Exception as e:
            logging.error(f"结界突破流程异常: {e}")
            return False
        finally:
            self.running = False

    def navigate_to_realm_raid(self) -> bool:
        """导航到结界突破界面

        Returns:
            bool: 是否成功导航
        """
        try:
            logging.info("导航到结界突破界面")

            # 点击阴阳寮按钮
            if not self.click_template("guild_button", timeout=10):
                logging.error("找不到阴阳寮按钮")
                return False

            self.wait(2)

            # 点击结界突破按钮
            if not self.click_template("realm_raid_button", timeout=10):
                logging.error("找不到结界突破按钮")
                return False

            self.wait(3)

            # 验证是否成功进入结界突破界面
            if self.find_template("realm_list_area", timeout=5):
                logging.info("成功进入结界突破界面")
                return True
            else:
                logging.error("未能进入结界突破界面")
                return False

        except Exception as e:
            logging.error(f"导航到结界突破界面失败: {e}")
            return False

    def execute_single_raid(self) -> bool:
        """执行单次结界突破

        Returns:
            bool: 是否成功完成
        """
        try:
            # 选择目标结界
            target = self.realm_selector.select_target_realm(
                self.settings.get('target_strategy', 'smart_grid')
            )

            if not target:
                logging.warning("没有找到合适的目标结界")
                return False

            self.current_target = target
            logging.info(f"选择目标结界: 索引{target['index']} (第{target['row']}行第{target['col']}列)")

            # 点击目标结界
            if not self.click_realm(target):
                logging.error("点击目标结界失败")
                return False

            # 开始挑战
            if not self.start_challenge():
                logging.error("开始挑战失败")
                return False

            # 执行战斗
            battle_result = self.execute_battle()

            # 处理战斗结果
            return self.handle_battle_result(battle_result)

        except Exception as e:
            logging.error(f"执行单次突破失败: {e}")
            self.stats['errors'] += 1
            return False
    
    def start_realm_raid(self) -> bool:
        """开始结界突破主流程
        
        Returns:
            bool: 是否成功完成
        """
        try:
            logging.info("开始结界突破流程")
            self.running = True
            self.stats['start_time'] = time.time()
            
            # 导航到结界突破界面
            if not self.navigate_to_realm_raid():
                logging.error("无法导航到结界突破界面")
                return False
            
            # 主循环
            while (self.running and 
                   self.current_raids < self.settings['max_raids_per_session'] and
                   self.consecutive_errors < ERROR_HANDLING['max_consecutive_errors']):
                
                # 检查是否需要休息
                if self.should_take_break():
                    self.take_break()
                
                # 执行一次突破
                if self.execute_single_raid():
                    self.current_raids += 1
                    self.consecutive_errors = 0
                    logging.info(f"完成第 {self.current_raids} 次突破")
                else:
                    self.consecutive_errors += 1
                    logging.warning(f"突破失败，连续错误次数: {self.consecutive_errors}")
                    
                    if self.consecutive_errors >= ERROR_HANDLING['max_consecutive_errors']:
                        logging.error("连续错误次数过多，停止执行")
                        break
                
                # 随机延迟
                self.random_delay()
            
            logging.info(f"结界突破流程结束，共完成 {self.current_raids} 次突破")
            self.print_statistics()
            return True
            
        except Exception as e:
            logging.error(f"结界突破流程异常: {e}")
            return False
        finally:
            self.running = False
    
    def navigate_to_realm_raid(self) -> bool:
        """导航到结界突破界面
        
        Returns:
            bool: 是否成功导航
        """
        try:
            logging.info("导航到结界突破界面")
            
            # 点击阴阳寮按钮
            if not self.click_template("guild_button", timeout=10):
                logging.error("找不到阴阳寮按钮")
                return False
            
            self.wait(2)
            
            # 点击结界突破按钮
            if not self.click_template("realm_raid_button", timeout=10):
                logging.error("找不到结界突破按钮")
                return False
            
            self.wait(3)
            
            # 验证是否成功进入结界突破界面
            if self.find_template("realm_list_area", timeout=5):
                logging.info("成功进入结界突破界面")
                return True
            else:
                logging.error("未能进入结界突破界面")
                return False
                
        except Exception as e:
            logging.error(f"导航到结界突破界面失败: {e}")
            return False
    
    def execute_single_raid(self) -> bool:
        """执行单次结界突破
        
        Returns:
            bool: 是否成功完成
        """
        try:
            # 扫描可用结界
            if not self.scan_available_realms():
                logging.warning("没有找到可用的结界")
                return False
            
            # 选择目标结界
            target = self.select_target_realm()
            if not target:
                logging.warning("没有合适的目标结界")
                return False
            
            self.current_target = target
            logging.info(f"选择目标结界: {target}")
            
            # 点击目标结界
            if not self.click_realm(target):
                logging.error("点击目标结界失败")
                return False
            
            # 开始挑战
            if not self.start_challenge():
                logging.error("开始挑战失败")
                return False
            
            # 执行战斗
            battle_result = self.execute_battle()
            
            # 处理战斗结果
            return self.handle_battle_result(battle_result)
            
        except Exception as e:
            logging.error(f"执行单次突破失败: {e}")
            self.stats['errors'] += 1
            return False
    
    def scan_available_realms(self) -> bool:
        """扫描可用的结界列表
        
        Returns:
            bool: 是否找到可用结界
        """
        try:
            logging.debug("扫描可用结界")
            self.available_realms = []
            
            # 获取结界网格配置
            grid_config = self.settings['realm_grid']
            
            # 计算结界位置
            for row in range(grid_config['rows']):
                realm_y = (grid_config['start_y'] + 
                          row * (grid_config['item_height'] + grid_config['spacing_y']))
                
                realm_pos = {
                    'row': row,
                    'x': grid_config['start_x'] + grid_config['item_width'] / 2,
                    'y': realm_y + grid_config['item_height'] / 2,
                    'level': self.detect_realm_level(grid_config['start_x'], realm_y, 
                                                   grid_config['item_width'], grid_config['item_height']),
                    'available': self.is_realm_available(grid_config['start_x'], realm_y,
                                                       grid_config['item_width'], grid_config['item_height'])
                }
                
                if realm_pos['available']:
                    self.available_realms.append(realm_pos)
            
            logging.info(f"找到 {len(self.available_realms)} 个可用结界")
            return len(self.available_realms) > 0
            
        except Exception as e:
            logging.error(f"扫描结界失败: {e}")
            return False
