# 📑 左侧标签页界面设计

## 🎯 **设计理念**
将所有功能设置集中到左侧标签页，右侧显示对应的说明和状态信息，让用户通过点击左侧标签进行功能切换和设置。

## 🎨 **界面布局**

### 整体布局
```
┌─────────────────────────────────────────────────────────────────┐
│ 阴阳师自动化工具 v2.0                                            │
├─────────────────────────────────────────────────────────────────┤
│ 文件 | 工具 | 帮助                                               │
├─────────────────────────────────────────────────────────────────┤
│ ┌─────────────────┐ ┌─────────────────────────────────────────┐ │
│ │   左侧标签页     │ │           右侧内容区域                   │ │
│ │                 │ │                                         │ │
│ │ 🔧 通用设置      │ │                                         │ │
│ │ 🗺️ 探索副本      │ │         功能说明和状态显示               │ │
│ │ 🏰 结界突破      │ │                                         │ │
│ │ 📝 运行日志      │ │                                         │ │
│ │                 │ │                                         │ │
│ │   [功能设置]     │ │                                         │ │
│ │                 │ │                                         │ │
│ └─────────────────┘ └─────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│ 当前窗口: 未选择 | 当前页面: 🔧 通用设置                        │
└─────────────────────────────────────────────────────────────────┘
```

## 📋 **标签页详解**

### 1. 🔧 **通用设置标签页**
```
功能设置区域：
├── 游戏窗口
│   ├── 窗口标题搜索
│   ├── 窗口句柄输入
│   └── 拖拽选择工具
├── 全局设置
│   ├── 操作模式 (后台/前台)
│   ├── 日志设置 (级别/文件管理)
│   ├── 性能设置 (截图质量/GPU加速)
│   └── 安全设置 (延迟/错误恢复)
└── 配置管理
    ├── 保存设置
    └── 重置默认

右侧内容区域：
├── 窗口管理说明
├── 全局设置说明
└── 配置管理说明
```

### 2. 🗺️ **探索副本标签页**
```
功能设置区域：
├── 探索设置
│   ├── 探索类型 (第一章~第二十八章)
│   ├── 探索次数 (1-1000)
│   └── 体力设置 (使用体力药/保留体力)
├── 高级设置
│   ├── 战斗设置 (自动战斗/快速战斗)
│   ├── 操作延迟 (500-3000毫秒)
│   └── 错误处理 (自动恢复/错误截图)
├── 控制按钮
│   ├── 开始探索
│   ├── 停止探索
│   └── 测试连接
└── 运行状态
    └── 当前状态显示

右侧内容区域：
├── 功能说明
├── 使用步骤
└── 高级设置说明
```

### 3. 🏰 **结界突破标签页**
```
功能设置区域：
├── 突破设置
│   ├── 突破次数 (1-1000)
│   ├── 选择策略 (智能网格/随机/优先级)
│   └── 失败处理 (重试/跳过冷却)
├── 高级设置
│   ├── 战斗设置 (自动战斗/超时时间)
│   ├── 随机延迟 (最小-最大毫秒)
│   └── 休息设置 (定时休息/休息时长)
├── 控制按钮
│   ├── 开始突破
│   ├── 停止突破
│   └── 测试连接
└── 运行状态
    └── 当前状态显示

右侧内容区域：
├── 功能说明
├── 使用步骤
└── 选择策略详解
```

### 4. 📝 **运行日志标签页**
```
功能设置区域：
├── 日志显示
│   ├── 实时日志滚动
│   ├── 日志级别过滤
│   └── 搜索功能
├── 日志管理
│   ├── 清空日志
│   ├── 保存日志
│   └── 打开日志文件夹
└── 日志设置
    ├── 自动滚动
    └── 显示时间戳

右侧内容区域：
├── 日志功能说明
├── 日志级别说明
└── 日志管理说明
```

## 🔄 **交互流程**

### 标签页切换
```
用户点击左侧标签
    ↓
触发 on_tab_changed 事件
    ↓
更新右侧内容区域
    ↓
更新状态栏显示
    ↓
记录切换日志
```

### 功能使用流程
```
1. 点击 "🔧 通用设置"
   ├── 选择游戏窗口
   ├── 配置全局设置
   └── 保存设置

2. 点击功能标签页 (探索/结界突破)
   ├── 配置功能参数
   ├── 点击"测试连接"验证
   └── 点击"开始"按钮启动

3. 点击 "📝 运行日志"
   ├── 查看实时运行状态
   ├── 监控错误信息
   └── 管理日志文件
```

## 🎨 **视觉设计**

### 标签页样式
```css
标签位置: 左侧垂直排列
标签宽度: 固定350px
标签图标: 使用emoji增强识别度
标签文字: 中文描述 + 图标
选中状态: 高亮显示
```

### 右侧内容区域
```css
背景色: 根据功能类型使用不同色调
- 通用设置: 蓝灰色调 (#f8f9fa)
- 探索副本: 绿色调 (#e8f5e8)  
- 结界突破: 橙色调 (#fff3e0)
- 运行日志: 紫色调 (#f3e5f5)

布局: 标题 + 内容卡片
字体: 清晰易读的字体大小
间距: 合理的内边距和外边距
```

## 🔧 **技术实现**

### 核心组件
```python
class MainWindow(QMainWindow):
    def setup_left_tabs(self):
        """设置左侧标签页"""
        self.left_tabs = QTabWidget()
        self.left_tabs.setTabPosition(QTabWidget.West)
        
    def on_tab_changed(self, index):
        """标签页切换回调"""
        self.update_content_area(index)
        
    def update_content_area(self, tab_index):
        """动态更新右侧内容"""
        # 根据标签页显示对应内容
```

### 信号通信
```python
# 窗口选择信号
window_selected = pyqtSignal(int, str)

# 设置变更信号  
settings_changed = pyqtSignal(dict)

# 标签切换信号
tab_changed = pyqtSignal(int)
```

## 📱 **响应式设计**

### 窗口大小适配
```
最小窗口: 1200x800
推荐窗口: 1400x900
左侧标签页: 固定350px宽度
右侧内容: 自适应剩余空间
分割器: 支持用户调整比例
```

### 内容自适应
```
文字内容: 自动换行
图片内容: 等比缩放
滚动条: 内容超出时自动显示
布局: 使用弹性布局适配不同尺寸
```

## 🚀 **使用优势**

### 用户体验
✅ **直观导航** - 左侧标签页清晰显示所有功能
✅ **集中设置** - 所有配置都在左侧面板完成
✅ **实时反馈** - 右侧内容区域提供详细说明
✅ **状态监控** - 状态栏实时显示当前状态

### 开发维护
✅ **模块化设计** - 每个标签页独立管理
✅ **易于扩展** - 新增功能只需添加新标签页
✅ **统一风格** - 一致的界面设计语言
✅ **代码复用** - 通用组件可重复使用

## 🎯 **启动体验**

### 首次启动
```bash
python main_gui_v2.py
```

### 界面展示
```
1. 程序启动后显示欢迎页面
2. 左侧显示4个功能标签页
3. 默认选中"🔧 通用设置"标签
4. 右侧显示对应的功能说明
5. 状态栏显示当前页面信息
```

### 操作指引
```
1. 首先点击"🔧 通用设置"选择游戏窗口
2. 配置全局设置（可选）
3. 点击功能标签页进行具体配置
4. 使用"📝 运行日志"监控运行状态
```

## ✨ **特色功能**

### 智能内容切换
- 根据选中的标签页动态显示对应的说明内容
- 不同功能使用不同的色彩主题
- 提供详细的使用指南和参数说明

### 统一状态管理
- 所有功能共享窗口选择状态
- 全局设置实时生效
- 统一的日志记录和错误处理

### 友好的用户引导
- 欢迎页面提供完整的使用指南
- 每个功能页面都有详细说明
- 状态栏实时显示当前操作状态

这种左侧标签页的设计让整个界面更加简洁直观，用户可以轻松地在不同功能间切换，同时获得相应的使用指导！🎉
