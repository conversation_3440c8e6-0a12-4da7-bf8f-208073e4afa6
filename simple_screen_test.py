#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""简单的屏幕截图和网格验证工具"""

import sys
import os
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                                QHBoxLayout, QPushButton, QLabel, QTextEdit,
                                QCheckBox, QFileDialog, QMessageBox)
    from PyQt5.QtCore import Qt
    from PyQt5.QtGui import QPixmap, QPainter, QPen, QColor, QBrush, QFont
    from PIL import ImageGrab
    import numpy as np
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请安装依赖: pip install PyQt5 pillow numpy")
    sys.exit(1)

class SimpleScreenTestGUI(QMainWindow):
    """简单的屏幕测试界面"""
    
    def __init__(self):
        super().__init__()
        self.screenshot_pixmap = None
        self.grid_config = None
        self.setup_ui()
        self.load_default_config()
    
    def setup_ui(self):
        """设置界面"""
        self.setWindowTitle("🎮 简单屏幕截图和网格验证")
        self.setGeometry(100, 100, 1000, 700)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        central_widget.setLayout(layout)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        self.capture_btn = QPushButton("📸 截取屏幕")
        self.capture_btn.clicked.connect(self.capture_screen)
        button_layout.addWidget(self.capture_btn)
        
        self.save_btn = QPushButton("💾 保存截图")
        self.save_btn.clicked.connect(self.save_screenshot)
        self.save_btn.setEnabled(False)
        button_layout.addWidget(self.save_btn)
        
        self.config_btn = QPushButton("🖱️ 交互式配置")
        self.config_btn.clicked.connect(self.open_interactive_config)
        button_layout.addWidget(self.config_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        # 显示选项
        option_layout = QHBoxLayout()
        
        self.show_grid_cb = QCheckBox("显示网格区域")
        self.show_grid_cb.setChecked(True)
        self.show_grid_cb.toggled.connect(self.update_display)
        option_layout.addWidget(self.show_grid_cb)
        
        self.show_items_cb = QCheckBox("显示结界项")
        self.show_items_cb.setChecked(True)
        self.show_items_cb.toggled.connect(self.update_display)
        option_layout.addWidget(self.show_items_cb)
        
        option_layout.addStretch()
        layout.addLayout(option_layout)
        
        # 图像显示区域
        self.image_label = QLabel()
        self.image_label.setMinimumSize(800, 500)
        self.image_label.setStyleSheet("border: 1px solid gray;")
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setText("点击'截取屏幕'开始")
        layout.addWidget(self.image_label)
        
        # 状态信息
        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(100)
        self.status_text.setReadOnly(True)
        layout.addWidget(self.status_text)
    
    def load_default_config(self):
        """加载默认配置"""
        try:
            # 默认网格配置
            self.grid_config = {
                "grid_area": {
                    "left": 0.12,
                    "top": 0.20,
                    "right": 0.88,
                    "bottom": 0.80
                },
                "layout": {
                    "rows": 3,
                    "columns": 3,
                    "item_spacing": {
                        "horizontal": 0.02,
                        "vertical": 0.03
                    }
                },
                "item_size": {
                    "width": 0.22,
                    "height": 0.15
                }
            }
            self.log_status("✅ 已加载默认网格配置")
        except Exception as e:
            self.log_status(f"❌ 加载默认配置失败: {e}")
    
    def capture_screen(self):
        """截取屏幕"""
        try:
            self.log_status("📸 正在截取屏幕...")
            
            # 隐藏窗口以避免截取到自己
            self.hide()
            
            # 等待一下让窗口完全隐藏
            QApplication.processEvents()
            import time
            time.sleep(0.5)
            
            # 截取屏幕
            screenshot = ImageGrab.grab()
            
            # 显示窗口
            self.show()
            
            # 转换为QPixmap
            screenshot_array = np.array(screenshot)
            height, width, channel = screenshot_array.shape
            bytes_per_line = 3 * width
            
            from PyQt5.QtGui import QImage
            q_image = QImage(screenshot_array.data, width, height, bytes_per_line, QImage.Format_RGB888)
            self.screenshot_pixmap = QPixmap.fromImage(q_image)
            
            self.update_display()
            self.save_btn.setEnabled(True)
            
            self.log_status(f"✅ 截图成功! 尺寸: {width}×{height}")
            
        except Exception as e:
            self.show()  # 确保窗口显示
            self.log_status(f"❌ 截图失败: {e}")
    
    def update_display(self):
        """更新显示"""
        if self.screenshot_pixmap is None:
            return
        
        try:
            # 创建副本用于绘制
            display_pixmap = self.screenshot_pixmap.copy()
            
            # 如果需要显示网格，绘制叠加层
            if (self.show_grid_cb.isChecked() or self.show_items_cb.isChecked()) and self.grid_config:
                self.draw_grid_overlay(display_pixmap)
            
            # 缩放以适应显示区域
            label_size = self.image_label.size()
            scaled_pixmap = display_pixmap.scaled(
                label_size, Qt.KeepAspectRatio, Qt.SmoothTransformation
            )
            
            self.image_label.setPixmap(scaled_pixmap)
            
        except Exception as e:
            self.log_status(f"❌ 更新显示失败: {e}")
    
    def draw_grid_overlay(self, pixmap):
        """在截图上绘制网格叠加"""
        painter = QPainter(pixmap)
        
        width = pixmap.width()
        height = pixmap.height()
        
        config = self.grid_config
        grid_area = config["grid_area"]
        
        # 计算网格区域
        grid_left = int(grid_area["left"] * width)
        grid_top = int(grid_area["top"] * height)
        grid_right = int(grid_area["right"] * width)
        grid_bottom = int(grid_area["bottom"] * height)
        
        if self.show_grid_cb.isChecked():
            # 绘制网格区域边框
            painter.setPen(QPen(QColor("#00ff00"), 4))  # 绿色边框
            painter.setBrush(QBrush(QColor(0, 255, 0, 30)))  # 半透明绿色填充
            painter.drawRect(grid_left, grid_top, grid_right - grid_left, grid_bottom - grid_top)
            
            # 绘制标签
            painter.setPen(QPen(QColor("#ffffff"), 2))
            painter.setFont(QFont("Arial", 16, QFont.Bold))
            painter.drawText(grid_left + 10, grid_top + 25, "网格区域")
        
        if self.show_items_cb.isChecked():
            # 绘制结界项
            layout = config["layout"]
            item_size = config["item_size"]
            
            grid_width = grid_right - grid_left
            grid_height = grid_bottom - grid_top
            
            item_width = int(item_size["width"] * grid_width)
            item_height = int(item_size["height"] * grid_height)
            spacing_h = int(layout["item_spacing"]["horizontal"] * grid_width)
            spacing_v = int(layout["item_spacing"]["vertical"] * grid_height)
            
            painter.setPen(QPen(QColor("#ff0000"), 3))  # 红色边框
            painter.setBrush(QBrush(QColor(255, 0, 0, 50)))  # 半透明红色填充
            
            for row in range(layout["rows"]):
                for col in range(layout["columns"]):
                    x = grid_left + col * (item_width + spacing_h)
                    y = grid_top + row * (item_height + spacing_v)
                    
                    painter.drawRect(x, y, item_width, item_height)
                    
                    # 绘制索引
                    painter.setPen(QPen(QColor("#ffffff"), 2))
                    painter.setFont(QFont("Arial", 14, QFont.Bold))
                    index = row * layout["columns"] + col
                    painter.drawText(x + 5, y + 20, str(index))
        
        painter.end()
    
    def save_screenshot(self):
        """保存截图"""
        if self.screenshot_pixmap is None:
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"screen_capture_{timestamp}.png"
        
        try:
            # 创建带网格的版本
            save_pixmap = self.screenshot_pixmap.copy()
            if self.grid_config:
                self.draw_grid_overlay(save_pixmap)
            
            save_pixmap.save(filename)
            self.log_status(f"💾 截图已保存: {filename}")
        except Exception as e:
            self.log_status(f"❌ 保存失败: {e}")
    
    def open_interactive_config(self):
        """打开交互式配置"""
        try:
            from features.realm_raid.interactive_grid_config import InteractiveGridConfigDialog
            
            dialog = InteractiveGridConfigDialog(self, self.grid_config)
            if dialog.exec_() == dialog.Accepted:
                self.grid_config = dialog.get_config()
                self.update_display()
                self.log_status("✅ 交互式配置已更新")
        except Exception as e:
            self.log_status(f"❌ 打开交互式配置失败: {e}")
    
    def log_status(self, message):
        """记录状态信息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.status_text.append(f"[{timestamp}] {message}")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setApplicationName("简单屏幕截图和网格验证")
    
    window = SimpleScreenTestGUI()
    window.show()
    
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
