# 网格配置问题解决指南

## 🔧 **界面映射失败解决方案**

### ✅ **问题已修复**

我已经为您解决了界面映射打开失败的问题，并提供了两个解决方案：

#### **方案一：简化版配置工具 (推荐)**
- 文件：`features/realm_raid/simple_grid_config.py`
- 特点：轻量级、稳定、易用
- 功能：完整的网格配置 + 实时预览

#### **方案二：完整版界面映射**
- 文件：`features/realm_raid/game_overlay_config.py`
- 特点：功能丰富、游戏界面覆盖
- 依赖：需要更多系统组件

## 🚀 **使用简化版配置工具**

### **启动方法**
1. **从主界面**：`main_gui_v2.py` → 结界突破 → "界面映射"按钮
2. **直接启动**：`python features/realm_raid/simple_grid_config.py`

### **界面说明**
```
┌─────────────────────────────────────────┐
│        结界突破 3×3网格配置              │
├─────────────────┬───────────────────────┤
│   控制面板      │      预览面板         │
├─────────────────┼───────────────────────┤
│ 📐 网格区域     │  ┌─────────────────┐  │
│   左: [====]    │  │   蓝色边框:     │  │
│   上: [====]    │  │   网格区域      │  │
│   右: [====]    │  │                 │  │
│   下: [====]    │  │   红色矩形:     │  │
│                 │  │   结界位置      │  │
│ 🗂️ 网格布局     │  │                 │  │
│   行数: [3]     │  │   数字: 索引    │  │
│   列数: [3]     │  └─────────────────┘  │
│   水平间距: [=] │                       │
│   垂直间距: [=] │                       │
│                 │                       │
│ 📏 结界项尺寸   │                       │
│   宽度: [====]  │                       │
│   高度: [====]  │                       │
└─────────────────┴───────────────────────┘
│  [重置默认]  [取消]  [保存配置]         │
└─────────────────────────────────────────┘
```

### **配置步骤**
1. **调整网格区域**
   - 拖拽"左、上、右、下"滑块
   - 蓝色边框显示网格区域

2. **设置网格布局**
   - 调整行数和列数 (支持1-5)
   - 调整水平和垂直间距

3. **优化结界项尺寸**
   - 调整宽度和高度
   - 红色矩形显示结界位置

4. **实时预览**
   - 右侧预览实时显示效果
   - 数字显示结界索引 (0-8)

5. **保存配置**
   - 点击"保存配置"按钮
   - 配置保存到 `custom_grid_config.json`

## 🎯 **配置参数说明**

### **网格区域 (百分比)**
- **左边界**: 0-50% (网格左边距离屏幕左边的比例)
- **上边界**: 0-50% (网格上边距离屏幕上边的比例)
- **右边界**: 50-100% (网格右边距离屏幕左边的比例)
- **下边界**: 50-100% (网格下边距离屏幕上边的比例)

### **网格布局**
- **行数**: 1-5 (网格的行数，默认3)
- **列数**: 1-5 (网格的列数，默认3)
- **水平间距**: 0-10% (结界项之间的水平间距)
- **垂直间距**: 0-10% (结界项之间的垂直间距)

### **结界项尺寸**
- **宽度**: 10-40% (单个结界项的宽度)
- **高度**: 10-30% (单个结界项的高度)

## 📋 **默认配置值**

```json
{
  "grid_area": {
    "left": 0.12,    // 12%
    "top": 0.20,     // 20%
    "right": 0.88,   // 88%
    "bottom": 0.80   // 80%
  },
  "layout": {
    "rows": 3,
    "columns": 3,
    "item_spacing": {
      "horizontal": 0.02,  // 2%
      "vertical": 0.03     // 3%
    }
  },
  "item_size": {
    "width": 0.22,   // 22%
    "height": 0.15   // 15%
  }
}
```

## 🔧 **常见问题解决**

### **❌ 配置工具打不开**
**解决方案**：
1. 确保已安装PyQt5：`pip install PyQt5`
2. 使用简化版工具：`python features/realm_raid/simple_grid_config.py`
3. 检查Python环境是否正确

### **❌ 保存配置失败**
**解决方案**：
1. 检查文件写入权限
2. 确保目录存在
3. 手动创建 `custom_grid_config.json` 文件

### **❌ 预览显示异常**
**解决方案**：
1. 调整窗口大小
2. 重新打开配置工具
3. 重置为默认配置

### **❌ 配置不生效**
**解决方案**：
1. 确认配置已保存成功
2. 重启程序
3. 检查配置文件格式是否正确

## 🎨 **配置技巧**

### **精确调整方法**
1. **先粗调**：大致调整网格区域位置
2. **再细调**：精确调整结界项尺寸
3. **验证效果**：观察预览中的覆盖效果
4. **保存测试**：保存后实际测试效果

### **不同分辨率适配**
- **1920×1080**: 使用默认配置
- **1366×768**: 适当调整网格区域
- **2560×1440**: 可能需要调整间距

### **特殊布局适配**
- **2×2网格**: 行数=2, 列数=2
- **4×4网格**: 行数=4, 列数=4
- **3×4网格**: 行数=3, 列数=4

## ✅ **验证配置正确性**

### **检查清单**
- [ ] 蓝色边框完全覆盖结界列表区域
- [ ] 红色矩形精确覆盖每个结界
- [ ] 相邻结界不重叠
- [ ] 所有结界都在屏幕范围内
- [ ] 索引编号正确显示 (0-8)

### **测试方法**
1. 保存配置
2. 重启程序
3. 运行结界突破功能
4. 观察点击位置是否准确

## 🎊 **总结**

### ✅ **简化版配置工具优势**
- **🚀 启动快速**: 无复杂依赖，秒开
- **🎯 操作简单**: 拖拽滑块即可配置
- **👁️ 实时预览**: 调整参数立即显示效果
- **💾 稳定保存**: 可靠的配置保存机制
- **🔧 易于调试**: 清晰的界面和提示

### 🚀 **开始使用**
现在您可以通过简化版配置工具，轻松调整3×3网格配置了！

**点击"界面映射"按钮，开始配置您的专属网格吧！** 🎯

## 📞 **获取帮助**

如果仍有问题，请：
1. 检查控制台错误信息
2. 确认Python和PyQt5环境
3. 尝试重新安装依赖包
4. 使用默认配置进行测试
