# 简化版结界突破模板指南

## 🎯 **简化后的操作流程**

```
假设已在结界突破界面 → 选择结界 → 点击进攻 → 自动战斗 → 识别胜利 → 确认 → 重复
```

## 📋 **核心模板清单 (仅需4个)**

### 🔴 **最核心模板 - 基本功能可用**

1. **attack_button.png** - 进攻按钮
   - 📸 截图时机: 选择结界后出现的"进攻"按钮
   - 📍 位置: 通常在界面下方中央
   - 🎯 作用: 确认挑战选中的结界

2. **auto_battle.png** - 自动战斗按钮  
   - 📸 截图时机: 进入战斗界面后
   - 📍 位置: 通常在右下角
   - 🎯 作用: 启用自动战斗模式

3. **victory.png** - 胜利标识
   - 📸 截图时机: 战斗胜利后
   - 📍 位置: 屏幕中央的"胜利"文字或图标
   - 🎯 作用: 识别战斗胜利

4. **confirm_button.png** - 确认按钮
   - 📸 截图时机: 胜利界面的确认按钮
   - 📍 位置: 通常在胜利提示下方
   - 🎯 作用: 确认胜利结果，返回结界列表

## 🚀 **快速制作步骤**

### **第1步: 准备工作**
1. 启动阴阳师游戏
2. 手动进入结界突破界面
3. 准备截图工具 (Win + Shift + S)

### **第2步: 制作模板**
1. **选择任意结界**
2. **截取"进攻"按钮** → 保存为 `templates/realm_raid/attack_button.png`
3. **点击进攻，进入战斗**
4. **截取"自动"按钮** → 保存为 `templates/realm_raid/auto_battle.png`
5. **等待战斗胜利**
6. **截取"胜利"文字** → 保存为 `templates/realm_raid/victory.png`
7. **截取"确认"按钮** → 保存为 `templates/realm_raid/confirm_button.png`

### **第3步: 验证模板**
```bash
python test_templates.py
```

## 📐 **截图要求**

### ✅ **推荐做法**
- 只截取按钮/文字本身，不要包含过多背景
- 确保图像清晰，无模糊
- PNG格式，尺寸建议50-200像素
- 包含足够的特征信息

### ❌ **避免问题**
- 不要截取过大的区域
- 不要包含会变化的元素（如时间、数字）
- 不要截取模糊或不清晰的图像
- 不要截取过小无法识别的图像

## 🔧 **高级配置 (可选)**

### **如果需要更完善的功能，可以添加:**

5. **defeat.png** - 失败标识
   - 处理战斗失败的情况

6. **realm_list_area.png** - 结界列表区域
   - 验证是否在正确界面

7. **battle_start.png** - 战斗开始标识
   - 确认成功进入战斗

## 🎯 **测试方法**

### **制作完成后:**
1. 将4个模板文件放入 `templates/realm_raid/` 目录
2. 运行 `python test_templates.py` 验证
3. 启动结界突破功能测试
4. 观察控制台日志，确认模板识别成功

### **常见问题:**
- **识别失败**: 重新截取更清晰的模板
- **点击位置不准**: 截取更精确的按钮区域
- **误识别**: 截取更具特征性的区域

## 🎊 **完成标准**

### ✅ **基本功能可用标准:**
- 能够识别并点击进攻按钮
- 能够启用自动战斗
- 能够识别胜利并确认
- 能够返回结界列表继续

### 🚀 **开始测试:**
有了这4个模板，结界突破的核心功能就能正常工作了！

## 📝 **制作清单**

- [ ] attack_button.png - 进攻按钮
- [ ] auto_battle.png - 自动战斗按钮  
- [ ] victory.png - 胜利标识
- [ ] confirm_button.png - 确认按钮

**完成这4个模板，就可以开始自动结界突破了！** 🎯
