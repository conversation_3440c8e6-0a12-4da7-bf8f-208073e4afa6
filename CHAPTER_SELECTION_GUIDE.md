# 多章节选择功能使用指南

## 概述

新的章节选择系统支持使用OCR文字识别来自动选择任意章节，不再局限于硬编码的28章。系统具有以下特点：

- **OCR优先**：使用EasyOCR识别章节号码文字
- **图像模板回退**：OCR失败时自动回退到传统的图像模板匹配
- **动态配置**：支持运行时更改目标章节
- **轻量化设计**：最小化依赖，易于部署

## 安装依赖

首先安装EasyOCR库：

```bash
pip install easyocr
```

如果安装过程中遇到问题，可以尝试：

```bash
pip install easyocr --no-deps
pip install torch torchvision
pip install opencv-python
pip install pillow
pip install numpy
```

## 配置章节

### 方法1：修改配置文件

编辑 `features/explore/explore_config.py`：

```python
DEFAULT_SETTINGS = {
    # ... 其他设置 ...
    'target_chapter': 26,           # 设置目标章节为26章
    'use_ocr_first': True,          # 优先使用OCR识别
    'fallback_to_template': True,   # OCR失败时回退到图像模板
}
```

### 方法2：代码中动态设置

```python
from features.explore.explore_bot import ExploreBot

# 创建机器人
bot = ExploreBot(game_hwnd)

# 设置目标章节
bot.set_target_chapter(26)  # 设置为26章
```

## 使用方法

### 基本使用

```python
from features.explore.explore_bot import ExploreBot
from core.window_utils import WindowUtils

# 查找游戏窗口
game_hwnd = WindowUtils.find_game_window()

# 创建探索机器人
bot = ExploreBot(game_hwnd)

# 设置目标章节
bot.set_target_chapter(26)

# 开始探索（会自动选择设置的章节）
bot.run_explore()
```

### 检测可用章节

```python
# 获取当前界面所有可用章节
available_chapters = bot.get_available_chapters()
print(f"可用章节: {available_chapters}")
```

### 手动章节选择

```python
# 手动尝试选择章节
success = bot.chapter_selector.click_target_chapter()
if success:
    print("成功选择章节")
else:
    print("章节选择失败")
```

## 添加新章节模板

如果某个章节的OCR识别不准确，可以添加图像模板作为回退：

1. 截取该章节按钮的图像，保存为PNG格式
2. 将图像放在 `templates/explore/` 目录下
3. 在 `features/explore/explore_config.py` 中添加映射：

```python
CHAPTER_TEMPLATES = {
    28: 'templates/explore/28.png',  # 已有的28章模板
    26: 'templates/explore/26.png',  # 新增26章模板
    # 可以继续添加更多章节
}
```

## 测试功能

运行测试脚本来验证功能：

```bash
python test_chapter_selection.py
```

测试脚本会：
1. 查找游戏窗口
2. 测试章节设置功能
3. 检测当前可用章节
4. 测试多个章节的识别

## 故障排除

### OCR识别失败

如果OCR无法识别章节：

1. **检查EasyOCR安装**：确保正确安装了easyocr库
2. **界面清晰度**：确保游戏界面清晰，章节号码可见
3. **添加图像模板**：为该章节添加图像模板作为回退

### 章节点击位置不准确

1. **调整点击位置**：OCR会自动计算文字中心点
2. **检查分辨率**：确保游戏分辨率与基准分辨率匹配
3. **使用图像模板**：为特定章节创建精确的图像模板

### 性能问题

1. **关闭GPU加速**：OCR默认使用CPU模式，性能已优化
2. **减少识别频率**：避免频繁调用OCR识别
3. **使用模板优先**：对于常用章节使用图像模板

## 配置选项

| 选项 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| target_chapter | int | 28 | 目标章节号 |
| use_ocr_first | bool | True | 是否优先使用OCR |
| fallback_to_template | bool | True | OCR失败时是否回退到模板 |

## 注意事项

1. **首次使用**：EasyOCR首次运行时会下载模型文件，可能需要较长时间
2. **网络连接**：模型下载需要网络连接
3. **性能影响**：OCR识别比图像模板匹配稍慢，但准确性更高
4. **兼容性**：系统会自动回退到原有的图像模板系统，确保向后兼容

## 扩展功能

系统设计为可扩展的，未来可以：

- 支持识别章节名称而不仅是数字
- 添加更多OCR引擎支持
- 实现智能章节推荐
- 支持章节收藏和快速切换