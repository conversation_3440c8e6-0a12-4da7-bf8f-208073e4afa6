"""结界突破配置文件"""

# 3x3网格配置
# 3x3网格配置
REALM_GRID_CONFIG = {
    # 网格区域定义（相对坐标 0.0-1.0）
    "grid_area": {
        "left": 0.12,      # 网格左边界
        "top": 0.19,       # 网格上边界
        "right": 0.9720000000000001,     # 网格右边界
        "bottom": 0.8624999999999987     # 网格下边界
    },

    # 网格布局 - 3x3排列
    "layout": {
        "rows": 3,         # 3行
        "columns": 3,      # 3列
        "item_spacing": {
            "horizontal": 0.02,  # 水平间距
            "vertical": 0.03     # 垂直间距
        }
    },

    # 单个结界项尺寸
    "item_size": {
        "width": 0.268,     # 结界项宽度
        "height": 0.19500000000000003     # 结界项高度
    }
}

# 突破券检测区域配置
RAID_TICKET_CONFIG = {
    # 突破券显示区域（通常在右上角或特定位置）
    "ticket_area": {
        "left": 0.75,      # 券数量显示区域左边界
        "top": 0.05,       # 券数量显示区域上边界
        "right": 0.95,     # 券数量显示区域右边界
        "bottom": 0.15     # 券数量显示区域下边界
    },

    # OCR识别配置
    "ocr_config": {
        "number_only": True,        # 只识别数字
        "min_confidence": 0.7,      # 最小置信度
        "preprocessing": True       # 是否预处理图像
    }
}

# 结界突破相关模板配置
REALM_RAID_TEMPLATES = {
    # 主界面导航
    "guild_button": {
        "path": "templates/realm_raid/guild_button.png",
        "threshold": 0.8,
        "rel_click_point": (0.5, 0.5),
        "description": "阴阳寮按钮"
    },
    "realm_raid_button": {
        "path": "templates/realm_raid/realm_raid_button.png",
        "threshold": 0.8,
        "rel_click_point": (0.5, 0.5),
        "description": "结界突破按钮"
    },

    # 结界突破界面元素
    "realm_list_area": {
        "path": "templates/realm_raid/realm_list_area.png",
        "threshold": 0.7,
        "rel_click_point": (0.5, 0.5),
        "description": "结界列表区域标识"
    },
    "challenge_button": {
        "path": "templates/realm_raid/challenge_button.png",
        "threshold": 0.8,
        "rel_click_point": (0.5, 0.5),
        "description": "挑战按钮"
    },
    "attack_button": {
        "path": "templates/realm_raid/attack_button.png",
        "threshold": 0.8,
        "rel_click_point": (0.5, 0.5),
        "description": "进攻按钮"
    },

    # 战斗相关
    "battle_start": {
        "path": "templates/realm_raid/battle_start.png",
        "threshold": 0.8,
        "rel_click_point": (0.5, 0.5),
        "description": "战斗开始标识"
    },
    "auto_battle": {
        "path": "templates/realm_raid/auto_battle.png",
        "threshold": 0.8,
        "rel_click_point": (0.5, 0.5),
        "description": "自动战斗按钮"
    },
    "battle_end": {
        "path": "templates/realm_raid/battle_end.png",
        "threshold": 0.8,
        "rel_click_point": (0.5, 0.5),
        "description": "战斗结束标识"
    },

    # 结果处理
    "victory": {
        "path": "templates/realm_raid/victory.png",
        "threshold": 0.8,
        "rel_click_point": (0.5, 0.5),
        "description": "胜利标识"
    },
    "defeat": {
        "path": "templates/realm_raid/defeat.png",
        "threshold": 0.8,
        "rel_click_point": (0.5, 0.5),
        "description": "失败标识"
    },
    "confirm_button": {
        "path": "templates/realm_raid/confirm_button.png",
        "threshold": 0.8,
        "rel_click_point": (0.5, 0.5),
        "description": "确认按钮"
    },
    "back_button": {
        "path": "templates/realm_raid/back_button.png",
        "threshold": 0.8,
        "rel_click_point": (0.5, 0.5),
        "description": "返回按钮"
    },

    # 特殊状态
    "no_tickets": {
        "path": "templates/realm_raid/no_tickets.png",
        "threshold": 0.8,
        "rel_click_point": (0.5, 0.5),
        "description": "没有突破券"
    },
    "cooldown": {
        "path": "templates/realm_raid/cooldown.png",
        "threshold": 0.8,
        "rel_click_point": (0.5, 0.5),
        "description": "冷却时间"
    },

    # 刷新和滚动
    "refresh_button": {
        "path": "templates/realm_raid/refresh_button.png",
        "threshold": 0.8,
        "rel_click_point": (0.5, 0.5),
        "description": "刷新按钮"
    },
    "realm_background": {
        "path": "templates/realm_raid/realm_background.png",
        "threshold": 0.6,
        "rel_click_point": (0.5, 0.5),
        "description": "结界背景标识"
    }
}

# 默认设置
DEFAULT_REALM_RAID_SETTINGS = {
    # 基本设置
    "max_raids_per_session": 10,  # 每次会话最大突破次数
    "battle_timeout": 180,  # 战斗超时时间（秒）
    "retry_on_defeat": True,  # 失败后是否重试
    "max_retries": 3,  # 最大重试次数

    # 目标选择策略
    "target_strategy": "smart_grid",  # 选择策略
    "randomize_order": True,  # 随机化选择顺序
    "skip_cooldown": True,  # 跳过冷却中的结界

    # 战斗设置
    "use_auto_battle": True,  # 使用自动战斗
    "battle_speed": "normal",  # 战斗速度: slow, normal, fast

    # 安全设置
    "random_delay_min": 1.0,  # 最小随机延迟
    "random_delay_max": 3.0,  # 最大随机延迟
    "break_interval": 1800,  # 休息间隔（秒）
    "break_duration": 300,  # 休息时长（秒）

    # 突破券设置
    "min_tickets_required": 1,  # 最少需要的突破券数量
    "wait_for_tickets": True,  # 是否等待突破券恢复
    "max_wait_time": 300,  # 最大等待时间（秒）
    "ticket_check_interval": 30,  # 突破券检查间隔（秒）

    # OCR设置
    "use_ocr_for_level": True,  # 使用OCR识别等级
    "use_ocr_for_name": False,  # 使用OCR识别名称
    "ocr_confidence_threshold": 0.7  # OCR置信度阈值
}

# 选择策略配置
SELECTION_STRATEGIES = {
    "random": {
        "description": "完全随机选择",
        "safety_level": "high",
        "efficiency": "low"
    },
    "top_left_priority": {
        "description": "优先选择左上角",
        "safety_level": "low",
        "efficiency": "high"
    },
    "center_out": {
        "description": "从中心向外选择",
        "safety_level": "medium",
        "efficiency": "medium"
    },
    "corners_first": {
        "description": "优先选择四个角",
        "safety_level": "medium",
        "efficiency": "medium"
    },
    "avoid_center": {
        "description": "避免选择中心位置",
        "safety_level": "medium",
        "efficiency": "medium"
    },
    "smart_grid": {
        "description": "智能网格选择（推荐）",
        "safety_level": "high",
        "efficiency": "medium"
    }
}

# 结界等级权重配置
REALM_LEVEL_WEIGHTS = {
    1: 1.0,
    2: 1.2,
    3: 1.5,
    4: 2.0,
    5: 2.5,
    6: 3.0
}

# 战斗结果处理配置
BATTLE_RESULT_ACTIONS = {
    "victory": {
        "wait_time": 2.0,
        "click_confirm": True,
        "continue_raiding": True
    },
    "defeat": {
        "wait_time": 3.0,
        "click_confirm": True,
        "retry_same_target": False,
        "find_new_target": True
    },
    "timeout": {
        "force_exit": True,
        "restart_battle": False
    }
}

# 错误处理配置
ERROR_HANDLING = {
    "max_consecutive_errors": 5,
    "error_cooldown": 30,  # 错误后冷却时间
    "auto_restart_on_error": True,
    "screenshot_on_error": True
}
