#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""阴阳师自动化工具 v2.0 - 主程序入口"""

import sys
import os
import logging
import traceback

# 设置编码
if sys.platform.startswith('win'):
    import locale
    try:
        locale.setlocale(locale.LC_ALL, 'Chinese_China.utf8')
    except:
        try:
            locale.setlocale(locale.LC_ALL, 'zh_CN.UTF-8')
        except:
            pass

from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QIcon

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
from core.main_window import MainWindow
from core.logging_config import setup_logging


def setup_application():
    """设置应用程序"""
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("阴阳师自动化工具")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("OnmyojiBot")
    
    # 设置应用程序图标（如果存在）
    icon_path = "resources/icon.ico"
    if os.path.exists(icon_path):
        app.setWindowIcon(QIcon(icon_path))
    
    # 设置高DPI支持
    app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    return app


def check_dependencies():
    """检查依赖项"""
    missing_deps = []
    
    try:
        import cv2
    except ImportError:
        missing_deps.append("opencv-python")
    
    try:
        import numpy
    except ImportError:
        missing_deps.append("numpy")
    
    try:
        import easyocr
    except ImportError:
        missing_deps.append("easyocr")
    
    try:
        import win32gui
    except ImportError:
        missing_deps.append("pywin32")
    
    if missing_deps:
        error_msg = f"缺少以下依赖项:\n{', '.join(missing_deps)}\n\n请运行: pip install {' '.join(missing_deps)}"
        QMessageBox.critical(None, "依赖项错误", error_msg)
        return False
    
    return True


def main():
    """主函数"""
    app = None
    try:
        # 设置日志系统
        setup_logging(
            log_level=logging.INFO,
            enable_file_log=True,
            log_dir="logs"
        )

        logging.info("🚀 阴阳师自动化工具 v2.0 启动")

        # 创建应用程序
        app = setup_application()

        # 检查依赖项
        if not check_dependencies():
            return 1

        logging.info("📦 依赖检查完成")

        # 创建并显示主窗口
        logging.info("🏗️ 创建主窗口...")
        main_window = MainWindow()

        logging.info("🖥️ 显示主窗口...")
        main_window.show()

        logging.info("✅ 主窗口已显示")

        # 运行应用程序
        logging.info("🔄 启动事件循环...")
        return app.exec_()

    except ImportError as e:
        error_msg = f"模块导入失败: {e}"
        print(f"❌ {error_msg}")
        logging.error(error_msg)
        logging.error(f"错误详情: {traceback.format_exc()}")

        if app is None:
            app = QApplication(sys.argv)
        QMessageBox.critical(None, "导入错误", f"{error_msg}\n\n请检查依赖安装是否完整")
        return 1

    except Exception as e:
        error_msg = f"程序启动失败: {e}"
        print(f"❌ {error_msg}")
        logging.error(error_msg)
        logging.error(f"错误详情: {traceback.format_exc()}")

        # 显示错误对话框
        if app is None:
            try:
                app = QApplication(sys.argv)
            except:
                print("无法创建QApplication，直接退出")
                return 1

        QMessageBox.critical(None, "启动错误", f"{error_msg}\n\n详细信息请查看日志文件")
        return 1


if __name__ == "__main__":
    # 设置控制台编码
    if sys.platform.startswith('win'):
        try:
            import codecs
            sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
            sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())
        except:
            pass

    print("🚀 启动阴阳师自动化工具 v2.0...")
    exit_code = main()
    print(f"🏁 程序结束，退出代码: {exit_code}")
    sys.exit(exit_code)
