# 游戏界面网格映射配置指南

## 🎯 **功能概述**

游戏界面网格映射工具可以让您直接在游戏界面上调整3×3网格配置，实时查看网格覆盖效果，让配置过程变得直观和精确。

## 🚀 **启动方法**

### **方法一：从主界面启动**
1. 运行 `python main_gui_v2.py`
2. 切换到"🏰 结界突破"标签页
3. 点击"界面映射"按钮

### **方法二：直接启动**
```bash
python features/realm_raid/game_overlay_config.py
```

## 🎮 **使用步骤**

### **第1步：连接游戏窗口**
1. **启动阴阳师游戏**
2. **进入结界突破界面**
3. **点击"🔄 刷新游戏窗口"按钮**
4. **确认显示"✅ 已连接"状态**

### **第2步：调整网格区域**
使用左侧控制面板的滑块调整网格区域：

#### **网格区域滑块**
- **左边界**: 调整网格左边界位置 (0-100%)
- **上边界**: 调整网格上边界位置 (0-100%)
- **右边界**: 调整网格右边界位置 (0-100%)
- **下边界**: 调整网格下边界位置 (0-100%)

**💡 调整技巧**: 拖拽滑块时，右侧游戏界面会实时显示黄色边框，确保边框完全覆盖结界列表区域。

### **第3步：配置网格布局**
#### **网格布局参数**
- **行数**: 设置网格行数 (1-5)
- **列数**: 设置网格列数 (1-5)
- **水平间距**: 调整结界项之间的水平间距 (0-10%)
- **垂直间距**: 调整结界项之间的垂直间距 (0-10%)

### **第4步：调整结界项尺寸**
#### **结界项尺寸参数**
- **宽度**: 单个结界项的宽度 (5-50%)
- **高度**: 单个结界项的高度 (5-50%)

**💡 调整技巧**: 红色矩形应该完全覆盖每个结界图标，但不要过大导致重叠。

### **第5步：优化显示效果**
#### **显示选项**
- **✅ 显示网格区域边框**: 显示黄色网格区域边框
- **✅ 显示结界项**: 显示红色结界项矩形
- **✅ 显示索引编号**: 显示结界索引 (0-8)
- **✅ 显示中心点**: 显示蓝色中心点

### **第6步：保存配置**
1. **调整完成后点击"💾 保存配置"**
2. **配置保存到 `custom_grid_config.json`**
3. **重启程序后自动加载新配置**

## 🎨 **界面说明**

### **左侧控制面板**
```
┌─────────────────────────┐
│    网格配置控制面板      │
├─────────────────────────┤
│  🎮 游戏窗口            │
│  📐 网格区域 (滑块)     │
│  🗂️ 网格布局            │
│  📏 结界项尺寸          │
│  👁️ 显示选项            │
│  ⚙️ 操作按钮            │
└─────────────────────────┘
```

### **右侧游戏界面**
```
┌─────────────────────────────────┐
│        游戏界面实时预览          │
├─────────────────────────────────┤
│  ┌─────────────────────────┐    │
│  │     游戏截图显示         │    │
│  │   + 网格覆盖层          │    │
│  │   + 实时调整效果        │    │
│  └─────────────────────────┘    │
│         ✅ 实时显示状态          │
└─────────────────────────────────┘
```

## 🎯 **视觉指示说明**

### **颜色含义**
- **🟡 黄色边框**: 网格区域边界
- **🔴 红色矩形**: 结界项点击区域
- **🔵 蓝色圆点**: 结界项中心点
- **⚪ 白色数字**: 结界索引编号 (0-8)

### **网格索引布局**
```
┌─────┬─────┬─────┐
│  0  │  1  │  2  │
├─────┼─────┼─────┤
│  3  │  4  │  5  │
├─────┼─────┼─────┤
│  6  │  7  │  8  │
└─────┴─────┴─────┘
```

## 🔧 **调整技巧**

### **精确定位方法**
1. **粗调整**: 先调整网格区域，使黄色边框大致覆盖结界列表
2. **细调整**: 调整结界项尺寸，使红色矩形精确覆盖每个结界
3. **间距优化**: 调整间距，确保相邻结界不重叠
4. **验证检查**: 检查所有9个位置是否都正确覆盖

### **常见问题解决**
#### **❌ 网格位置偏移**
- 调整网格区域的左边界和上边界

#### **❌ 结界项过大/过小**
- 调整结界项的宽度和高度

#### **❌ 结界重叠**
- 增加水平或垂直间距
- 减小结界项尺寸

#### **❌ 超出屏幕范围**
- 调整网格区域右边界和下边界
- 减少行列数量

## 🎮 **实际使用流程**

### **配置前准备**
1. ✅ 启动阴阳师游戏
2. ✅ 进入结界突破界面
3. ✅ 确保界面完全显示
4. ✅ 启动映射配置工具

### **配置过程**
1. **连接游戏** → 点击"刷新游戏窗口"
2. **调整区域** → 拖拽滑块调整网格区域
3. **优化布局** → 设置行列数和间距
4. **精确尺寸** → 调整结界项宽高
5. **验证效果** → 检查所有位置覆盖
6. **保存配置** → 点击"保存配置"

### **配置后验证**
1. ✅ 重启程序
2. ✅ 运行结界突破功能
3. ✅ 观察点击位置是否准确
4. ✅ 检查选择策略是否正常

## 🚀 **高级功能**

### **自动刷新**
- 默认每1秒自动刷新游戏截图
- 可以取消"自动刷新截图"选项手动控制

### **测试点击**
- 点击"🎯 测试点击"按钮
- 系统会依次点击所有网格位置
- 验证配置的准确性

### **配置预设**
- 支持保存多套配置
- 快速切换不同分辨率的配置

## ⚠️ **注意事项**

### **使用要求**
- 游戏必须在前台运行
- 结界突破界面必须完全显示
- 不要在配置过程中切换游戏界面

### **性能优化**
- 配置完成后可以关闭映射工具
- 大分辨率下可能需要更多内存
- 建议在配置时关闭其他大型程序

## 🎊 **总结**

### ✅ **游戏界面映射的优势**
- **🎯 直观精确**: 直接在游戏界面上调整，所见即所得
- **⚡ 实时反馈**: 拖拽滑块立即看到效果
- **🎮 游戏适配**: 完美适配任何分辨率和界面布局
- **🔧 简单易用**: 无需复杂计算，拖拽即可完成配置

### 🚀 **开始使用**
现在您可以通过游戏界面映射工具，轻松配置出最适合您游戏界面的3×3网格了！

**点击"界面映射"按钮，开始直观配置吧！** 🎯
