import logging
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QTextEdit, QCheckBox,
                            QGroupBox, QSpinBox, QComboBox, QProgressBar,
                            QTabWidget, QFileDialog, QMessageBox, QInputDialog)
from PyQt5.QtCore import QThread, pyqtSignal, Qt, QTimer
from PyQt5.QtGui import QFont, QIcon
import traceback
from typing import Callable, List, Dict, Any, Optional, Tuple

class GUILogHandler(logging.Handler):
    """将日志重定向到GUI的处理器"""
    
    def __init__(self, signal):
        super().__init__()
        self.signal = signal
        
        # 设置格式器
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        self.setFormatter(formatter)
    
    def emit(self, record):
        log_message = self.format(record)
        self.signal.emit(log_message)

class WindowFinderWidget(QWidget):
    """窗口查找组件
    
    提供窗口查找和选择功能的可复用组件
    """
    
    # 定义信号
    window_selected = pyqtSignal(int, str)  # 参数为窗口句柄和标题
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.dragging = False
        self.drag_start_pos = None
        self.current_hwnd = None  # 存储当前选中的窗口句柄
        self.current_title = None  # 存储当前选中的窗口标题
        self.initUI()
    
    def initUI(self):
        # 主布局
        layout = QVBoxLayout()
        
        # 窗口标题组
        title_group = QGroupBox("游戏窗口")
        title_layout = QVBoxLayout()
        
        # 第一行：窗口标题输入和查找按钮
        title_row = QHBoxLayout()
        self.window_title_input = QLineEdit()
        self.window_title_input.setPlaceholderText("输入游戏窗口标题(例如: MuMu模拟器)")
        self.find_window_btn = QPushButton("查找窗口")
        self.find_window_btn.clicked.connect(self.findWindow)
        
        title_row.addWidget(QLabel("窗口标题:"))
        title_row.addWidget(self.window_title_input)
        title_row.addWidget(self.find_window_btn)
        title_layout.addLayout(title_row)
        
        # 第二行：窗口句柄输入和使用按钮
        hwnd_row = QHBoxLayout()
        self.hwnd_input = QLineEdit()
        self.hwnd_input.setPlaceholderText("输入窗口句柄数字")
        self.use_hwnd_btn = QPushButton("使用句柄")
        self.use_hwnd_btn.clicked.connect(self.useManualHwnd)
        
        hwnd_row.addWidget(QLabel("窗口句柄:"))
        hwnd_row.addWidget(self.hwnd_input)
        hwnd_row.addWidget(self.use_hwnd_btn)
        title_layout.addLayout(hwnd_row)
        
        # 第三行：拖动查找按钮
        drag_row = QHBoxLayout()
        self.setup_drag_finder_button()
        drag_row.addWidget(self.finder_label)
        drag_row.addWidget(self.drag_finder_btn)
        drag_row.addStretch()
        title_layout.addLayout(drag_row)
        
        # 窗口信息显示
        info_row = QHBoxLayout()
        self.window_info_label = QLabel("未选择窗口")
        info_row.addWidget(self.window_info_label)
        title_layout.addLayout(info_row)
        
        title_group.setLayout(title_layout)
        layout.addWidget(title_group)
        
        self.setLayout(layout)
    
    def setup_drag_finder_button(self):
        """设置拖动查找按钮"""
        self.drag_finder_btn = QPushButton("⊕", self)
        self.drag_finder_btn.setToolTip("按住拖动到目标窗口上")
        self.drag_finder_btn.setCursor(Qt.CrossCursor)

        # 初始样式
        self.drag_finder_btn.setStyleSheet("""
            QPushButton {
                background-color: white;
                color: black;
                border: 1px solid #666;
                border-radius: 0px;
                min-width: 24px;
                min-height: 24px;
                max-width: 24px;
                max-height: 24px;
                font-weight: bold;
                font-size: 16px;
            }
            QPushButton:hover {
                background-color: #f0f0f0;
            }
            QPushButton:pressed {
                background-color: #ddf;
                border: 1px solid #55f;
            }
        """)
        
        # 保存原始的mousePressEvent
        self.drag_finder_btn_original_mousePressEvent = self.drag_finder_btn.mousePressEvent
        # 替换为自定义的mousePressEvent
        self.drag_finder_btn.mousePressEvent = self.dragBtnMousePressEvent
        self.drag_finder_btn.mouseReleaseEvent = self.dragBtnMouseReleaseEvent
        self.drag_finder_btn.mouseMoveEvent = self.dragBtnMouseMoveEvent

        # 按钮标签
        self.finder_label = QLabel("拖动准星到窗口:", self)
    
    def dragBtnMousePressEvent(self, event):
        """拖动按钮鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            self.dragging = True
            self.drag_start_pos = event.pos()
            
            # 按下时改变样式
            self.drag_finder_btn.setStyleSheet("""
                QPushButton {
                    background-color: #ddf;
                    color: black;
                    border: 1px solid #55f;
                    border-radius: 0px;
                    min-width: 24px;
                    min-height: 24px;
                    max-width: 24px;
                    max-height: 24px;
                    font-weight: bold;
                    font-size: 16px;
                }
            """)
            event.accept()
        else:
            event.ignore()
    
    def dragBtnMouseMoveEvent(self, event):
        """鼠标移动事件处理"""
        if not self.dragging:
            return
            
        # 移动按钮位置（产生拖动效果）
        # 实际上按钮不会移动，但鼠标会被捕获以追踪拖动
        pass
    
    def dragBtnMouseReleaseEvent(self, event):
        """拖动按钮鼠标释放事件"""
        if event.button() == Qt.LeftButton and self.dragging:
            self.dragging = False
            
            # 恢复正常样式
            self.drag_finder_btn.setStyleSheet("""
                QPushButton {
                    background-color: white;
                    color: black;
                    border: 1px solid #666;
                    border-radius: 0px;
                    min-width: 24px;
                    min-height: 24px;
                    max-width: 24px;
                    max-height: 24px;
                    font-weight: bold;
                    font-size: 16px;
                }
                QPushButton:hover {
                    background-color: #f0f0f0;
                }
                QPushButton:pressed {
                    background-color: #ddf;
                    border: 1px solid #55f;
                }
            """)
            
            # 释放鼠标
            self.drag_finder_btn.releaseMouse()
            self.drag_finder_btn.setMouseTracking(False)
            
            # 获取当前鼠标位置下的窗口
            self.findWindowUnderCursor()
    
    def findWindowUnderCursor(self):
        """查找鼠标指针下的窗口"""
        import win32gui
        import win32api
        
        try:
            # 获取鼠标位置
            pos = win32api.GetCursorPos()
            
            # 获取鼠标位置下的窗口句柄
            hwnd = win32gui.WindowFromPoint(pos)
            
            # 获取窗口标题
            title = win32gui.GetWindowText(hwnd)
            
            # 判断是否为有效窗口
            if hwnd and title and win32gui.IsWindowVisible(hwnd):
                from core.window_utils import WindowUtils
                
                # 获取窗口信息
                info = WindowUtils.get_window_info(hwnd)
                
                if info:
                    # 更新输入框和信息显示
                    self.window_title_input.setText(title)
                    self.hwnd_input.setText(str(hwnd))
                    self.setWindowInfo(hwnd, title)
                    
                    # 显示成功消息
                    QMessageBox.information(self, "成功", f"已获取窗口信息:\n标题: {title}\n句柄: {hwnd}")
                    return
            
            # 如果未找到有效窗口
            QMessageBox.warning(self, "警告", "未找到有效窗口，请重试")
        except Exception as e:
            QMessageBox.warning(self, "错误", f"获取窗口信息失败: {str(e)}")
    
    def findWindow(self):
        """查找窗口"""
        from core.window_utils import WindowUtils
        
        title = self.window_title_input.text().strip()
        if not title:
            QMessageBox.warning(self, "警告", "请输入窗口标题")
            return
            
        # 查找所有匹配的窗口
        windows = WindowUtils.find_all_windows_by_title(title)
        
        if not windows:
            QMessageBox.warning(self, "警告", f"未找到标题包含 '{title}' 的窗口")
            return
            
        # 如果只找到一个窗口，直接使用
        if len(windows) == 1:
            hwnd, title = windows[0]
            self.setWindowInfo(hwnd, title)
            return
            
        # 如果找到多个窗口，显示选择对话框
        window_list = [f"{hwnd} - {title}" for hwnd, title in windows]
        selected, ok = QInputDialog.getItem(self, "选择窗口", "找到多个匹配窗口，请选择:", window_list, 0, False)
        
        if ok and selected:
            # 解析选择的窗口
            hwnd = int(selected.split(" - ")[0])
            title = " - ".join(selected.split(" - ")[1:])
            self.setWindowInfo(hwnd, title)
    
    def useManualHwnd(self):
        """使用手动输入的窗口句柄"""
        from core.window_utils import WindowUtils
        
        try:
            hwnd = int(self.hwnd_input.text().strip())
            
            # 获取窗口信息
            info = WindowUtils.get_window_info(hwnd)
            
            if not info:
                QMessageBox.warning(self, "警告", f"无效的窗口句柄: {hwnd}")
                return
                
            self.setWindowInfo(hwnd, info['title'])
        except ValueError:
            QMessageBox.warning(self, "警告", "窗口句柄必须是数字")
    
    def setWindowInfo(self, hwnd, title):
        """设置窗口信息"""
        from core.window_utils import WindowUtils

        info = WindowUtils.get_window_info(hwnd)
        if not info:
            return

        # 存储当前选中的窗口信息
        self.current_hwnd = hwnd
        self.current_title = title

        # 更新界面显示
        self.hwnd_input.setText(str(hwnd))
        width, height = info['size']
        self.window_info_label.setText(f"已选择窗口: {hwnd} - {title} ({width}x{height})")

        # 发送信号
        self.window_selected.emit(hwnd, title)

    def get_selected_hwnd(self):
        """获取当前选中的窗口句柄"""
        return self.current_hwnd

    def get_selected_title(self):
        """获取当前选中的窗口标题"""
        return self.current_title

    def is_window_selected(self):
        """检查是否已选择窗口"""
        return self.current_hwnd is not None

class LogViewWidget(QWidget):
    """日志查看组件
    
    提供日志显示和管理功能
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.log_file_path = "onmyoji_log.txt"
        self.max_log_size = 10  # MB
        self.max_log_age = 7    # 天
        self.initUI()
    
    def initUI(self):
        # 主布局
        layout = QVBoxLayout()
        
        # 日志显示区域
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        self.clear_log_btn = QPushButton("清空日志")
        self.clear_log_btn.clicked.connect(self.clearLog)
        button_layout.addWidget(self.clear_log_btn)
        
        self.refresh_log_btn = QPushButton("刷新日志")
        self.refresh_log_btn.clicked.connect(self.refreshLog)
        button_layout.addWidget(self.refresh_log_btn)
        
        self.save_log_btn = QPushButton("保存日志")
        self.save_log_btn.clicked.connect(self.saveLog)
        button_layout.addWidget(self.save_log_btn)
        
        layout.addLayout(button_layout)
        
        # 日志设置
        settings_group = QGroupBox("日志设置")
        settings_layout = QHBoxLayout()
        
        settings_layout.addWidget(QLabel("最大大小 (MB):"))
        self.max_size_spin = QSpinBox()
        self.max_size_spin.setRange(1, 100)
        self.max_size_spin.setValue(self.max_log_size)
        settings_layout.addWidget(self.max_size_spin)
        
        settings_layout.addWidget(QLabel("最大天数:"))
        self.max_age_spin = QSpinBox()
        self.max_age_spin.setRange(1, 365)
        self.max_age_spin.setValue(self.max_log_age)
        settings_layout.addWidget(self.max_age_spin)
        
        self.apply_settings_btn = QPushButton("应用设置")
        self.apply_settings_btn.clicked.connect(self.applySettings)
        settings_layout.addWidget(self.apply_settings_btn)
        
        settings_group.setLayout(settings_layout)
        layout.addWidget(settings_group)
        
        self.setLayout(layout)
        
        # 初始加载日志
        self.refreshLog()
    
    def clearLog(self):
        """清空日志"""
        reply = QMessageBox.question(self, '确认', '确定要清空日志文件吗？', 
                                    QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
                                    
        if reply == QMessageBox.Yes:
            try:
                # 清空日志文件
                with open(self.log_file_path, 'w', encoding='utf-8') as f:
                    import datetime
                    f.write(f"日志已清空 {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write("="*50 + "\n\n")
                
                # 刷新显示
                self.refreshLog()
                QMessageBox.information(self, "成功", "日志已清空")
            except Exception as e:
                QMessageBox.warning(self, "错误", f"清空日志失败: {str(e)}")
    
    def refreshLog(self):
        """刷新日志显示"""
        try:
            # 读取日志文件
            with open(self.log_file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 更新显示
            self.log_text.setText(content)
            
            # 滚动到底部
            self.log_text.moveCursor(self.log_text.textCursor().End)
        except Exception as e:
            self.log_text.setText(f"读取日志失败: {str(e)}")
    
    def saveLog(self):
        """保存日志到新文件"""
        try:
            # 选择保存路径
            file_name, _ = QFileDialog.getSaveFileName(self, "保存日志", "onmyoji_log_backup.txt", "文本文件 (*.txt)")
            
            if not file_name:
                return
                
            # 复制日志文件
            import shutil
            shutil.copy2(self.log_file_path, file_name)
            
            QMessageBox.information(self, "成功", f"日志已保存到: {file_name}")
        except Exception as e:
            QMessageBox.warning(self, "错误", f"保存日志失败: {str(e)}")
    
    def applySettings(self):
        """应用日志设置"""
        self.max_log_size = self.max_size_spin.value()
        self.max_log_age = self.max_age_spin.value()
        
        QMessageBox.information(self, "成功", "日志设置已更新")
    
    def addLog(self, message):
        """添加日志消息"""
        self.log_text.append(message)
        # 滚动到底部
        self.log_text.moveCursor(self.log_text.textCursor().End)

class StatsWidget(QWidget):
    """统计信息组件
    
    显示和管理统计数据
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.stats = {}  # 统计数据
        self.initUI()
    
    def initUI(self):
        # 主布局
        layout = QVBoxLayout()
        
        # 统计表格
        self.stats_group = QGroupBox("运行统计")
        stats_layout = QVBoxLayout()
        
        # 运行时间
        time_layout = QHBoxLayout()
        time_layout.addWidget(QLabel("运行时间:"))
        self.runtime_label = QLabel("00:00:00")
        self.runtime_label.setAlignment(Qt.AlignRight)
        time_layout.addWidget(self.runtime_label)
        stats_layout.addLayout(time_layout)
        
        # 统计项目将根据实际统计数据动态添加
        self.stats_items = {}
        
        self.stats_group.setLayout(stats_layout)
        layout.addWidget(self.stats_group)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        self.reset_stats_btn = QPushButton("重置统计")
        self.reset_stats_btn.clicked.connect(self.resetStats)
        button_layout.addWidget(self.reset_stats_btn)
        
        self.export_stats_btn = QPushButton("导出统计")
        self.export_stats_btn.clicked.connect(self.exportStats)
        button_layout.addWidget(self.export_stats_btn)
        
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
        
        # 创建定时器，每秒更新一次运行时间
        self.start_time = None
        self.timer = QTimer()
        self.timer.timeout.connect(self.updateRuntime)
    
    def startTracking(self):
        """开始跟踪运行时间"""
        import datetime
        self.start_time = datetime.datetime.now()
        self.timer.start(1000)  # 每秒更新一次
    
    def stopTracking(self):
        """停止跟踪运行时间"""
        self.timer.stop()
    
    def updateRuntime(self):
        """更新运行时间显示"""
        if not self.start_time:
            return
            
        import datetime
        elapsed = datetime.datetime.now() - self.start_time
        hours, remainder = divmod(elapsed.total_seconds(), 3600)
        minutes, seconds = divmod(remainder, 60)
        self.runtime_label.setText(f"{int(hours):02}:{int(minutes):02}:{int(seconds):02}")
    
    def updateStats(self, stats):
        """更新统计数据"""
        if not stats:
            return
            
        self.stats = stats
        
        # 更新或创建统计项
        stats_layout = self.stats_group.layout()
        
        for key, value in stats.items():
            if key not in self.stats_items:
                # 创建新的统计项
                item_layout = QHBoxLayout()
                item_layout.addWidget(QLabel(f"{key}:"))
                label = QLabel(str(value))
                label.setAlignment(Qt.AlignRight)
                item_layout.addWidget(label)
                stats_layout.addLayout(item_layout)
                self.stats_items[key] = label
            else:
                # 更新现有统计项
                self.stats_items[key].setText(str(value))
    
    def resetStats(self):
        """重置统计数据"""
        reply = QMessageBox.question(self, '确认', '确定要重置统计数据吗？', 
                                    QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
                                    
        if reply == QMessageBox.Yes:
            # 清空统计数据
            self.stats = {}
            
            # 更新显示
            for label in self.stats_items.values():
                label.setText("0")
            
            # 重置运行时间
            import datetime
            self.start_time = datetime.datetime.now()
            self.updateRuntime()
    
    def exportStats(self):
        """导出统计数据"""
        if not self.stats:
            QMessageBox.information(self, "提示", "当前没有统计数据可导出")
            return
            
        try:
            # 选择保存路径
            file_name, _ = QFileDialog.getSaveFileName(self, "导出统计", "onmyoji_stats.txt", "文本文件 (*.txt)")
            
            if not file_name:
                return
                
            # 保存统计数据
            with open(file_name, 'w', encoding='utf-8') as f:
                import datetime
                f.write(f"阴阳师自动化脚本统计数据 - {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("="*50 + "\n\n")
                
                # 运行时间
                if self.start_time:
                    elapsed = datetime.datetime.now() - self.start_time
                    hours, remainder = divmod(elapsed.total_seconds(), 3600)
                    minutes, seconds = divmod(remainder, 60)
                    f.write(f"运行时间: {int(hours):02}:{int(minutes):02}:{int(seconds):02}\n\n")
                
                # 统计项目
                for key, value in self.stats.items():
                    f.write(f"{key}: {value}\n")
            
            QMessageBox.information(self, "成功", f"统计数据已导出到: {file_name}")
        except Exception as e:
            QMessageBox.warning(self, "错误", f"导出统计数据失败: {str(e)}")

# 自定义消息对话框
def show_message(parent, title, message, icon=QMessageBox.Information):
    """显示消息对话框"""
    msg_box = QMessageBox(parent)
    msg_box.setWindowTitle(title)
    msg_box.setText(message)
    msg_box.setIcon(icon)
    msg_box.exec_()

# 自定义输入对话框
from PyQt5.QtWidgets import QInputDialog

def get_text_input(parent, title, label, default=""):
    """获取文本输入"""
    text, ok = QInputDialog.getText(parent, title, label, text=default)
    if ok and text:
        return text
    return None

def get_int_input(parent, title, label, default=0, min_val=0, max_val=100):
    """获取整数输入"""
    num, ok = QInputDialog.getInt(parent, title, label, default, min_val, max_val)
    if ok:
        return num
    return None 