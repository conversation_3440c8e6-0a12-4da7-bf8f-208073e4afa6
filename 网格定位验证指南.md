# 🎯 网格定位验证指南

## 📋 验证步骤

### **第一步：启动验证工具**
```bash
# 启动简单屏幕测试工具
python simple_screen_test.py
```

### **第二步：准备游戏界面**
1. **打开阴阳师游戏**
2. **进入结界突破界面**
   - 点击游戏主界面的"结界突破"
   - 确保显示结界卡片的网格界面
3. **调整游戏窗口**
   - 建议使用全屏或固定窗口大小
   - 确保界面完整显示

### **第三步：截取游戏界面**
1. **点击"📸 截取屏幕"按钮**
2. **工具会自动隐藏窗口并截图**
3. **截图完成后会显示在界面中**

### **第四步：验证网格定位**

#### **🟢 网格区域验证**
- **绿色边框**显示检测到的结界卡片区域
- **检查要点**：
  - 绿色边框是否完全包围所有结界卡片
  - 边框是否贴合卡片区域边缘
  - 是否有多余的空白区域被包含

#### **🔴 结界项验证**
- **红色方框**显示每个结界卡片的预期位置
- **检查要点**：
  - 每个红色方框是否准确覆盖对应的结界卡片
  - 方框大小是否与卡片大小匹配
  - 方框位置是否居中对齐卡片

#### **🔢 索引验证**
- **白色数字**显示每个位置的索引（0-8）
- **检查要点**：
  - 索引顺序是否正确（从左到右，从上到下）
  - 数字是否显示在对应卡片的左上角

## 🔧 调整配置

### **如果定位不准确**

#### **方法一：使用交互式配置**
1. **点击"🖱️ 交互式配置"按钮**
2. **在弹出的配置窗口中**：
   - 🖱️ **拖拽蓝色手柄**调整网格区域边界
   - 🖱️ **拖拽红色结界项**调整卡片尺寸
   - 🔄 **鼠标滚轮**缩放结界项大小
   - 🎛️ **使用滑块**微调间距
3. **点击"保存配置"**
4. **返回验证界面查看效果**

#### **方法二：手动调整参数**
编辑配置文件中的关键参数：

```json
{
  "grid_area": {
    "left": 0.12,    // 网格左边界（0.0-1.0）
    "top": 0.20,     // 网格上边界（0.0-1.0）
    "right": 0.88,   // 网格右边界（0.0-1.0）
    "bottom": 0.80   // 网格下边界（0.0-1.0）
  },
  "item_size": {
    "width": 0.22,   // 结界项宽度比例
    "height": 0.15   // 结界项高度比例
  },
  "layout": {
    "item_spacing": {
      "horizontal": 0.02,  // 水平间距比例
      "vertical": 0.03     // 垂直间距比例
    }
  }
}
```

## 📊 验证标准

### **✅ 定位准确的标志**
- 🟢 **网格区域**：绿色边框紧贴结界卡片区域
- 🔴 **结界项**：红色方框完全覆盖每张卡片
- 🎯 **居中对齐**：方框与卡片中心对齐
- 📏 **尺寸匹配**：方框大小与卡片大小一致

### **❌ 需要调整的情况**
- 🟢 **网格区域过大**：包含了过多空白区域
- 🟢 **网格区域过小**：未完全包含所有卡片
- 🔴 **结界项偏移**：红色方框与卡片位置不匹配
- 🔴 **结界项尺寸**：方框过大或过小

## 🎮 不同分辨率适配

### **常见分辨率配置**

#### **1920×1080 (推荐)**
```json
{
  "grid_area": {"left": 0.12, "top": 0.20, "right": 0.88, "bottom": 0.80},
  "item_size": {"width": 0.22, "height": 0.15}
}
```

#### **1366×768**
```json
{
  "grid_area": {"left": 0.10, "top": 0.18, "right": 0.90, "bottom": 0.82},
  "item_size": {"width": 0.24, "height": 0.16}
}
```

#### **2560×1440**
```json
{
  "grid_area": {"left": 0.13, "top": 0.21, "right": 0.87, "bottom": 0.79},
  "item_size": {"width": 0.21, "height": 0.14}
}
```

## 💾 保存验证结果

### **保存截图**
1. **点击"💾 保存截图"按钮**
2. **截图会保存为带时间戳的PNG文件**
3. **文件包含网格叠加层，便于后续查看**

### **保存配置**
1. **在交互式配置中调整完成后**
2. **点击"保存配置"按钮**
3. **配置会自动保存到配置文件**

## 🚀 验证完成后

### **应用到主程序**
1. **确认定位准确后**
2. **启动主程序**：`python main_gui_v2.py`
3. **切换到"🏰 结界突破"标签页**
4. **开始使用自动化功能**

### **实际测试**
1. **使用"测试点击"功能**
2. **观察是否准确点击到结界卡片**
3. **如有偏差，返回验证工具重新调整**

## ⚠️ 注意事项

### **环境要求**
- ✅ **游戏界面稳定**：确保游戏界面完全加载
- ✅ **窗口位置固定**：避免游戏窗口移动
- ✅ **分辨率一致**：保持游戏分辨率不变
- ✅ **界面干净**：关闭可能遮挡的弹窗

### **常见问题**
- **Q: 截图是黑屏？**
  - A: 某些游戏有截图保护，尝试窗口模式
- **Q: 网格位置完全错误？**
  - A: 检查游戏是否在正确的结界突破界面
- **Q: 配置保存后不生效？**
  - A: 重启主程序以加载新配置

## 🎯 最终目标

通过这个验证工具，您可以：
1. **📸 可视化验证**网格定位的准确性
2. **🖱️ 交互式调整**配置参数
3. **💾 保存验证结果**供后续参考
4. **🎮 确保自动化**功能的准确性

---

## 🎉 开始验证吧！

现在您可以：
1. **启动验证工具**：`python simple_screen_test.py`
2. **打开阴阳师游戏**并进入结界突破界面
3. **截取屏幕**并验证网格定位
4. **根据需要调整配置**
5. **保存验证结果**

**祝您验证顺利！** 🎮✨
