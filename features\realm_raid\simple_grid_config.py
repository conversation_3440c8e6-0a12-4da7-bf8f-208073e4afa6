#!/usr/bin/env python3
"""简化版网格配置工具"""

import sys
import json
from PyQt5.QtWidgets import (QApplication, QDialog, QVBoxLayout, QHBoxLayout,
                             QLabel, QSlider, QSpinBox, QPushButton, 
                             QGroupBox, QGridLayout, QMessageBox, QFrame)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QPainter, QPen, QBrush, QColor

class SimpleGridConfig(QDialog):
    """简化版网格配置对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.config = self.get_default_config()
        self.setup_ui()
        self.load_config()
    
    def setup_ui(self):
        """设置界面"""
        self.setWindowTitle("3×3网格配置 (简化版)")
        self.setFixedSize(500, 600)
        
        layout = QVBoxLayout()
        
        # 标题
        title = QLabel("结界突破 3×3网格配置")
        title.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50; margin: 10px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 说明
        info = QLabel("调整滑块来配置网格参数，右侧预览实时显示效果")
        info.setStyleSheet("color: #7f8c8d; margin: 5px;")
        info.setAlignment(Qt.AlignCenter)
        layout.addWidget(info)
        
        # 主要内容区域
        content_layout = QHBoxLayout()
        
        # 左侧控制面板
        control_panel = self.create_control_panel()
        content_layout.addWidget(control_panel, 1)
        
        # 右侧预览
        preview_panel = self.create_preview_panel()
        content_layout.addWidget(preview_panel, 1)
        
        layout.addLayout(content_layout)
        
        # 按钮区域
        button_layout = self.create_button_layout()
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
    
    def create_control_panel(self):
        """创建控制面板"""
        panel = QFrame()
        panel.setStyleSheet("border: 1px solid #bdc3c7; background-color: #ecf0f1; padding: 10px;")
        layout = QVBoxLayout()
        
        # 网格区域配置
        grid_area_group = self.create_grid_area_group()
        layout.addWidget(grid_area_group)
        
        # 网格布局配置
        grid_layout_group = self.create_grid_layout_group()
        layout.addWidget(grid_layout_group)
        
        # 结界项尺寸配置
        item_size_group = self.create_item_size_group()
        layout.addWidget(item_size_group)
        
        layout.addStretch()
        panel.setLayout(layout)
        return panel
    
    def create_grid_area_group(self):
        """创建网格区域配置组"""
        group = QGroupBox("网格区域 (百分比)")
        layout = QGridLayout()
        
        # 左边界
        layout.addWidget(QLabel("左:"), 0, 0)
        self.left_slider = QSlider(Qt.Horizontal)
        self.left_slider.setRange(0, 50)
        self.left_slider.setValue(12)
        self.left_slider.valueChanged.connect(self.update_config)
        layout.addWidget(self.left_slider, 0, 1)
        self.left_label = QLabel("12%")
        layout.addWidget(self.left_label, 0, 2)
        
        # 上边界
        layout.addWidget(QLabel("上:"), 1, 0)
        self.top_slider = QSlider(Qt.Horizontal)
        self.top_slider.setRange(0, 50)
        self.top_slider.setValue(20)
        self.top_slider.valueChanged.connect(self.update_config)
        layout.addWidget(self.top_slider, 1, 1)
        self.top_label = QLabel("20%")
        layout.addWidget(self.top_label, 1, 2)
        
        # 右边界
        layout.addWidget(QLabel("右:"), 2, 0)
        self.right_slider = QSlider(Qt.Horizontal)
        self.right_slider.setRange(50, 100)
        self.right_slider.setValue(88)
        self.right_slider.valueChanged.connect(self.update_config)
        layout.addWidget(self.right_slider, 2, 1)
        self.right_label = QLabel("88%")
        layout.addWidget(self.right_label, 2, 2)
        
        # 下边界
        layout.addWidget(QLabel("下:"), 3, 0)
        self.bottom_slider = QSlider(Qt.Horizontal)
        self.bottom_slider.setRange(50, 100)
        self.bottom_slider.setValue(80)
        self.bottom_slider.valueChanged.connect(self.update_config)
        layout.addWidget(self.bottom_slider, 3, 1)
        self.bottom_label = QLabel("80%")
        layout.addWidget(self.bottom_label, 3, 2)
        
        group.setLayout(layout)
        return group
    
    def create_grid_layout_group(self):
        """创建网格布局配置组"""
        group = QGroupBox("网格布局")
        layout = QGridLayout()
        
        # 行数
        layout.addWidget(QLabel("行数:"), 0, 0)
        self.rows_spin = QSpinBox()
        self.rows_spin.setRange(1, 5)
        self.rows_spin.setValue(3)
        self.rows_spin.valueChanged.connect(self.update_config)
        layout.addWidget(self.rows_spin, 0, 1)
        
        # 列数
        layout.addWidget(QLabel("列数:"), 0, 2)
        self.columns_spin = QSpinBox()
        self.columns_spin.setRange(1, 5)
        self.columns_spin.setValue(3)
        self.columns_spin.valueChanged.connect(self.update_config)
        layout.addWidget(self.columns_spin, 0, 3)
        
        # 水平间距
        layout.addWidget(QLabel("水平间距:"), 1, 0)
        self.h_spacing_slider = QSlider(Qt.Horizontal)
        self.h_spacing_slider.setRange(0, 10)
        self.h_spacing_slider.setValue(2)
        self.h_spacing_slider.valueChanged.connect(self.update_config)
        layout.addWidget(self.h_spacing_slider, 1, 1)
        self.h_spacing_label = QLabel("2%")
        layout.addWidget(self.h_spacing_label, 1, 2)
        
        # 垂直间距
        layout.addWidget(QLabel("垂直间距:"), 2, 0)
        self.v_spacing_slider = QSlider(Qt.Horizontal)
        self.v_spacing_slider.setRange(0, 10)
        self.v_spacing_slider.setValue(3)
        self.v_spacing_slider.valueChanged.connect(self.update_config)
        layout.addWidget(self.v_spacing_slider, 2, 1)
        self.v_spacing_label = QLabel("3%")
        layout.addWidget(self.v_spacing_label, 2, 2)
        
        group.setLayout(layout)
        return group
    
    def create_item_size_group(self):
        """创建结界项尺寸配置组"""
        group = QGroupBox("结界项尺寸")
        layout = QGridLayout()
        
        # 宽度
        layout.addWidget(QLabel("宽度:"), 0, 0)
        self.width_slider = QSlider(Qt.Horizontal)
        self.width_slider.setRange(10, 40)
        self.width_slider.setValue(22)
        self.width_slider.valueChanged.connect(self.update_config)
        layout.addWidget(self.width_slider, 0, 1)
        self.width_label = QLabel("22%")
        layout.addWidget(self.width_label, 0, 2)
        
        # 高度
        layout.addWidget(QLabel("高度:"), 1, 0)
        self.height_slider = QSlider(Qt.Horizontal)
        self.height_slider.setRange(10, 30)
        self.height_slider.setValue(15)
        self.height_slider.valueChanged.connect(self.update_config)
        layout.addWidget(self.height_slider, 1, 1)
        self.height_label = QLabel("15%")
        layout.addWidget(self.height_label, 1, 2)
        
        group.setLayout(layout)
        return group
    
    def create_preview_panel(self):
        """创建预览面板"""
        panel = QFrame()
        panel.setStyleSheet("border: 1px solid #34495e; background-color: #2c3e50;")
        layout = QVBoxLayout()
        
        # 标题
        title = QLabel("预览")
        title.setStyleSheet("color: white; font-weight: bold; padding: 5px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 预览组件
        self.preview_widget = GridPreviewWidget()
        self.preview_widget.setFixedSize(200, 150)
        layout.addWidget(self.preview_widget)
        
        # 说明
        info = QLabel("蓝色边框: 网格区域\n红色矩形: 结界位置\n数字: 索引编号")
        info.setStyleSheet("color: #bdc3c7; font-size: 10px; padding: 5px;")
        layout.addWidget(info)
        
        layout.addStretch()
        panel.setLayout(layout)
        return panel
    
    def create_button_layout(self):
        """创建按钮布局"""
        layout = QHBoxLayout()
        
        # 重置按钮
        reset_btn = QPushButton("重置默认")
        reset_btn.clicked.connect(self.reset_to_default)
        layout.addWidget(reset_btn)
        
        layout.addStretch()
        
        # 取消按钮
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        layout.addWidget(cancel_btn)
        
        # 确定按钮
        ok_btn = QPushButton("保存配置")
        ok_btn.setStyleSheet("QPushButton { background-color: #27ae60; color: white; font-weight: bold; padding: 8px; }")
        ok_btn.clicked.connect(self.save_and_accept)
        layout.addWidget(ok_btn)
        
        return layout
    
    def get_default_config(self):
        """获取默认配置"""
        return {
            "grid_area": {
                "left": 0.12,
                "top": 0.20,
                "right": 0.88,
                "bottom": 0.80
            },
            "layout": {
                "rows": 3,
                "columns": 3,
                "item_spacing": {
                    "horizontal": 0.02,
                    "vertical": 0.03
                }
            },
            "item_size": {
                "width": 0.22,
                "height": 0.15
            }
        }
    
    def load_config(self):
        """加载配置到界面"""
        config = self.config
        
        # 网格区域
        self.left_slider.setValue(int(config["grid_area"]["left"] * 100))
        self.top_slider.setValue(int(config["grid_area"]["top"] * 100))
        self.right_slider.setValue(int(config["grid_area"]["right"] * 100))
        self.bottom_slider.setValue(int(config["grid_area"]["bottom"] * 100))
        
        # 网格布局
        self.rows_spin.setValue(config["layout"]["rows"])
        self.columns_spin.setValue(config["layout"]["columns"])
        self.h_spacing_slider.setValue(int(config["layout"]["item_spacing"]["horizontal"] * 100))
        self.v_spacing_slider.setValue(int(config["layout"]["item_spacing"]["vertical"] * 100))
        
        # 结界项尺寸
        self.width_slider.setValue(int(config["item_size"]["width"] * 100))
        self.height_slider.setValue(int(config["item_size"]["height"] * 100))
        
        self.update_config()
    
    def update_config(self):
        """更新配置"""
        self.config = {
            "grid_area": {
                "left": self.left_slider.value() / 100.0,
                "top": self.top_slider.value() / 100.0,
                "right": self.right_slider.value() / 100.0,
                "bottom": self.bottom_slider.value() / 100.0
            },
            "layout": {
                "rows": self.rows_spin.value(),
                "columns": self.columns_spin.value(),
                "item_spacing": {
                    "horizontal": self.h_spacing_slider.value() / 100.0,
                    "vertical": self.v_spacing_slider.value() / 100.0
                }
            },
            "item_size": {
                "width": self.width_slider.value() / 100.0,
                "height": self.height_slider.value() / 100.0
            }
        }
        
        # 更新标签显示
        self.left_label.setText(f"{self.left_slider.value()}%")
        self.top_label.setText(f"{self.top_slider.value()}%")
        self.right_label.setText(f"{self.right_slider.value()}%")
        self.bottom_label.setText(f"{self.bottom_slider.value()}%")
        self.h_spacing_label.setText(f"{self.h_spacing_slider.value()}%")
        self.v_spacing_label.setText(f"{self.v_spacing_slider.value()}%")
        self.width_label.setText(f"{self.width_slider.value()}%")
        self.height_label.setText(f"{self.height_slider.value()}%")
        
        # 更新预览
        self.preview_widget.update_config(self.config)
    
    def reset_to_default(self):
        """重置为默认配置"""
        self.config = self.get_default_config()
        self.load_config()
    
    def save_and_accept(self):
        """保存配置并接受"""
        try:
            # 保存到JSON文件
            with open("custom_grid_config.json", 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            
            QMessageBox.information(self, "保存成功", 
                "网格配置已保存！\n文件: custom_grid_config.json\n重启程序后生效。")
            self.accept()
        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"保存配置失败: {e}")
    
    def get_config(self):
        """获取当前配置"""
        return self.config

class GridPreviewWidget(QFrame):
    """网格预览组件"""
    
    def __init__(self):
        super().__init__()
        self.config = None
        self.setStyleSheet("border: 1px solid #7f8c8d; background-color: #34495e;")
    
    def update_config(self, config):
        """更新配置"""
        self.config = config
        self.update()
    
    def paintEvent(self, event):
        """绘制预览"""
        super().paintEvent(event)
        
        if not self.config:
            return
        
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 获取组件尺寸
        width = self.width()
        height = self.height()
        
        # 绘制网格区域
        grid_area = self.config["grid_area"]
        grid_left = int(grid_area["left"] * width)
        grid_top = int(grid_area["top"] * height)
        grid_right = int(grid_area["right"] * width)
        grid_bottom = int(grid_area["bottom"] * height)
        
        # 绘制网格区域边框
        painter.setPen(QPen(QColor("#3498db"), 2))
        painter.drawRect(grid_left, grid_top, grid_right - grid_left, grid_bottom - grid_top)
        
        # 绘制结界项
        layout = self.config["layout"]
        item_size = self.config["item_size"]
        
        item_width = int(item_size["width"] * width)
        item_height = int(item_size["height"] * height)
        spacing_h = int(layout["item_spacing"]["horizontal"] * width)
        spacing_v = int(layout["item_spacing"]["vertical"] * height)
        
        painter.setPen(QPen(QColor("#e74c3c"), 1))
        painter.setBrush(QBrush(QColor("#e74c3c"), Qt.SolidPattern))
        
        for row in range(layout["rows"]):
            for col in range(layout["columns"]):
                x = grid_left + col * (item_width + spacing_h)
                y = grid_top + row * (item_height + spacing_v)
                
                painter.drawRect(x, y, item_width, item_height)
                
                # 绘制索引
                painter.setPen(QPen(QColor("white"), 1))
                index = row * layout["columns"] + col
                painter.drawText(x + item_width//2 - 5, y + item_height//2 + 5, str(index))
                painter.setPen(QPen(QColor("#e74c3c"), 1))

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    dialog = SimpleGridConfig()
    if dialog.exec_() == QDialog.Accepted:
        config = dialog.get_config()
        print("保存的配置:")
        print(json.dumps(config, indent=2, ensure_ascii=False))
    
    sys.exit()

if __name__ == "__main__":
    main()
