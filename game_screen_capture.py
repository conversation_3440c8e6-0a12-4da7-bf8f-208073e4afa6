#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""游戏界面截图和定位验证工具"""

import sys
import os
import time
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                                QHBoxLayout, QPushButton, QLabel, QTextEdit,
                                QGroupBox, QCheckBox, QSpinBox, QComboBox,
                                QFileDialog, QMessageBox, QProgressBar)
    from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread
    from PyQt5.QtGui import QPixmap, QFont, QPainter, QPen, QColor, QBrush
    import cv2
    import numpy as np
    from PIL import Image, ImageGrab
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请安装依赖: pip install PyQt5 opencv-python pillow")
    sys.exit(1)

class ScreenCaptureThread(QThread):
    """截图线程"""
    captured = pyqtSignal(object)  # 截图完成信号
    
    def __init__(self, window_title="阴阳师-网易游戏"):
        super().__init__()
        self.window_title = window_title
        self.running = False
    
    def run(self):
        """执行截图"""
        try:
            # 尝试截取指定窗口
            screenshot = self.capture_game_window()
            if screenshot is not None:
                self.captured.emit(screenshot)
            else:
                # 如果找不到游戏窗口，截取全屏
                screenshot = self.capture_full_screen()
                self.captured.emit(screenshot)
        except Exception as e:
            print(f"❌ 截图失败: {e}")
    
    def capture_game_window(self):
        """截取游戏窗口"""
        try:
            import win32gui
            import win32ui
            import win32con
            
            # 查找游戏窗口
            hwnd = win32gui.FindWindow(None, self.window_title)
            if hwnd == 0:
                print(f"未找到窗口: {self.window_title}")
                return None
            
            # 获取窗口尺寸
            left, top, right, bottom = win32gui.GetWindowRect(hwnd)
            width = right - left
            height = bottom - top
            
            # 截图
            hwndDC = win32gui.GetWindowDC(hwnd)
            mfcDC = win32ui.CreateDCFromHandle(hwndDC)
            saveDC = mfcDC.CreateCompatibleDC()
            saveBitMap = win32ui.CreateBitmap()
            saveBitMap.CreateCompatibleBitmap(mfcDC, width, height)
            saveDC.SelectObject(saveBitMap)
            saveDC.BitBlt((0, 0), (width, height), mfcDC, (0, 0), win32con.SRCCOPY)
            
            # 转换为numpy数组
            bmpinfo = saveBitMap.GetInfo()
            bmpstr = saveBitMap.GetBitmapBits(True)
            img = np.frombuffer(bmpstr, dtype='uint8')
            img.shape = (height, width, 4)
            img = cv2.cvtColor(img, cv2.COLOR_BGRA2RGB)
            
            # 清理资源
            win32gui.DeleteObject(saveBitMap.GetHandle())
            saveDC.DeleteDC()
            mfcDC.DeleteDC()
            win32gui.ReleaseDC(hwnd, hwndDC)
            
            return img
            
        except ImportError:
            print("❌ 缺少win32gui模块，使用全屏截图")
            return None
        except Exception as e:
            print(f"❌ 窗口截图失败: {e}")
            return None
    
    def capture_full_screen(self):
        """截取全屏"""
        try:
            screenshot = ImageGrab.grab()
            return np.array(screenshot)
        except Exception as e:
            print(f"❌ 全屏截图失败: {e}")
            return None

class GridOverlayWidget(QWidget):
    """网格叠加显示组件"""
    
    def __init__(self):
        super().__init__()
        self.screenshot = None
        self.grid_config = None
        self.show_grid = True
        self.show_handles = True
        self.show_items = True
        self.setMinimumSize(800, 600)
    
    def set_screenshot(self, screenshot):
        """设置截图"""
        self.screenshot = screenshot
        self.update()
    
    def set_grid_config(self, config):
        """设置网格配置"""
        self.grid_config = config
        self.update()
    
    def paintEvent(self, event):
        """绘制事件"""
        painter = QPainter(self)
        
        if self.screenshot is not None:
            # 绘制截图
            height, width = self.screenshot.shape[:2]
            
            # 缩放截图以适应组件大小
            widget_width = self.width()
            widget_height = self.height()
            
            scale_x = widget_width / width
            scale_y = widget_height / height
            scale = min(scale_x, scale_y)
            
            scaled_width = int(width * scale)
            scaled_height = int(height * scale)
            
            # 居中显示
            x_offset = (widget_width - scaled_width) // 2
            y_offset = (widget_height - scaled_height) // 2
            
            # 转换为QPixmap并绘制
            from PyQt5.QtGui import QImage
            h, w, ch = self.screenshot.shape
            bytes_per_line = ch * w
            qt_image = QImage(self.screenshot.data, w, h, bytes_per_line, QImage.Format_RGB888)
            pixmap = QPixmap.fromImage(qt_image)
            scaled_pixmap = pixmap.scaled(scaled_width, scaled_height, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            
            painter.drawPixmap(x_offset, y_offset, scaled_pixmap)
            
            # 绘制网格叠加
            if self.grid_config and (self.show_grid or self.show_handles or self.show_items):
                self.draw_grid_overlay(painter, x_offset, y_offset, scaled_width, scaled_height)
    
    def draw_grid_overlay(self, painter, x_offset, y_offset, width, height):
        """绘制网格叠加"""
        config = self.grid_config
        grid_area = config["grid_area"]
        
        # 计算网格区域
        grid_left = x_offset + int(grid_area["left"] * width)
        grid_top = y_offset + int(grid_area["top"] * height)
        grid_right = x_offset + int(grid_area["right"] * width)
        grid_bottom = y_offset + int(grid_area["bottom"] * height)
        
        if self.show_grid:
            # 绘制网格区域边框
            painter.setPen(QPen(QColor("#00ff00"), 3))  # 绿色边框
            painter.setBrush(QBrush(QColor(0, 255, 0, 50)))  # 半透明绿色填充
            painter.drawRect(grid_left, grid_top, grid_right - grid_left, grid_bottom - grid_top)
        
        if self.show_handles:
            # 绘制拖拽手柄
            handle_size = 12
            painter.setPen(QPen(QColor("#0080ff"), 2))
            painter.setBrush(QBrush(QColor("#0080ff")))
            
            # 四个边界手柄
            painter.drawEllipse(grid_left - handle_size//2, (grid_top + grid_bottom)//2 - handle_size//2, handle_size, handle_size)
            painter.drawEllipse(grid_right - handle_size//2, (grid_top + grid_bottom)//2 - handle_size//2, handle_size, handle_size)
            painter.drawEllipse((grid_left + grid_right)//2 - handle_size//2, grid_top - handle_size//2, handle_size, handle_size)
            painter.drawEllipse((grid_left + grid_right)//2 - handle_size//2, grid_bottom - handle_size//2, handle_size, handle_size)
        
        if self.show_items:
            # 绘制结界项
            layout = config["layout"]
            item_size = config["item_size"]
            
            grid_width = grid_right - grid_left
            grid_height = grid_bottom - grid_top
            
            item_width = int(item_size["width"] * grid_width)
            item_height = int(item_size["height"] * grid_height)
            spacing_h = int(layout["item_spacing"]["horizontal"] * grid_width)
            spacing_v = int(layout["item_spacing"]["vertical"] * grid_height)
            
            painter.setPen(QPen(QColor("#ff0000"), 2))  # 红色边框
            painter.setBrush(QBrush(QColor(255, 0, 0, 100)))  # 半透明红色填充
            
            for row in range(layout["rows"]):
                for col in range(layout["columns"]):
                    x = grid_left + col * (item_width + spacing_h)
                    y = grid_top + row * (item_height + spacing_v)
                    
                    painter.drawRect(x, y, item_width, item_height)
                    
                    # 绘制索引
                    painter.setPen(QPen(QColor("#ffffff"), 2))
                    painter.setFont(QFont("Arial", 10, QFont.Bold))
                    index = row * layout["columns"] + col
                    painter.drawText(x + 5, y + 15, str(index))

class GameScreenCaptureGUI(QMainWindow):
    """游戏界面截图和定位验证主界面"""
    
    def __init__(self):
        super().__init__()
        self.screenshot = None
        self.grid_config = None
        self.setup_ui()
        self.load_default_config()
    
    def setup_ui(self):
        """设置界面"""
        self.setWindowTitle("🎮 游戏界面截图和定位验证工具")
        self.setGeometry(100, 100, 1200, 800)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QHBoxLayout()
        central_widget.setLayout(layout)
        
        # 左侧控制面板
        self.setup_control_panel(layout)
        
        # 右侧显示区域
        self.setup_display_area(layout)
    
    def setup_control_panel(self, parent_layout):
        """设置控制面板"""
        control_widget = QWidget()
        control_widget.setFixedWidth(300)
        control_layout = QVBoxLayout()
        control_widget.setLayout(control_layout)
        
        # 截图控制
        capture_group = QGroupBox("📸 截图控制")
        capture_layout = QVBoxLayout()
        capture_group.setLayout(capture_layout)
        
        self.capture_btn = QPushButton("📸 截取游戏界面")
        self.capture_btn.clicked.connect(self.capture_screenshot)
        capture_layout.addWidget(self.capture_btn)
        
        self.save_btn = QPushButton("💾 保存截图")
        self.save_btn.clicked.connect(self.save_screenshot)
        self.save_btn.setEnabled(False)
        capture_layout.addWidget(self.save_btn)
        
        control_layout.addWidget(capture_group)
        
        # 显示控制
        display_group = QGroupBox("👁️ 显示控制")
        display_layout = QVBoxLayout()
        display_group.setLayout(display_layout)
        
        self.show_grid_cb = QCheckBox("显示网格区域")
        self.show_grid_cb.setChecked(True)
        self.show_grid_cb.toggled.connect(self.update_display)
        display_layout.addWidget(self.show_grid_cb)
        
        self.show_handles_cb = QCheckBox("显示拖拽手柄")
        self.show_handles_cb.setChecked(True)
        self.show_handles_cb.toggled.connect(self.update_display)
        display_layout.addWidget(self.show_handles_cb)
        
        self.show_items_cb = QCheckBox("显示结界项")
        self.show_items_cb.setChecked(True)
        self.show_items_cb.toggled.connect(self.update_display)
        display_layout.addWidget(self.show_items_cb)
        
        control_layout.addWidget(display_group)
        
        # 配置控制
        config_group = QGroupBox("⚙️ 配置控制")
        config_layout = QVBoxLayout()
        config_group.setLayout(config_layout)
        
        self.load_config_btn = QPushButton("📂 加载配置")
        self.load_config_btn.clicked.connect(self.load_config)
        config_layout.addWidget(self.load_config_btn)
        
        self.open_interactive_btn = QPushButton("🖱️ 打开交互式配置")
        self.open_interactive_btn.clicked.connect(self.open_interactive_config)
        config_layout.addWidget(self.open_interactive_btn)
        
        control_layout.addWidget(config_group)
        
        # 状态信息
        status_group = QGroupBox("📊 状态信息")
        status_layout = QVBoxLayout()
        status_group.setLayout(status_layout)
        
        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(150)
        self.status_text.setReadOnly(True)
        status_layout.addWidget(self.status_text)
        
        control_layout.addWidget(status_group)
        
        control_layout.addStretch()
        parent_layout.addWidget(control_widget)
    
    def setup_display_area(self, parent_layout):
        """设置显示区域"""
        self.display_widget = GridOverlayWidget()
        parent_layout.addWidget(self.display_widget)
    
    def load_default_config(self):
        """加载默认配置"""
        try:
            from features.realm_raid.realm_raid_config import REALM_GRID_CONFIG
            self.grid_config = REALM_GRID_CONFIG
            self.display_widget.set_grid_config(self.grid_config)
            self.log_status("✅ 已加载默认网格配置")
        except Exception as e:
            self.log_status(f"❌ 加载默认配置失败: {e}")
    
    def capture_screenshot(self):
        """截取游戏界面"""
        self.log_status("📸 开始截取游戏界面...")
        self.capture_btn.setEnabled(False)
        
        # 创建截图线程
        self.capture_thread = ScreenCaptureThread()
        self.capture_thread.captured.connect(self.on_screenshot_captured)
        self.capture_thread.start()
    
    def on_screenshot_captured(self, screenshot):
        """截图完成回调"""
        if screenshot is not None:
            self.screenshot = screenshot
            self.display_widget.set_screenshot(screenshot)
            self.save_btn.setEnabled(True)
            
            height, width = screenshot.shape[:2]
            self.log_status(f"✅ 截图成功! 尺寸: {width}×{height}")
        else:
            self.log_status("❌ 截图失败")
        
        self.capture_btn.setEnabled(True)
    
    def save_screenshot(self):
        """保存截图"""
        if self.screenshot is None:
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"game_screenshot_{timestamp}.png"
        
        try:
            cv2.imwrite(filename, cv2.cvtColor(self.screenshot, cv2.COLOR_RGB2BGR))
            self.log_status(f"💾 截图已保存: {filename}")
        except Exception as e:
            self.log_status(f"❌ 保存失败: {e}")
    
    def load_config(self):
        """加载配置文件"""
        filename, _ = QFileDialog.getOpenFileName(
            self, "选择配置文件", "", "JSON文件 (*.json);;所有文件 (*)"
        )
        
        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    self.grid_config = json.load(f)
                self.display_widget.set_grid_config(self.grid_config)
                self.log_status(f"✅ 已加载配置: {filename}")
            except Exception as e:
                self.log_status(f"❌ 加载配置失败: {e}")
    
    def open_interactive_config(self):
        """打开交互式配置"""
        try:
            from features.realm_raid.interactive_grid_config import InteractiveGridConfigDialog
            
            if self.grid_config is None:
                self.load_default_config()
            
            dialog = InteractiveGridConfigDialog(self, self.grid_config)
            if dialog.exec_() == dialog.Accepted:
                self.grid_config = dialog.get_config()
                self.display_widget.set_grid_config(self.grid_config)
                self.log_status("✅ 交互式配置已更新")
        except Exception as e:
            self.log_status(f"❌ 打开交互式配置失败: {e}")
    
    def update_display(self):
        """更新显示"""
        self.display_widget.show_grid = self.show_grid_cb.isChecked()
        self.display_widget.show_handles = self.show_handles_cb.isChecked()
        self.display_widget.show_items = self.show_items_cb.isChecked()
        self.display_widget.update()
    
    def log_status(self, message):
        """记录状态信息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.status_text.append(f"[{timestamp}] {message}")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setApplicationName("游戏界面截图和定位验证工具")
    
    window = GameScreenCaptureGUI()
    window.show()
    
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
