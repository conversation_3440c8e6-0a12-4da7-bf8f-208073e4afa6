
"""结界突破GUI界面 - 简化版"""

import logging
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QCheckBox, QGroupBox,
                            QSpinBox, QComboBox, QMessageBox,
                            QSlider, QRadioButton, QButtonGroup)
from PyQt5.QtCore import QThread, pyqtSignal
import traceback
import json
import os

from features.realm_raid.realm_raid_bot import RealmRaidBot


class RealmRaidThread(QThread):
    """后台运行结界突破脚本的线程"""
    
    update_signal = pyqtSignal(str)  # 更新日志信号
    stats_signal = pyqtSignal(dict)  # 更新统计数据信号
    
    def __init__(self, bot, parent=None):
        super().__init__(parent)
        self.bot = bot
        self.running = False
    
    def run(self):
        """运行线程"""
        self.running = True
        try:
            logging.info("🧵 结界突破后台线程开始运行")
            self.update_signal.emit("🚀 开始结界突破...")
            self.bot.start_realm_raid()
            logging.info("✅ 结界突破流程正常结束")
        except Exception as e:
            error_msg = f"结界突破线程发生错误: {str(e)}"
            logging.error(f"❌ {error_msg}")
            logging.error(f"错误详情: {traceback.format_exc()}")
            self.update_signal.emit(f"❌ 发生错误: {str(e)}")
        finally:
            self.running = False
            logging.info("🧵 结界突破后台线程结束")
            self.update_signal.emit("🛑 结界突破停止")
    
    def stop(self):
        """停止线程"""
        logging.info("🛑 用户请求停止结界突破线程")
        self.running = False
        if self.bot:
            self.bot.running = False
        self.update_signal.emit("🛑 停止结界突破")


class RealmRaidGUI(QWidget):
    """结界突破功能的GUI组件 - 简化版"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.realm_raid_bot = None
        self.raid_thread = None
        self.is_running = False
        self.setup_ui()
        logging.info("🎮 结界突破GUI组件初始化完成")
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # 突破设置组
        settings_group = QGroupBox("突破设置")
        settings_layout = QVBoxLayout()
        settings_group.setLayout(settings_layout)
        
        # 突破次数设置
        count_layout = QHBoxLayout()
        count_layout.addWidget(QLabel("突破次数:"))
        self.raid_count_spin = QSpinBox()
        self.raid_count_spin.setRange(1, 1000)
        self.raid_count_spin.setValue(50)
        count_layout.addWidget(self.raid_count_spin)
        count_layout.addStretch()
        settings_layout.addLayout(count_layout)
        
        # 选择策略
        strategy_layout = QHBoxLayout()
        strategy_layout.addWidget(QLabel("选择策略:"))
        self.strategy_combo = QComboBox()
        self.strategy_combo.addItems([
            "智能网格", "随机选择", "左上优先",
            "中心向外", "角落优先"
        ])
        self.strategy_combo.setCurrentText("智能网格")
        strategy_layout.addWidget(self.strategy_combo)

        # 网格配置按钮
        self.grid_config_btn = QPushButton("数值配置")
        self.grid_config_btn.setFixedWidth(80)
        self.grid_config_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 5px;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        self.grid_config_btn.clicked.connect(self.open_grid_config)
        strategy_layout.addWidget(self.grid_config_btn)

        # 交互式网格配置按钮
        self.interactive_config_btn = QPushButton("🖱️ 拖拽配置")
        self.interactive_config_btn.setFixedWidth(90)
        self.interactive_config_btn.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                border: none;
                padding: 5px;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """)
        self.interactive_config_btn.clicked.connect(self.open_interactive_grid_config)
        strategy_layout.addWidget(self.interactive_config_btn)

        # 游戏界面映射按钮
        self.overlay_config_btn = QPushButton("界面映射")
        self.overlay_config_btn.setFixedWidth(80)
        self.overlay_config_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 5px;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        self.overlay_config_btn.clicked.connect(self.open_overlay_config)
        strategy_layout.addWidget(self.overlay_config_btn)

        strategy_layout.addStretch()
        settings_layout.addLayout(strategy_layout)
        
        # 失败处理
        failure_layout = QHBoxLayout()
        self.retry_on_defeat_check = QCheckBox("失败后重试")
        self.retry_on_defeat_check.setChecked(True)
        failure_layout.addWidget(self.retry_on_defeat_check)
        
        self.skip_cooldown_check = QCheckBox("跳过冷却结界")
        self.skip_cooldown_check.setChecked(True)
        failure_layout.addWidget(self.skip_cooldown_check)
        failure_layout.addStretch()
        settings_layout.addLayout(failure_layout)
        
        layout.addWidget(settings_group)
        
        # 高级设置组
        advanced_group = QGroupBox("高级设置")
        advanced_layout = QVBoxLayout()
        advanced_group.setLayout(advanced_layout)
        
        # 战斗设置
        battle_layout = QHBoxLayout()
        self.auto_battle_check = QCheckBox("自动战斗")
        self.auto_battle_check.setChecked(True)
        battle_layout.addWidget(self.auto_battle_check)
        
        self.battle_timeout_spin = QSpinBox()
        self.battle_timeout_spin.setRange(60, 600)
        self.battle_timeout_spin.setValue(180)
        self.battle_timeout_spin.setSuffix(" 秒")
        battle_layout.addWidget(QLabel("战斗超时:"))
        battle_layout.addWidget(self.battle_timeout_spin)
        battle_layout.addStretch()
        advanced_layout.addLayout(battle_layout)
        
        # 延迟设置
        delay_layout = QHBoxLayout()
        delay_layout.addWidget(QLabel("随机延迟:"))
        
        self.delay_min_spin = QSpinBox()
        self.delay_min_spin.setRange(500, 10000)
        self.delay_min_spin.setValue(1000)
        self.delay_min_spin.setSuffix(" 毫秒")
        delay_layout.addWidget(self.delay_min_spin)
        
        delay_layout.addWidget(QLabel(" - "))
        
        self.delay_max_spin = QSpinBox()
        self.delay_max_spin.setRange(1000, 15000)
        self.delay_max_spin.setValue(3000)
        self.delay_max_spin.setSuffix(" 毫秒")
        delay_layout.addWidget(self.delay_max_spin)
        delay_layout.addStretch()
        advanced_layout.addLayout(delay_layout)
        
        # 休息设置
        break_layout = QHBoxLayout()
        self.auto_break_check = QCheckBox("定时休息")
        break_layout.addWidget(self.auto_break_check)
        
        break_layout.addWidget(QLabel("每"))
        self.break_interval_spin = QSpinBox()
        self.break_interval_spin.setRange(10, 200)
        self.break_interval_spin.setValue(50)
        break_layout.addWidget(self.break_interval_spin)
        
        break_layout.addWidget(QLabel("次休息"))
        self.break_duration_spin = QSpinBox()
        self.break_duration_spin.setRange(60, 1800)
        self.break_duration_spin.setValue(300)
        self.break_duration_spin.setSuffix(" 秒")
        break_layout.addWidget(self.break_duration_spin)
        break_layout.addStretch()
        advanced_layout.addLayout(break_layout)
        
        layout.addWidget(advanced_group)
        
        # 控制按钮组
        control_group = QGroupBox("控制")
        control_layout = QHBoxLayout()
        control_group.setLayout(control_layout)
        
        self.start_button = QPushButton("开始突破")
        self.start_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        self.start_button.clicked.connect(self.start_raid)
        control_layout.addWidget(self.start_button)
        
        self.stop_button = QPushButton("停止突破")
        self.stop_button.setStyleSheet("QPushButton { background-color: #f44336; color: white; font-weight: bold; }")
        self.stop_button.setEnabled(False)
        self.stop_button.clicked.connect(self.stop_raid)
        control_layout.addWidget(self.stop_button)
        
        self.test_button = QPushButton("测试连接")
        self.test_button.clicked.connect(self.test_connection)
        control_layout.addWidget(self.test_button)
        
        layout.addWidget(control_group)
        
        # 状态显示
        status_group = QGroupBox("运行状态")
        status_layout = QVBoxLayout()
        status_group.setLayout(status_layout)
        
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("QLabel { font-size: 14px; font-weight: bold; }")
        status_layout.addWidget(self.status_label)
        
        layout.addWidget(status_group)
        
        layout.addStretch()
    
    def get_main_window(self):
        """获取主窗口实例"""
        parent = self.parent()
        while parent:
            if hasattr(parent, 'get_current_hwnd'):
                return parent
            parent = parent.parent()
        return None
    
    def start_raid(self):
        """开始结界突破"""
        try:
            logging.info("🚀 用户点击开始突破按钮")
            
            # 获取主窗口的窗口句柄
            main_window = self.get_main_window()
            if not main_window:
                error_msg = "无法获取主窗口实例"
                logging.error(f"❌ {error_msg}")
                QMessageBox.critical(self, "错误", error_msg)
                return
            
            hwnd = main_window.get_current_hwnd()
            if not hwnd:
                error_msg = "请先在左侧选择游戏窗口"
                logging.error(f"❌ 启动失败: {error_msg}")
                QMessageBox.critical(self, "错误", error_msg)
                return
            
            logging.info(f"✅ 使用窗口句柄: {hwnd}")
            
            # 创建机器人实例
            logging.info("🤖 正在创建结界突破机器人实例...")
            self.realm_raid_bot = RealmRaidBot(hwnd)
            
            # 应用用户设置
            settings = self.get_current_settings()
            logging.info(f"⚙️ 应用用户设置: {settings}")
            self.realm_raid_bot.settings.update(settings)
            
            # 更新界面状态
            self.is_running = True
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.status_label.setText("运行中...")
            logging.info("🔄 界面状态已更新")
            
            # 启动突破线程
            logging.info("🧵 启动结界突破后台线程...")
            self.raid_thread = RealmRaidThread(self.realm_raid_bot)
            self.raid_thread.update_signal.connect(self.on_update)
            self.raid_thread.stats_signal.connect(self.on_stats_update)
            self.raid_thread.finished.connect(self.on_raid_finished)
            self.raid_thread.start()
            
            logging.info("✅ 结界突破启动成功")
            
        except Exception as e:
            error_msg = f"启动失败: {e}"
            logging.error(f"❌ {error_msg}")
            logging.error(f"错误详情: {traceback.format_exc()}")
            QMessageBox.critical(self, "错误", error_msg)
            self.reset_ui_state()
    
    def stop_raid(self):
        """停止结界突破"""
        try:
            logging.info("🛑 用户点击停止突破按钮")
            
            if self.raid_thread and self.raid_thread.isRunning():
                logging.info("⏹️ 正在停止结界突破线程...")
                self.raid_thread.stop()
                
                if self.raid_thread.wait(5000):
                    logging.info("✅ 结界突破线程已安全停止")
                else:
                    logging.warning("⚠️ 线程停止超时，强制终止")
            
            self.reset_ui_state()
            logging.info("🛑 结界突破已停止")
            
        except Exception as e:
            error_msg = f"停止突破失败: {e}"
            logging.error(f"❌ {error_msg}")
            logging.error(f"错误详情: {traceback.format_exc()}")
    
    def test_connection(self):
        """测试连接"""
        try:
            logging.info("🔧 用户点击测试连接按钮")
            
            main_window = self.get_main_window()
            if not main_window:
                error_msg = "无法获取主窗口实例"
                logging.error(f"❌ {error_msg}")
                QMessageBox.critical(self, "错误", error_msg)
                return
            
            hwnd = main_window.get_current_hwnd()
            if not hwnd:
                error_msg = "请先在左侧选择游戏窗口"
                logging.error(f"❌ 测试连接失败: {error_msg}")
                QMessageBox.critical(self, "错误", error_msg)
                return
            
            logging.info(f"🔍 开始测试窗口连接，句柄: {hwnd}")
            
            # 创建临时机器人实例进行测试
            logging.info("🤖 创建临时机器人实例进行测试...")
            test_bot = RealmRaidBot(hwnd)
            
            # 测试截图功能
            logging.info("📸 测试截图功能...")
            screenshot = test_bot.take_screenshot()
            
            if screenshot is not None:
                success_msg = "连接测试成功！可以正常截图。"
                logging.info(f"✅ {success_msg}")
                QMessageBox.information(self, "成功", success_msg)
            else:
                error_msg = "连接测试失败！无法截图。"
                logging.error(f"❌ {error_msg}")
                QMessageBox.critical(self, "失败", error_msg)
                
        except Exception as e:
            error_msg = f"测试连接失败: {e}"
            logging.error(f"❌ {error_msg}")
            logging.error(f"错误详情: {traceback.format_exc()}")
            QMessageBox.critical(self, "错误", error_msg)
    
    def get_current_settings(self):
        """获取当前设置"""
        return {
            'max_raids_per_session': self.raid_count_spin.value(),
            'selection_strategy': self.strategy_combo.currentText(),
            'retry_on_defeat': self.retry_on_defeat_check.isChecked(),
            'skip_cooldown': self.skip_cooldown_check.isChecked(),
            'auto_battle': self.auto_battle_check.isChecked(),
            'battle_timeout': self.battle_timeout_spin.value(),
            'random_delay_min': self.delay_min_spin.value() / 1000.0,
            'random_delay_max': self.delay_max_spin.value() / 1000.0,
            'auto_break': self.auto_break_check.isChecked(),
            'break_interval': self.break_interval_spin.value(),
            'break_duration': self.break_duration_spin.value()
        }
    
    def on_update(self, message):
        """更新回调"""
        # 这里可以发送信号到主窗口的日志组件
        logging.info(f"结界突破更新: {message}")
    
    def on_stats_update(self, stats):
        """统计更新回调"""
        # 这里可以更新状态显示
        pass

    def open_grid_config(self):
        """打开网格配置对话框"""
        try:
            from features.realm_raid.grid_config_dialog import GridConfigDialog
            from features.realm_raid.realm_raid_config import REALM_GRID_CONFIG

            # 创建配置对话框
            dialog = GridConfigDialog(self, REALM_GRID_CONFIG)

            if dialog.exec_() == dialog.Accepted:
                # 获取新配置
                new_config = dialog.get_config()

                # 保存配置
                self.save_grid_config(new_config)

                # 显示成功消息
                QMessageBox.information(self, "配置保存",
                    "网格配置已保存！\n重启程序后生效。")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开网格配置失败：{str(e)}")

    def open_interactive_grid_config(self):
        """打开交互式网格配置对话框"""
        try:
            from features.realm_raid.interactive_grid_config import InteractiveGridConfigDialog
            from features.realm_raid.realm_raid_config import REALM_GRID_CONFIG

            # 创建交互式配置对话框
            dialog = InteractiveGridConfigDialog(self, REALM_GRID_CONFIG)

            if dialog.exec_() == dialog.Accepted:
                # 获取新配置
                new_config = dialog.get_config()

                # 保存配置
                self.save_grid_config(new_config)

                # 显示成功消息
                QMessageBox.information(self, "配置保存",
                    "🎉 交互式网格配置已保存！\n"
                    "✅ 配置文件: custom_grid_config.json\n"
                    "🔄 重启程序后生效")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开交互式网格配置失败：{str(e)}")

    def save_grid_config(self, config):
        """保存网格配置"""
        try:
            config_file = "custom_grid_config.json"
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)

            # 同时更新配置文件中的配置
            self.update_config_file(config)

        except Exception as e:
            raise Exception(f"保存配置失败: {e}")

    def update_config_file(self, config):
        """更新配置文件"""
        try:
            config_file_path = "features/realm_raid/realm_raid_config.py"

            # 读取当前配置文件
            with open(config_file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 构建新的配置字符串
            new_config_str = f'''# 3x3网格配置
REALM_GRID_CONFIG = {{
    # 网格区域定义（相对坐标 0.0-1.0）
    "grid_area": {{
        "left": {config["grid_area"]["left"]},      # 网格左边界
        "top": {config["grid_area"]["top"]},       # 网格上边界
        "right": {config["grid_area"]["right"]},     # 网格右边界
        "bottom": {config["grid_area"]["bottom"]}     # 网格下边界
    }},

    # 网格布局 - {config["layout"]["rows"]}x{config["layout"]["columns"]}排列
    "layout": {{
        "rows": {config["layout"]["rows"]},         # {config["layout"]["rows"]}行
        "columns": {config["layout"]["columns"]},      # {config["layout"]["columns"]}列
        "item_spacing": {{
            "horizontal": {config["layout"]["item_spacing"]["horizontal"]},  # 水平间距
            "vertical": {config["layout"]["item_spacing"]["vertical"]}     # 垂直间距
        }}
    }},

    # 单个结界项尺寸
    "item_size": {{
        "width": {config["item_size"]["width"]},     # 结界项宽度
        "height": {config["item_size"]["height"]}     # 结界项高度
    }}
}}'''

            # 替换配置部分
            import re
            pattern = r'REALM_GRID_CONFIG = \{.*?\}'
            new_content = re.sub(pattern, new_config_str, content, flags=re.DOTALL)

            # 写回文件
            with open(config_file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)

        except Exception as e:
            # 如果更新配置文件失败，至少保存到JSON文件
            pass

    def open_overlay_config(self):
        """打开游戏界面映射配置"""
        try:
            from features.realm_raid.fixed_game_overlay import FixedGameOverlayController

            # 获取主窗口实例以传递窗口句柄
            main_window = self.get_main_window()

            # 创建修复的游戏窗口覆盖工具，传递主窗口引用
            self.fixed_overlay = FixedGameOverlayController(main_window)
            self.fixed_overlay.show()

            # 检查是否已经自动连接
            if self.fixed_overlay.game_window:
                QMessageBox.information(self, "自动连接成功",
                    "✅ 已自动连接到当前选择的游戏窗口！\n\n"
                    "🎯 现在可以直接点击'🟢 显示覆盖'！\n\n"
                    "✨ 特点：\n"
                    "• 控制界面完全不透明\n"
                    "• 透明覆盖层直接显示在游戏窗口上\n"
                    "• 可以透过覆盖层正常操作游戏\n"
                    "• 按 ESC 键快速关闭覆盖层")
            else:
                QMessageBox.information(self, "覆盖工具已打开",
                    "修复的游戏窗口覆盖工具已打开！\n\n"
                    "🎯 使用步骤：\n"
                    "1. 点击'🎮 连接游戏窗口'\n"
                    "2. 点击'🟢 显示覆盖'\n"
                    "3. 直接在游戏窗口上看到覆盖效果\n"
                    "4. 点击'💾 保存配置'\n\n"
                    "💡 提示：如果左侧已选择游戏窗口，会自动连接！\n"
                    "🔧 界面已修复，确保完全不透明！")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开游戏窗口覆盖失败：{str(e)}")

    def on_overlay_config_changed(self, config):
        """界面映射配置变更回调"""
        # 这里可以实时更新配置
        pass
    
    def on_raid_finished(self):
        """突破结束回调"""
        logging.info("🏁 结界突破流程结束回调")
        self.reset_ui_state()
    
    def reset_ui_state(self):
        """重置界面状态"""
        logging.debug("🔄 重置结界突破GUI界面状态")
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.status_label.setText("就绪")
        self.is_running = False
