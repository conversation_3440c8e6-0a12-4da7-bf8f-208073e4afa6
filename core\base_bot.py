import os
import time
import random
import math
import numpy as np
import win32gui
import win32api
import win32con
import cv2
from PIL import ImageGrab, Image
import logging
from typing import Tuple, List, Dict, Optional, Callable, Union, Any
from core.adaptive_template import AdaptiveTemplateManager
from core.screenshot import Screenshot
from core.template_manager import TemplateManager
from core.auto_clicker import AutoClicker
from core.anti_detection import AntiDetection
from core.background_operation import BackgroundOperation
import concurrent.futures

class BaseBot(AntiDetection):
    """基础自动化机器人类
    
    提供通用的自动化功能，包括:
    - 窗口控制
    - 鼠标点击
    - 图像识别
    - 模拟人类操作
    - 后台操作
    """
    
    def __init__(self, hwnd=None, base_resolution: Tuple[int, int] = (1920, 1080)):
        """初始化基础机器人
        
        Args:
            hwnd: 游戏窗口句柄，如果为None则需要后续设置
            base_resolution: 基准分辨率，默认为1920x1080
        """
        # 调用父类(AntiDetection)构造函数
        super().__init__(hwnd)
        
        self.hwnd = hwnd
        self.window_rect = None
        self.stats = {}
        self.running = False
        self.latest_screenshot = None
        
        # 基本设置参数
        self.template_match_threshold = 0.7  # 降低匹配阈值
        self.wait_time = 1.0
        
        # 防检测设置
        self.use_random_click = True
        self.click_radius = 5  # 点击半径范围（像素）
        
        self.use_random_delay = True
        self.min_delay = 0.8  # 最小延迟时间（秒）
        self.max_delay = 1.5  # 最大延迟时间（秒）
        
        self.use_human_like_move = True  # 模拟人类移动
        
        self.use_breaks = True  # 是否使用休息
        self.break_interval = 1800  # 默认每30分钟休息一次
        self.break_duration = 60    # 默认休息1分钟
        self.last_break_time = time.time()
        
        # 后台操作设置
        self.use_background_mode = False  # 是否使用后台模式
        
        # 内部计数器
        self.continuous_actions = 0  # 连续操作次数
        
        # 自定义模板点击区域
        self.custom_click_areas = {}
        
        logging.info(f"初始化机器人 - 基准分辨率: {base_resolution[0]}x{base_resolution[1]}")
        
        # 创建功能组件
        self.screenshot = Screenshot(hwnd)
        self.auto_clicker = AutoClicker(hwnd)
        self.template_manager = TemplateManager(base_resolution)
        self.adaptive_template_manager = AdaptiveTemplateManager(base_resolution)
        self.background_operation = BackgroundOperation(hwnd)
        
        # 如果提供了窗口句柄，获取窗口区域
        if self.hwnd:
            self.get_window_rect()
        
        # 确保客户区偏移是最新的
        if hwnd:
            self.update_client_offsets()
    
    def update_client_offsets(self):
        """更新所有组件的客户区偏移"""
        try:
            if hasattr(self, 'screenshot'):
                self.screenshot.update_client_offset()
            if hasattr(self, 'auto_clicker'):
                self.auto_clicker.update_client_offset()
            logging.debug("已更新所有组件的客户区偏移")
        except Exception as e:
            logging.error(f"更新客户区偏移失败: {str(e)}")
    
    def set_hwnd(self, hwnd):
        """设置窗口句柄
        
        Args:
            hwnd: 窗口句柄
            
        Returns:
            bool: 是否设置成功
        """
        if hwnd and win32gui.IsWindow(hwnd):
            self.hwnd = hwnd
            self.get_window_rect()
            
            # 设置各组件的窗口句柄
            if hasattr(self, 'screenshot'):
                self.screenshot.set_hwnd(hwnd)
            
            if hasattr(self, 'auto_clicker'):
                self.auto_clicker.set_hwnd(hwnd)
                
            if hasattr(self, 'background_operation'):
                self.background_operation.set_hwnd(hwnd)
            
            # 更新客户区偏移
            self.update_client_offsets()
            
            logging.info(f"设置窗口句柄: {hwnd}")
            return True
        logging.error(f"无效的窗口句柄: {hwnd}")
        return False
    
    def get_window_rect(self):
        """获取窗口矩形区域
        
        Returns:
            tuple: 窗口矩形区域 (left, top, right, bottom)
        """
        if not self.hwnd:
            logging.warning("未设置窗口句柄，无法获取窗口区域")
            return None
            
        try:
            self.window_rect = win32gui.GetWindowRect(self.hwnd)
            left, top, right, bottom = self.window_rect
            width = right - left
            height = bottom - top
            logging.info(f"获取窗口区域: 位置[{left},{top}], 尺寸[{width}x{height}]")
            
            # 同步更新AutoClicker的客户区偏移
            if hasattr(self, 'auto_clicker'):
                self.auto_clicker.update_client_offset()
            
            return self.window_rect
        except Exception as e:
            logging.error(f"获取窗口区域失败: {str(e)}")
            return None
    
    def load_template(self, template_name: str, template_path: str, rel_click_point: Tuple[float, float] = (0.5, 0.5)) -> bool:
        """加载模板图像
        
        Args:
            template_name: 模板名称
            template_path: 模板文件路径
            rel_click_point: 相对点击位置，范围0.0-1.0，默认为中心点(0.5, 0.5)
            
        Returns:
            bool: 是否加载成功
        """
        try:
            if not os.path.exists(template_path):
                logging.error(f"模板文件不存在: {template_path}")
                return False
                
            # 同时加载到自适应模板管理器
            result1 = self.adaptive_template_manager.load_template(template_name, template_path, rel_click_point)
            # 同时加载到普通模板管理器
            result2 = self.template_manager.load_template(template_name, template_path, rel_click_point)
            
            logging.info(f"加载模板: {template_name}, 文件: {template_path}, 相对点击位置: {rel_click_point}")
            return result1 and result2
        except Exception as e:
            logging.error(f"加载模板失败: {str(e)}")
            return False
    
    def take_screenshot(self) -> np.ndarray:
        """获取游戏窗口客户区域截图
        
        Returns:
            np.ndarray: 截图图像数据，如果失败则返回None
        """
        if not self.hwnd:
            logging.error("未设置窗口句柄，无法截图")
            return None
            
        if not self.window_rect:
            self.get_window_rect()
            if not self.window_rect:
                return None
        
        try:
            # 确保客户区偏移是最新的
            self.update_client_offsets()
            
            # 记录窗口信息供调试
            window_title = win32gui.GetWindowText(self.hwnd)
            window_class = win32gui.GetClassName(self.hwnd)
            logging.debug(f"截图窗口信息 - 句柄: {self.hwnd}, 标题: {window_title}, 类名: {window_class}")
            
            # 如果使用后台模式，使用后台截图方法
            if self.use_background_mode:
                screenshot = self.background_operation.capture_background()
                if screenshot is not None:
                    self.latest_screenshot = screenshot
                    return screenshot
            
            # 优先使用客户区域截图方法
            logging.debug("尝试使用客户区域截图方法")
            screenshot = self.screenshot.capture_client_area()
            
            # 如果客户区截图失败，回退到普通截图
            if screenshot is None:
                logging.warning("客户区截图失败，尝试使用普通截图方法")
                screenshot = self.screenshot.capture()
                if screenshot is None:
                    logging.error("普通截图方法也失败，无法获取截图")
                    return None
                else:
                    logging.debug("成功使用普通截图方法获取截图")
            else:
                logging.debug("成功使用客户区截图方法获取截图")
            
                self.latest_screenshot = screenshot
                logging.info(f"截图完成: 尺寸[{screenshot.shape[1]}x{screenshot.shape[0]}]")
                return screenshot
        except Exception as e:
            logging.error(f"截图失败: {str(e)}")
            return None
    
    def find_template(self, template_name: str, threshold: float = None) -> Optional[Tuple[int, int]]:
        """查找模板图像
        
        Args:
            template_name: 模板名称
            threshold: 匹配阈值，如果为None则使用默认值
            
        Returns:
            tuple: 模板中心点坐标(x, y)，如果未找到则返回None
        """
        if self.latest_screenshot is None:
            self.take_screenshot()
            if self.latest_screenshot is None:
                logging.error("没有有效的截图，无法查找模板")
                return None
        
        if threshold is None:
            threshold = self.template_match_threshold
            
        try:
            # 首先在自适应模板管理器中查找
            position = self.adaptive_template_manager.find_template(template_name, self.latest_screenshot, threshold)
            
            if position:
                x, y = position
                logging.info(f"找到模板[{template_name}]: 位置[{x},{y}], 匹配度[{threshold:.3f}]")
                return position
            
            # 如果自适应模板未找到，尝试使用常规模板管理器
            position = self.template_manager.find_template(template_name, self.latest_screenshot, threshold)
            
            if position:
                x, y = position
                logging.info(f"找到模板[{template_name}]: 位置[{x},{y}], 匹配度[{threshold:.3f}]")
                return position
                
            logging.info(f"未找到模板[{template_name}]")
            return None
        except Exception as e:
            logging.error(f"查找模板出错: {str(e)}")
            return None
    
    def find_all_templates(self, template_name: str, threshold: float = None, 
                          max_results: int = 10) -> List[Tuple[int, int]]:
        """查找屏幕上所有匹配的模板位置
        
        Args:
            template_name: 模板名称
            threshold: 匹配阈值，如果为None则使用默认值
            max_results: 最大结果数量
            
        Returns:
            list: 所有找到的位置列表 [(x1, y1), (x2, y2), ...], 每个位置为目标的中心点坐标
        """
        if not threshold:
            threshold = self.template_match_threshold
            
        if self.latest_screenshot is None:
            logging.info(f"查找所有模板[{template_name}]: 未有截图，先执行截图")
            self.take_screenshot()
            if self.latest_screenshot is None:
                return []
        
        try:
            logging.info(f"查找所有模板[{template_name}]: 阈值[{threshold}], 最大结果数[{max_results}]")
            
            # 使用自适应模板系统查找所有匹配
            results = self.adaptive_template_manager.find_all_templates(
                self.latest_screenshot,
                template_name,
                threshold=threshold,
                max_results=max_results
            )
            
            # 如果自适应模板系统找到了结果，直接返回
            if results:
                centers = [result['center'] for result in results]
                logging.info(f"自适应系统找到 {len(centers)} 个模板[{template_name}]")
                return centers
                
            # 尝试使用常规模板管理器
            positions = self.template_manager.find_all_templates_in_image(
                self.latest_screenshot,
                template_name,
                threshold=threshold,
                max_results=max_results
            )
            
            if positions:
                logging.info(f"常规模板系统找到 {len(positions)} 个模板[{template_name}]")
                return positions
            
            logging.info(f"未找到模板[{template_name}]")
            return []
        except Exception as e:
            logging.error(f"查找所有模板失败[{template_name}]: {str(e)}")
            return []
    
    def find_multiple_templates(self, template_names: List[str], threshold: float = None) -> Dict[str, Tuple[int, int]]:
        """查找多个模板（串行方式）
        
        Args:
            template_names: 模板名称列表
            threshold: 匹配阈值，如果为None则使用默认值
            
        Returns:
            dict: {模板名称: (x, y)} - 找到的中心位置坐标
        """
        if not threshold:
            threshold = self.template_match_threshold
            
        if self.latest_screenshot is None:
            logging.info(f"查找多个模板: 未有截图，先执行截图")
            self.take_screenshot()
            if self.latest_screenshot is None:
                return {}
        
        results = {}
        
        for template_name in template_names:
            position = self.find_template(template_name, threshold)
            if position:
                results[template_name] = position
                
        return results
            
    def find_multiple_templates_parallel(self, template_names: List[str], threshold: float = None, max_workers: int = None) -> Dict[str, Tuple[int, int]]:
        """并行查找多个模板
        
        Args:
            template_names: 模板名称列表
            threshold: 匹配阈值，如果为None则使用默认值
            max_workers: 最大工作线程数，默认为None(由系统决定)
            
        Returns:
            dict: {模板名称: (x, y)} - 找到的中心位置坐标
        """
        if not threshold:
            threshold = self.template_match_threshold
            
        if self.latest_screenshot is None:
            logging.info(f"并行查找多个模板: 未有截图，先执行截图")
            self.take_screenshot()
            if self.latest_screenshot is None:
                return {}
        
        logging.info(f"并行查找多个模板: 模板数量[{len(template_names)}], 阈值[{threshold}]")
        
        try:
            # 首先尝试使用自适应模板管理器并行查找
            results_adaptive = self.adaptive_template_manager.find_multiple_templates_parallel(
                self.latest_screenshot, 
                template_names, 
                threshold=threshold,
                max_workers=max_workers
            )
            
            # 处理自适应模板的结果
            results = {}
            for name, result in results_adaptive.items():
                center_x, center_y = result['center']
                results[name] = (center_x, center_y)
                
            # 查找未匹配的模板
            not_found_templates = [name for name in template_names if name not in results]
            
            # 如果有未找到的模板，尝试使用常规模板管理器
            if not_found_templates:
                logging.info(f"自适应模板未找到{len(not_found_templates)}个模板，尝试使用常规模板管理器")
                results_normal = self.template_manager.find_multiple_templates_parallel(
                    self.latest_screenshot,
                    not_found_templates,
                    threshold=threshold,
                    max_workers=max_workers
                )
                
                # 合并结果
                results.update(results_normal)
            
            logging.info(f"并行查找完成: 总共找到 {len(results)}/{len(template_names)} 个模板")
            return results
            
        except Exception as e:
            logging.error(f"并行查找多个模板失败: {str(e)}")
            return {}
    
    def _bernstein(self, i: int, n: int, t: float) -> float:
        """计算贝塞尔曲线的伯恩斯坦多项式
        
        Args:
            i: 控制点索引
            n: 控制点总数
            t: 参数值
            
        Returns:
            float: 多项式值
        """
        return math.comb(n, i) * (t ** i) * ((1 - t) ** (n - i))
    
    def smooth_move(self, x_dest: int, y_dest: int, duration: float = 0.3) -> None:
        """平滑移动鼠标到目标位置
        
        Args:
            x_dest: 目标X坐标
            y_dest: 目标Y坐标
            duration: 移动持续时间（秒），默认减小到0.3秒
        """
        # 获取当前鼠标位置
        x_orig, y_orig = win32api.GetCursorPos()
        
        # 计算距离
        distance = math.sqrt((x_dest-x_orig)**2 + (y_dest-y_orig)**2)
        
        # 根据距离动态调整移动时间，近距离更快
        if distance < 100:
            duration = min(duration, 0.15)  # 小于100像素时最多0.15秒
        elif distance < 300:
            duration = min(duration, 0.25)  # 小于300像素时最多0.25秒
        
        # 减少步数，30步/秒已足够自然
        steps = int(duration * 30)
        steps = max(steps, 5)  # 至少5步
        
        # 使用贝塞尔曲线生成平滑路径
        # 创建随机控制点
        control_xs = [x_orig]
        control_ys = [y_orig]
        
        # 添加1-2个随机控制点
        control_points = random.randint(1, 2)
        for i in range(control_points):
            # 在起点和终点之间添加随机控制点，但有一定偏移
            mid_x = (x_orig + x_dest) / 2
            mid_y = (y_orig + y_dest) / 2
            
            # 随机偏移量，与距离成正比
            offset_range = math.sqrt((x_dest-x_orig)**2 + (y_dest-y_orig)**2) * 0.2
            
            control_xs.append(mid_x + random.uniform(-offset_range, offset_range))
            control_ys.append(mid_y + random.uniform(-offset_range, offset_range))
        
        control_xs.append(x_dest)
        control_ys.append(y_dest)
        
        # 创建贝塞尔曲线路径
        path = []
        n = len(control_xs) - 1
        
        for i in range(steps + 1):
            t = i / steps
            x = 0
            y = 0
            
            for j in range(n + 1):
                bernstein = self._bernstein(j, n, t)
                x += control_xs[j] * bernstein
                y += control_ys[j] * bernstein
                
            # 可以添加一些随机性使移动更自然，但减少扰动量
            if i > 0 and i < steps:
                # 微小的随机扰动，与移动速度成正比但幅度更小
                speed_factor = math.sqrt((x_dest-x_orig)**2 + (y_dest-y_orig)**2) / duration / 2000
                x += random.uniform(-1, 1) * speed_factor
                y += random.uniform(-1, 1) * speed_factor
                
            path.append((int(x), int(y)))
        
        # 执行平滑移动
        for px, py in path:
            win32api.SetCursorPos((px, py))
            time.sleep(duration / steps)  # 控制移动速度
    
    def _generate_human_path(self, x1: int, y1: int, x2: int, y2: int, control_points: int = 2) -> List[Tuple[float, float]]:
        """生成人类般的鼠标移动路径（使用贝塞尔曲线）
        
        Args:
            x1: 起始点X坐标
            y1: 起始点Y坐标
            x2: 终点X坐标
            y2: 终点Y坐标
            control_points: 控制点数量
            
        Returns:
            list: 路径点列表
        """
        # 创建随机控制点
        control_xs = [x1]
        control_ys = [y1]
        
        for i in range(control_points):
            control_xs.append(random.uniform(min(x1, x2), max(x1, x2)))
            control_ys.append(random.uniform(min(y1, y2), max(y1, y2)))
            
        control_xs.append(x2)
        control_ys.append(y2)
        
        # 创建贝塞尔曲线路径
        path = []
        steps = int(math.sqrt((x2-x1)**2 + (y2-y1)**2) / 5)  # 根据距离计算步数
        steps = max(steps, 10)  # 最少10步
        
        for i in range(steps + 1):
            t = i / steps
            x = 0
            y = 0
            n = len(control_xs) - 1
            
            for j in range(n + 1):
                bernstein = self._bernstein(j, n, t)
                x += control_xs[j] * bernstein
                y += control_ys[j] * bernstein
                
            path.append((x, y))
            
        return path
    
    def wait(self, seconds: float = None) -> None:
        """等待指定时间，支持随机化
        
        Args:
            seconds: 等待时间，如果为None则使用默认值
        """
        if seconds is None:
            seconds = self.wait_time
            
        if self.use_random_delay:
            seconds = random.uniform(self.min_delay, self.max_delay)
            
        logging.info(f"等待: {seconds:.2f}秒")
        time.sleep(seconds)
        
    def check_if_break_needed(self) -> bool:
        """检查是否需要休息
        
        Returns:
            bool: 是否需要休息
        """
        if not self.use_breaks:
            return False
            
        current_time = time.time()
        time_since_last_break = current_time - self.last_break_time
        if time_since_last_break > self.break_interval:
            logging.info(f"已运行{time_since_last_break:.1f}秒，需要休息")
            return True
        
        remaining = self.break_interval - time_since_last_break
        logging.debug(f"距离下次休息还有: {remaining:.1f}秒")
        return False
    
    def take_break(self, callback: Callable = None) -> None:
        """执行休息逻辑
        
        Args:
            callback: 回调函数，用于通知UI
        """
        logging.info(f"开始休息，持续时间：{self.break_duration}秒")
        
        # 如果有回调函数，通知UI
        if callback:
            callback({"status": "break", "message": f"正在休息，剩余时间：{self.break_duration}秒"})
            
        # 记录休息开始时间
        self.last_break_time = time.time()
        
        # 休息指定时间
        time.sleep(self.break_duration)
        
        logging.info("休息结束，继续运行")
        
        # 如果有回调函数，通知UI
        if callback:
            callback({"status": "running", "message": "休息结束，继续运行"})
    
    def get_current_resolution(self) -> Tuple[int, int]:
        """获取当前窗口分辨率
        
        Returns:
            tuple: 窗口分辨率 (width, height)
        """
        if not self.window_rect:
            self.get_window_rect()
            
        if not self.window_rect:
            logging.warning("无法获取窗口分辨率，使用默认值(1920, 1080)")
            return (1920, 1080)  # 返回默认分辨率
            
        left, top, right, bottom = self.window_rect
        width = right - left
        height = bottom - top
        
        logging.info(f"当前窗口分辨率: {width}x{height}")
        return (width, height)
    
    def set_base_resolution(self, width: int, height: int) -> None:
        """设置基准分辨率
        
        Args:
            width: 宽度
            height: 高度
        """
        self.adaptive_template_manager.base_resolution = (width, height)
        self.template_manager.base_resolution = (width, height)
        logging.info(f"设置基准分辨率: {width}x{height}")
    
    def start(self, callback: Callable = None) -> None:
        """启动自动化脚本
        
        这是一个基类方法，需要在子类中实现具体功能
        
        Args:
            callback: 回调函数，用于通知UI
        """
        raise NotImplementedError("此方法需要在子类中实现")
    
    def stop(self) -> None:
        """停止自动化脚本"""
        logging.info("停止自动化脚本")
        self.running = False
    
    def click(self, x: int, y: int, delay: float = 0.1, is_screen_coord: bool = False) -> bool:
        """点击指定位置，支持随机化点击位置和平滑移动
        
        Args:
            x: 横坐标
            y: 纵坐标
            delay: 点击后等待时间
            is_screen_coord: 是否已经是屏幕坐标，True表示直接使用，False需要转换
            
        Returns:
            bool: 是否点击成功
        """
        if not self.hwnd and not is_screen_coord:
            logging.error("未设置窗口句柄，无法点击")
            return False
            
        # 如果使用后台模式，使用后台点击方法
        if self.use_background_mode and not is_screen_coord:
            # 随机偏移在后台点击方法中处理
            return self.background_operation.click_background(
                x, y, 
                delay=delay, 
                use_random=self.use_random_click, 
                offset_range=self.click_radius
            )
            
        # 如果不是后台模式，使用原有的点击方法
        final_x, final_y = x, y
        
        # 如果不是屏幕坐标，需要转换
        if not is_screen_coord and self.hwnd:
            try:
                # 转换为屏幕坐标
                final_x, final_y = win32gui.ClientToScreen(self.hwnd, (x, y))
                logging.debug(f"坐标转换: 客户区[{x},{y}] -> 屏幕[{final_x},{final_y}]")
            except Exception as e:
                logging.error(f"坐标转换失败: {str(e)}")
                return False
        
        if self.use_random_click:
            # 在半径范围内随机化点击位置
            angle = random.uniform(0, 2 * math.pi)
            distance = random.uniform(0, self.click_radius)
            rand_x = int(final_x + distance * math.cos(angle))
            rand_y = int(final_y + distance * math.sin(angle))
            final_x, final_y = rand_x, rand_y
            logging.debug(f"随机化点击位置: 原始[{x},{y}] -> 随机化[{final_x},{final_y}]")
        
        logging.info(f"点击位置: [{final_x},{final_y}]")

        try:
            # 前台模式使用平滑移动，更像人类操作
            if self.use_human_like_move:
                # 使用平滑移动到目标位置，增加移动时间让移动更明显
                move_duration = random.uniform(0.3, 0.8)  # 增加移动时间范围
                self.smooth_move(final_x, final_y, duration=move_duration)
                logging.info(f"🖱️ 前台模式平滑移动: [{final_x},{final_y}], 耗时: {move_duration:.2f}秒")
            else:
                # 直接设置鼠标位置（快速模式）
                win32api.SetCursorPos((final_x, final_y))
                logging.debug(f"直接移动到位置: [{final_x},{final_y}]")

            # 点击前的短暂停顿
            time.sleep(random.uniform(0.02, 0.08))
            
            # 点击
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)
            time.sleep(random.uniform(0.03, 0.1))  # 缩短随机化按下时间
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)
            
            # 记录连续操作次数
            self.continuous_actions += 1
            
            # 添加随机延迟
            if self.use_random_delay:
                delay = random.uniform(self.min_delay, self.max_delay)
                logging.debug(f"随机延迟: {delay:.2f}秒")
            
            time.sleep(delay)
            return True
        except Exception as e:
            logging.error(f"点击操作失败: {str(e)}")
            return False
    
    def set_custom_click_area(self, template_name: str, x1: int, y1: int, x2: int, y2: int) -> None:
        """为特定模板设置自定义随机点击区域
        
        Args:
            template_name: 模板名称
            x1: 左上角X坐标
            y1: 左上角Y坐标
            x2: 右下角X坐标
            y2: 右下角Y坐标
        """
        self.custom_click_areas[template_name] = (x1, y1, x2, y2)
        logging.info(f"设置模板[{template_name}]的自定义点击区域: ({x1},{y1}) - ({x2},{y2})")
    
    def get_template_size(self, template_name: str) -> Tuple[int, int]:
        """获取模板图像的尺寸
        
        Args:
            template_name: 模板名称
            
        Returns:
            tuple: 模板尺寸 (width, height)，如果未找到则返回默认值(20, 20)
        """
        try:
            # 先尝试从自适应模板管理器获取
            template = self.adaptive_template_manager.templates.get(template_name)
            if template and 'image' in template:
                image = template['image']
                if isinstance(image, np.ndarray) and image.size > 0:
                    return image.shape[1], image.shape[0]
                    
            # 如果未找到，尝试从常规模板管理器获取
            template = self.template_manager.templates.get(template_name)
            if template and 'image' in template:
                image = template['image']
                if isinstance(image, np.ndarray) and image.size > 0:
                    return image.shape[1], image.shape[0]
                    
            # 都未找到返回默认值
            return (20, 20)
        except Exception as e:
            logging.error(f"获取模板'{template_name}'尺寸时出错: {str(e)}")
            return (20, 20)
    
    def adaptive_gaussian_click(self, template_name: str, pos: Tuple[int, int]) -> Tuple[int, int]:
        """结合高斯分布和目标大小的自适应随机点击
        
        使用高斯分布生成更自然的随机点击位置，同时根据目标大小调整分布参数。
        点击位置会集中在中心，向边缘递减，更符合人类点击习惯。
        
        Args:
            template_name: 模板名称，用于获取目标大小
            pos: 中心点位置
            
        Returns:
            tuple: 随机偏移后的位置 (x, y)
        """
        x, y = pos
        try:
            # 获取模板大小
            width, height = self.get_template_size(template_name)
            
            # 根据模板大小计算合适的标准差
            # 标准差约为目标尺寸的12-15%
            sigma_x = max(2, min(int(width * 0.12), 8))
            sigma_y = max(2, min(int(height * 0.12), 8))
            
            # 使用高斯分布生成偏移
            # 高斯分布特性：约68%的值在1个标准差内，95%在2个标准差内
            offset_x = int(random.gauss(0, sigma_x))
            offset_y = int(random.gauss(0, sigma_y))
            
            # 限制偏移不超过模板大小的40%，确保不会点到目标外太远
            max_offset_x = int(width * 0.4)
            max_offset_y = int(height * 0.4)
            
            offset_x = max(-max_offset_x, min(offset_x, max_offset_x))
            offset_y = max(-max_offset_y, min(offset_y, max_offset_y))
            
            # 应用偏移
            rand_x = x + offset_x
            rand_y = y + offset_y
            
            logging.debug(f"高斯自适应点击: 原位置[{x},{y}], 模板大小[{width}x{height}], "
                         f"标准差[{sigma_x},{sigma_y}], 偏移[{offset_x},{offset_y}], "
                         f"最终位置[{rand_x},{rand_y}]")
            
            return rand_x, rand_y
        except Exception as e:
            logging.error(f"自适应高斯点击计算失败: {str(e)}")
            # 出错时使用小范围随机
            return x + random.randint(-3, 3), y + random.randint(-3, 3)
    
    def click_template(self, template_name: str, threshold: float = None) -> bool:
        """查找并点击模板图像
        
        Args:
            template_name: 模板名称
            threshold: 匹配阈值，如果为None则使用默认值
            
        Returns:
            bool: 是否成功点击
        """
        logging.info(f"尝试查找并点击模板[{template_name}]")
        pos = self.find_template(template_name, threshold)
        if pos:
            # 检查是否有自定义点击区域
            if template_name in self.custom_click_areas:
                x1, y1, x2, y2 = self.custom_click_areas[template_name]
                # 在自定义区域内随机选择坐标
                click_x = random.randint(x1, x2)
                click_y = random.randint(y1, y2)
                logging.info(f"使用自定义点击区域点击模板[{template_name}]: 区域({x1},{y1})-({x2},{y2}), 选择点({click_x},{click_y})")
                
                if not self.hwnd:
                    logging.error("未设置窗口句柄，无法点击")
                    return False
                
                # 更新客户区偏移以确保准确性
                self.update_client_offsets()
                
                # 将客户区相对坐标直接转换为屏幕坐标
                screen_x, screen_y = win32gui.ClientToScreen(self.hwnd, (click_x, click_y))
                
                return self.click(screen_x, screen_y, delay=0.1, is_screen_coord=True)
                
            # 截图上的相对坐标
            template_x, template_y = pos
            
            try:
                # 使用更简单直接的方法获取客户区在屏幕上的坐标
                if not self.hwnd:
                    logging.error("未设置窗口句柄，无法点击")
                    return False
                
                # 更新客户区偏移以确保准确性
                self.update_client_offsets()
                
                # 使用高斯自适应随机点击计算点击位置
                click_x, click_y = self.adaptive_gaussian_click(template_name, (template_x, template_y))
                
                # 将客户区相对坐标直接转换为屏幕坐标
                screen_x, screen_y = win32gui.ClientToScreen(self.hwnd, (click_x, click_y))
                
                logging.info(f"点击模板[{template_name}]: 识别位置[{template_x},{template_y}] -> 偏移位置[{click_x},{click_y}] -> 屏幕坐标[{screen_x},{screen_y}]")
                
                # 获取当前窗口分辨率供调试
                current_resolution = self.get_current_resolution()
                logging.debug(f"当前窗口分辨率: {current_resolution[0]}x{current_resolution[1]}")
                
                # 记录客户区偏移供调试
                if hasattr(self, 'auto_clicker') and hasattr(self.auto_clicker, 'client_offset'):
                    logging.debug(f"客户区偏移: {self.auto_clicker.client_offset}")
                
                # 直接点击屏幕坐标，避免中间转换
                win32api.SetCursorPos((screen_x, screen_y))
                
                return self.click(screen_x, screen_y, delay=0.1, is_screen_coord=True)
            except Exception as e:
                logging.error(f"坐标转换失败: {str(e)}")
                return False
        
        logging.info(f"未找到模板[{template_name}]，无法点击")
        return False
    
    def click_any_template(self, template_name: str, threshold: float = None, max_results: int = 10) -> bool:
        """查找并点击屏幕上任意一个匹配的模板
        
        Args:
            template_name: 模板名称
            threshold: 匹配阈值，如果为None则使用默认值
            max_results: 最大查找结果数
            
        Returns:
            bool: 是否成功点击
        """
        positions = self.find_all_templates(template_name, threshold, max_results)
        if positions and len(positions) > 0:
            # 随机选择一个位置点击
            idx = random.randint(0, len(positions) - 1)
            template_x, template_y = positions[idx]
            
            try:
                # 直接将相对坐标转换为屏幕坐标
                if not self.hwnd:
                    logging.error("未设置窗口句柄，无法点击")
                    return False
                    
                # 更新客户区偏移以确保准确性
                self.update_client_offsets()
                
                # 将客户区相对坐标直接转换为屏幕坐标
                screen_x, screen_y = win32gui.ClientToScreen(self.hwnd, (template_x, template_y))
                
                logging.info(f"点击模板[{template_name}]的第{idx+1}个匹配: 客户区相对坐标[{template_x},{template_y}] -> 屏幕坐标[{screen_x},{screen_y}]")
                
                # 获取当前窗口分辨率供调试
                current_resolution = self.get_current_resolution()
                logging.debug(f"当前窗口分辨率: {current_resolution[0]}x{current_resolution[1]}")
                
                return self.click(screen_x, screen_y, delay=0.1, is_screen_coord=True)
            except Exception as e:
                logging.error(f"坐标转换失败: {str(e)}")
                return False
        
        logging.info(f"未找到模板[{template_name}]，无法点击")
        return False
    
    def click_multiple_templates(self, template_names: List[str], threshold: float = None) -> bool:
        """查找并点击多个模板中的第一个找到的
        
        Args:
            template_names: 模板名称列表
            threshold: 匹配阈值，如果为None则使用默认值
            
        Returns:
            bool: 是否成功点击
        """
        results = self.find_multiple_templates(template_names, threshold)
        if results:
            # 随机选择一个模板点击
            template_name = random.choice(list(results.keys()))
            template_x, template_y = results[template_name]
            
            try:
                # 直接将相对坐标转换为屏幕坐标
                if not self.hwnd:
                    logging.error("未设置窗口句柄，无法点击")
                    return False
                
                # 更新客户区偏移以确保准确性
                self.update_client_offsets()
                
                # 将客户区相对坐标直接转换为屏幕坐标
                screen_x, screen_y = win32gui.ClientToScreen(self.hwnd, (template_x, template_y))
                
                logging.info(f"点击多模板中的[{template_name}]: 客户区相对坐标[{template_x},{template_y}] -> 屏幕坐标[{screen_x},{screen_y}]")
                
                # 获取当前窗口分辨率供调试
                current_resolution = self.get_current_resolution()
                logging.debug(f"当前窗口分辨率: {current_resolution[0]}x{current_resolution[1]}")
                
                return self.click(screen_x, screen_y, delay=0.1, is_screen_coord=True)
            except Exception as e:
                logging.error(f"坐标转换失败: {str(e)}")
                return False
        
        templates_str = ", ".join(template_names)
        logging.info(f"未找到任何模板[{templates_str}]，无法点击")
        return False
        
    def click_multiple_templates_parallel(self, template_names: List[str], threshold: float = None, max_workers: int = None) -> bool:
        """并行查找并点击多个模板中的第一个找到的
        
        Args:
            template_names: 模板名称列表
            threshold: 匹配阈值，如果为None则使用默认值
            max_workers: 最大工作线程数
            
        Returns:
            bool: 是否成功点击
        """
        results = self.find_multiple_templates_parallel(template_names, threshold, max_workers)
        if results:
            # 随机选择一个模板点击
            template_name = random.choice(list(results.keys()))
            template_x, template_y = results[template_name]
            
            try:
                # 直接将相对坐标转换为屏幕坐标
                if not self.hwnd:
                    logging.error("未设置窗口句柄，无法点击")
                    return False
                
                # 更新客户区偏移以确保准确性
                self.update_client_offsets()
                
                # 将客户区相对坐标直接转换为屏幕坐标
                screen_x, screen_y = win32gui.ClientToScreen(self.hwnd, (template_x, template_y))
                
                logging.info(f"点击并行查找的多模板中的[{template_name}]: 客户区相对坐标[{template_x},{template_y}] -> 屏幕坐标[{screen_x},{screen_y}]")
                
                # 获取当前窗口分辨率供调试
                current_resolution = self.get_current_resolution()
                logging.debug(f"当前窗口分辨率: {current_resolution[0]}x{current_resolution[1]}")
                
                return self.click(screen_x, screen_y, delay=0.1, is_screen_coord=True)
            except Exception as e:
                logging.error(f"坐标转换失败: {str(e)}")
                return False
        
        templates_str = ", ".join(template_names)
        logging.info(f"未找到任何模板[{templates_str}]，无法点击")
        return False
    
    def enable_background_mode(self, enabled: bool = True) -> None:
        """启用或禁用后台操作模式
        
        Args:
            enabled: 是否启用后台模式
        """
        self.use_background_mode = enabled
        logging.info(f"{'启用' if enabled else '禁用'}后台操作模式")
        
    def is_background_mode_enabled(self) -> bool:
        """检查是否启用了后台模式
        
        Returns:
            bool: 是否启用后台模式
        """
        return self.use_background_mode
        
    def right_click(self, x: int, y: int, delay: float = 0.1) -> bool:
        """右键点击指定位置
        
        Args:
            x: 横坐标
            y: 纵坐标
            delay: 点击后等待时间
            
        Returns:
            bool: 是否点击成功
        """
        if not self.hwnd:
            logging.error("未设置窗口句柄，无法点击")
            return False
            
        # 如果使用后台模式，使用后台右键点击方法
        if self.use_background_mode:
            return self.background_operation.right_click_background(x, y, delay)
            
        # 否则使用前台右键点击
        return self.auto_clicker.right_click(x, y, delay)
        
    def drag(self, start_x: int, start_y: int, end_x: int, end_y: int, 
             duration: float = 0.5, delay: float = 0.1) -> bool:
        """执行鼠标拖拽
        
        Args:
            start_x: 起始X坐标
            start_y: 起始Y坐标
            end_x: 结束X坐标
            end_y: 结束Y坐标
            duration: 拖拽持续时间(秒)
            delay: 拖拽后延迟时间(秒)
            
        Returns:
            bool: 是否成功拖拽
        """
        if not self.hwnd:
            logging.error("未设置窗口句柄，无法拖拽")
            return False
            
        # 如果使用后台模式，使用后台拖拽方法
        if self.use_background_mode:
            return self.background_operation.drag_background(
                start_x, start_y, end_x, end_y, duration, delay
            )
            
        # 否则使用前台拖拽
        return self.auto_clicker.drag(start_x, start_y, end_x, end_y, duration, delay)
        
    def send_key(self, key_code: int, delay: float = 0.1) -> bool:
        """发送按键
        
        Args:
            key_code: 按键代码
            delay: 按键后延迟时间(秒)
            
        Returns:
            bool: 是否成功
        """
        if not self.hwnd:
            logging.error("未设置窗口句柄，无法发送按键")
            return False
            
        # 如果使用后台模式，使用后台发送按键方法
        if self.use_background_mode:
            return self.background_operation.send_key(key_code, delay)
            
        # 否则使用前台按键模拟
        try:
            # 确保窗口在前台
            win32gui.SetForegroundWindow(self.hwnd)
            time.sleep(0.1)  # 等待窗口激活
            
            # 发送按键
            win32api.keybd_event(key_code, 0, 0, 0)  # 按下
            time.sleep(0.05)
            win32api.keybd_event(key_code, 0, win32con.KEYEVENTF_KEYUP, 0)  # 释放
            
            logging.debug(f"发送按键: {key_code}")
            
            # 延迟
            if delay > 0:
                time.sleep(delay)
                
            return True
        except Exception as e:
            logging.error(f"发送按键失败: {str(e)}")
        return False

    def scroll(self, x: int, y: int, direction: int, scroll_count: int = 1) -> bool:
        """鼠标滚轮滚动

        Args:
            x: 滚动位置X坐标
            y: 滚动位置Y坐标
            direction: 滚动方向，正值向上，负值向下
            scroll_count: 滚动次数

        Returns:
            bool: 是否成功
        """
        if not self.hwnd:
            logging.error("未设置窗口句柄，无法滚动")
            return False

        try:
            # 如果使用后台模式，使用后台滚动方法
            if self.use_background_mode:
                return self.background_operation.scroll_background(x, y, direction, scroll_count)

            # 前台滚动方法
            # 先移动鼠标到指定位置
            final_x, final_y = x, y

            # 转换为屏幕坐标
            try:
                final_x, final_y = win32gui.ClientToScreen(self.hwnd, (x, y))
                logging.debug(f"滚动坐标转换: 客户区[{x},{y}] -> 屏幕[{final_x},{final_y}]")
            except Exception as e:
                logging.error(f"滚动坐标转换失败: {str(e)}")
                return False

            # 移动鼠标到目标位置
            win32api.SetCursorPos((final_x, final_y))
            time.sleep(0.05)  # 短暂等待

            # 执行滚动
            for _ in range(scroll_count):
                # direction > 0 向上滚动，direction < 0 向下滚动
                wheel_delta = 120 if direction > 0 else -120
                win32api.mouse_event(
                    win32con.MOUSEEVENTF_WHEEL,
                    final_x, final_y,
                    wheel_delta, 0
                )
                time.sleep(0.05)  # 滚动间隔

            logging.debug(f"滚动操作: 位置[{final_x},{final_y}], 方向[{direction}], 次数[{scroll_count}]")
            return True

        except Exception as e:
            logging.error(f"滚动操作失败: {str(e)}")
            return False