import sys
import logging
from PyQt5.QtWidgets import QApplication, QMainWindow, QTabWidget, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QIcon

# 导入日志配置
from core.logging_config import setup_logging

# 导入功能组件
from features.explore.explore_gui import ExploreGUI
from features.realm_raid.realm_raid_gui import RealmRaidGUI

# 配置日志
LOG_FILE_PATH = "onmyoji_log.txt"
MAX_LOG_SIZE_MB = 10  # 日志文件达到10MB时清理
MAX_LOG_AGE_DAYS = 7  # 日志文件超过7天时清理

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE_PATH, encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class MainWindow(QMainWindow):
    """阴阳师自动化工具主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("阴阳师自动化工具")
        self.setGeometry(100, 100, 900, 700)
        self.initUI()
    
    def initUI(self):
        """初始化UI"""
        # 创建中心部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        layout = QVBoxLayout()
        central_widget.setLayout(layout)
        
        # 创建标签页
        self.tabs = QTabWidget()
        
        # 添加探索标签页
        self.explore_tab = ExploreGUI()
        self.tabs.addTab(self.explore_tab, "探索副本")

        # 添加结界突破标签页
        self.realm_raid_tab = RealmRaidGUI()
        self.tabs.addTab(self.realm_raid_tab, "结界突破")

        # 为未来功能预留标签页
        self.future_tab = QWidget()
        future_layout = QVBoxLayout()
        future_layout.addWidget(QWidget())  # 占位
        self.future_tab.setLayout(future_layout)
        self.tabs.addTab(self.future_tab, "更多功能")
        
        # 添加标签页到主布局
        layout.addWidget(self.tabs)
        
        # 设置状态栏
        self.statusBar().showMessage("准备就绪")
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        # 关闭探索标签页
        if hasattr(self, 'explore_tab'):
            self.explore_tab.closeEvent(event)
        
        # 接受关闭事件
        event.accept()

if __name__ == "__main__":
    # 配置日志系统
    setup_logging(
        log_level=logging.INFO,
        enable_file_log=True,
        log_dir="logs"
    )

    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())