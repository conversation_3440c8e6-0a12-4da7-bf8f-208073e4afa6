#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
阴阳师自动化辅助工具
主程序入口
"""

import os
import sys
import logging
import atexit
from datetime import datetime
import tkinter as tk
from tkinter import ttk, messagebox
import threading
import win32gui

from ui.main_window import MainWindow
from core.window_utils import WindowUtils

# 全局变量
running_bots = []  # 保存运行中的bot实例，用于程序退出时清理
active_threads = []  # 保存运行中的线程，用于程序退出时清理

# 配置日志系统
def setup_logging():
    """配置日志系统"""
    # 创建logs目录
    if not os.path.exists('logs'):
        os.makedirs('logs')
        
    # 创建带时间戳的日志文件名
    log_timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
    log_file = f"logs/ys_bot_{log_timestamp}.log"
    
    # 基本配置
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
        handlers=[
            logging.StreamHandler(),  # 输出到控制台
            logging.FileHandler(log_file, encoding='utf-8')  # 同时保存到文件
        ]
    )
    
    # 设置第三方库的日志级别，避免过多无关日志
    logging.getLogger('PIL').setLevel(logging.WARNING)
    logging.getLogger('matplotlib').setLevel(logging.WARNING)
    logging.getLogger('cv2').setLevel(logging.WARNING)
    
    # 创建主日志记录器
    logger = logging.getLogger('阴阳师辅助工具')
    logger.info('-' * 60)
    logger.info('日志系统初始化完成')
    logger.info(f'日志文件: {log_file}')
    logger.info('-' * 60)
    
    return logger

def cleanup_resources():
    """清理程序资源"""
    logger = logging.getLogger('阴阳师辅助工具')
    logger.info('开始清理资源')
    
    # 停止所有运行中的bot
    for bot in running_bots:
        try:
            logger.info(f'停止机器人: {bot.__class__.__name__}')
            bot.running = False
            bot.stop()
        except Exception as e:
            logger.error(f'停止机器人时出错: {str(e)}')
    
    # 等待所有线程结束
    for thread in active_threads:
        if thread.is_alive():
            logger.info(f'等待线程结束: {thread.name}')
            thread.join(timeout=2.0)  # 等待最多2秒
    
    # 记录清理完成
    logger.info('资源清理完成')

def register_bot(bot):
    """注册机器人实例，用于程序退出时清理
    
    Args:
        bot: 机器人实例
    """
    if bot not in running_bots:
        running_bots.append(bot)
        logger = logging.getLogger('阴阳师辅助工具')
        logger.info(f'注册机器人: {bot.__class__.__name__}')

def register_thread(thread):
    """注册线程，用于程序退出时清理
    
    Args:
        thread: 线程实例
    """
    if thread not in active_threads:
        active_threads.append(thread)
        logger = logging.getLogger('阴阳师辅助工具')
        logger.info(f'注册线程: {thread.name}')

# 主函数
def main():
    """程序入口函数"""
    # 设置日志
    logger = setup_logging()
    logger.info('程序启动')
    
    # 注册退出时的清理函数
    atexit.register(cleanup_resources)
    
    # 创建主窗口
    root = tk.Tk()
    root.title("阴阳师辅助工具")
    
    # 捕获窗口关闭事件
    def on_closing():
        logger.info('用户关闭主窗口')
        cleanup_resources()
        root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    
    try:
        # 设置程序图标
        if os.path.exists("assets/icon.ico"):
            root.iconbitmap("assets/icon.ico")
            logger.info('已加载程序图标')
    except Exception as e:
        logger.warning(f'加载图标失败: {str(e)}')
    
    # 创建主界面
    logger.info('创建主界面')
    app = MainWindow(root)
    
    # 向应用程序传递注册函数
    app.register_bot = register_bot
    app.register_thread = register_thread
    
    # 程序启动时检测游戏窗口
    logger.info('检测游戏窗口')
    game_hwnd = WindowUtils.find_game_window()
    if game_hwnd:
        title = win32gui.GetWindowText(game_hwnd)
        logger.info(f'找到游戏窗口: {title} (hwnd: {game_hwnd})')
        app.set_game_window(game_hwnd)
    else:
        logger.warning('未找到游戏窗口')
        messagebox.showinfo("提示", "未检测到游戏窗口，请启动游戏后再试")
    
    # 启动主循环
    logger.info('启动主循环')
    root.mainloop()
    logger.info('程序退出')

if __name__ == "__main__":
    main() 