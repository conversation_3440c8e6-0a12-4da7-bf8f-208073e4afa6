#!/usr/bin/env python3
"""日志查看器工具"""

import sys
import os
import glob
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                            QWidget, QTextEdit, QComboBox, QPushButton, QLabel,
                            QFileDialog, QMessageBox, QSplitter, QListWidget,
                            QCheckBox, QLineEdit)
from PyQt5.QtCore import QTimer, Qt
from PyQt5.QtGui import QFont, QTextCursor

class LogViewer(QMainWindow):
    """日志查看器主窗口"""
    
    def __init__(self):
        super().__init__()
        self.current_log_file = None
        self.auto_refresh = False
        self.setup_ui()
        self.load_log_files()
    
    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("日志查看器")
        self.setGeometry(100, 100, 1200, 800)
        
        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout()
        central_widget.setLayout(main_layout)
        
        # 顶部控制栏
        self.setup_control_bar(main_layout)
        
        # 分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧文件列表
        self.setup_file_list(splitter)
        
        # 右侧日志内容
        self.setup_log_content(splitter)
        
        # 设置分割器比例
        splitter.setSizes([300, 900])
        
        # 自动刷新定时器
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_log_content)
    
    def setup_control_bar(self, parent_layout):
        """设置控制栏"""
        control_layout = QHBoxLayout()
        
        # 日志目录选择
        control_layout.addWidget(QLabel("日志目录:"))
        self.log_dir_combo = QComboBox()
        # 设置下拉框样式
        self.log_dir_combo.setStyleSheet("""
            QComboBox {
                font-size: 13px;
                padding: 4px 8px;
                border: 1px solid #ccc;
                border-radius: 3px;
                background-color: white;
                min-width: 120px;
            }
            QComboBox QAbstractItemView {
                font-size: 13px;
                selection-background-color: #e3f2fd;
            }
        """)
        self.log_dir_combo.setEditable(True)
        self.log_dir_combo.addItems(["logs", ".", "logs/archive"])
        control_layout.addWidget(self.log_dir_combo)
        
        # 刷新按钮
        refresh_btn = QPushButton("刷新文件列表")
        refresh_btn.clicked.connect(self.load_log_files)
        control_layout.addWidget(refresh_btn)
        
        # 自动刷新
        self.auto_refresh_check = QCheckBox("自动刷新")
        self.auto_refresh_check.toggled.connect(self.toggle_auto_refresh)
        control_layout.addWidget(self.auto_refresh_check)
        
        # 搜索框
        control_layout.addWidget(QLabel("搜索:"))
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("输入搜索关键词...")
        self.search_edit.returnPressed.connect(self.search_logs)
        control_layout.addWidget(self.search_edit)
        
        # 搜索按钮
        search_btn = QPushButton("搜索")
        search_btn.clicked.connect(self.search_logs)
        control_layout.addWidget(search_btn)
        
        # 清空按钮
        clear_btn = QPushButton("清空显示")
        clear_btn.clicked.connect(self.clear_log_display)
        control_layout.addWidget(clear_btn)
        
        control_layout.addStretch()
        parent_layout.addLayout(control_layout)
    
    def setup_file_list(self, splitter):
        """设置文件列表"""
        file_widget = QWidget()
        file_layout = QVBoxLayout()
        file_widget.setLayout(file_layout)
        
        file_layout.addWidget(QLabel("日志文件:"))
        
        self.file_list = QListWidget()
        self.file_list.itemClicked.connect(self.on_file_selected)
        file_layout.addWidget(self.file_list)
        
        # 文件操作按钮
        file_btn_layout = QHBoxLayout()
        
        open_btn = QPushButton("打开文件")
        open_btn.clicked.connect(self.open_external_file)
        file_btn_layout.addWidget(open_btn)
        
        delete_btn = QPushButton("删除文件")
        delete_btn.clicked.connect(self.delete_selected_file)
        file_btn_layout.addWidget(delete_btn)
        
        file_layout.addLayout(file_btn_layout)
        
        splitter.addWidget(file_widget)
    
    def setup_log_content(self, splitter):
        """设置日志内容显示"""
        content_widget = QWidget()
        content_layout = QVBoxLayout()
        content_widget.setLayout(content_layout)
        
        # 当前文件标签
        self.current_file_label = QLabel("当前文件: 无")
        content_layout.addWidget(self.current_file_label)
        
        # 日志内容显示
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 10))
        content_layout.addWidget(self.log_text)
        
        # 底部状态
        status_layout = QHBoxLayout()
        self.status_label = QLabel("准备就绪")
        status_layout.addWidget(self.status_label)
        status_layout.addStretch()
        
        # 跳转到底部按钮
        goto_bottom_btn = QPushButton("跳转到底部")
        goto_bottom_btn.clicked.connect(self.goto_bottom)
        status_layout.addWidget(goto_bottom_btn)
        
        content_layout.addLayout(status_layout)
        
        splitter.addWidget(content_widget)
    
    def load_log_files(self):
        """加载日志文件列表"""
        try:
            log_dir = self.log_dir_combo.currentText()
            if not os.path.exists(log_dir):
                self.status_label.setText(f"目录不存在: {log_dir}")
                return
            
            self.file_list.clear()
            
            # 查找所有日志文件
            log_patterns = [
                os.path.join(log_dir, "*.log"),
                os.path.join(log_dir, "realm_raid_*.log"),
                os.path.join(log_dir, "main.log"),
                os.path.join(log_dir, "error.log")
            ]
            
            log_files = []
            for pattern in log_patterns:
                log_files.extend(glob.glob(pattern))
            
            # 去重并排序
            log_files = sorted(set(log_files), key=os.path.getmtime, reverse=True)
            
            for log_file in log_files:
                file_info = self.get_file_info(log_file)
                self.file_list.addItem(file_info)
            
            self.status_label.setText(f"找到 {len(log_files)} 个日志文件")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载日志文件失败: {e}")
    
    def get_file_info(self, file_path):
        """获取文件信息"""
        try:
            stat = os.stat(file_path)
            size = stat.st_size
            mtime = datetime.fromtimestamp(stat.st_mtime)
            
            # 格式化大小
            if size < 1024:
                size_str = f"{size}B"
            elif size < 1024*1024:
                size_str = f"{size/1024:.1f}KB"
            else:
                size_str = f"{size/(1024*1024):.1f}MB"
            
            filename = os.path.basename(file_path)
            return f"{filename} ({size_str}, {mtime.strftime('%m-%d %H:%M')})"
            
        except Exception:
            return os.path.basename(file_path)
    
    def on_file_selected(self, item):
        """文件选择事件"""
        try:
            file_info = item.text()
            filename = file_info.split(" (")[0]  # 提取文件名
            
            log_dir = self.log_dir_combo.currentText()
            file_path = os.path.join(log_dir, filename)
            
            if os.path.exists(file_path):
                self.load_log_file(file_path)
            else:
                QMessageBox.warning(self, "警告", f"文件不存在: {file_path}")
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开文件失败: {e}")
    
    def load_log_file(self, file_path):
        """加载日志文件内容"""
        try:
            self.current_log_file = file_path
            self.current_file_label.setText(f"当前文件: {os.path.basename(file_path)}")
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            self.log_text.setPlainText(content)
            self.goto_bottom()
            
            # 更新状态
            lines = content.count('\n')
            size = len(content.encode('utf-8'))
            self.status_label.setText(f"已加载 {lines} 行, {size} 字节")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"读取文件失败: {e}")
    
    def refresh_log_content(self):
        """刷新日志内容"""
        if self.current_log_file and os.path.exists(self.current_log_file):
            self.load_log_file(self.current_log_file)
    
    def toggle_auto_refresh(self, checked):
        """切换自动刷新"""
        self.auto_refresh = checked
        if checked:
            self.refresh_timer.start(2000)  # 每2秒刷新
            self.status_label.setText("自动刷新已启用")
        else:
            self.refresh_timer.stop()
            self.status_label.setText("自动刷新已禁用")
    
    def search_logs(self):
        """搜索日志"""
        keyword = self.search_edit.text().strip()
        if not keyword:
            return
        
        content = self.log_text.toPlainText()
        if not content:
            return
        
        # 高亮搜索结果
        cursor = self.log_text.textCursor()
        cursor.movePosition(QTextCursor.Start)
        
        found = self.log_text.find(keyword)
        if found:
            self.status_label.setText(f"找到关键词: {keyword}")
        else:
            self.status_label.setText(f"未找到关键词: {keyword}")
    
    def clear_log_display(self):
        """清空日志显示"""
        self.log_text.clear()
        self.current_log_file = None
        self.current_file_label.setText("当前文件: 无")
        self.status_label.setText("显示已清空")
    
    def goto_bottom(self):
        """跳转到底部"""
        cursor = self.log_text.textCursor()
        cursor.movePosition(QTextCursor.End)
        self.log_text.setTextCursor(cursor)
    
    def open_external_file(self):
        """打开外部文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择日志文件", ".", "日志文件 (*.log);;所有文件 (*.*)"
        )
        if file_path:
            self.load_log_file(file_path)
    
    def delete_selected_file(self):
        """删除选中的文件"""
        current_item = self.file_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "警告", "请先选择要删除的文件")
            return
        
        file_info = current_item.text()
        filename = file_info.split(" (")[0]
        log_dir = self.log_dir_combo.currentText()
        file_path = os.path.join(log_dir, filename)
        
        reply = QMessageBox.question(
            self, "确认删除", 
            f"确定要删除文件 {filename} 吗？\n此操作不可恢复！",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                os.remove(file_path)
                self.load_log_files()  # 刷新文件列表
                self.status_label.setText(f"已删除文件: {filename}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"删除文件失败: {e}")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    viewer = LogViewer()
    viewer.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
