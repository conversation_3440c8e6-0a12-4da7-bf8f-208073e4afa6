#!/usr/bin/env python3
"""增强版OCR工具 - 提高识别准确率"""

import cv2
import numpy as np
import logging
from typing import List, Tuple, Dict, Optional
import re
import os
from datetime import datetime

# 尝试导入多个OCR引擎
OCR_ENGINES = {}

try:
    import easyocr
    OCR_ENGINES['easyocr'] = True
except ImportError:
    OCR_ENGINES['easyocr'] = False

try:
    import paddleocr
    OCR_ENGINES['paddleocr'] = True
except ImportError:
    OCR_ENGINES['paddleocr'] = False

class EnhancedOCR:
    """增强版OCR识别器"""
    
    def __init__(self):
        self.logger = logging.getLogger('EnhancedOCR')
        self.engines = {}
        
        # 初始化可用的OCR引擎
        self._init_engines()
        
        # 预处理策略
        self.preprocessing_strategies = [
            self._preprocess_standard,
            self._preprocess_high_contrast,
            self._preprocess_adaptive,
            self._preprocess_morphology,
            self._preprocess_multi_scale
        ]
    
    def _init_engines(self):
        """初始化OCR引擎"""
        # 初始化EasyOCR
        if OCR_ENGINES['easyocr']:
            try:
                self.engines['easyocr'] = easyocr.Reader(['ch_sim', 'en'], gpu=False)
                self.logger.info("✅ EasyOCR初始化成功")
            except Exception as e:
                self.logger.error(f"❌ EasyOCR初始化失败: {e}")
        
        # 初始化PaddleOCR
        if OCR_ENGINES['paddleocr']:
            try:
                self.engines['paddleocr'] = paddleocr.PaddleOCR(
                    use_angle_cls=True,
                    lang='ch',
                    use_gpu=False,
                    show_log=False
                )
                self.logger.info("✅ PaddleOCR初始化成功")
            except Exception as e:
                self.logger.error(f"❌ PaddleOCR初始化失败: {e}")
        
        if not self.engines:
            self.logger.warning("⚠️ 没有可用的OCR引擎，请安装 easyocr 或 paddleocr")
    
    def is_available(self) -> bool:
        """检查是否有可用的OCR引擎"""
        return len(self.engines) > 0
    
    def _preprocess_standard(self, image: np.ndarray) -> np.ndarray:
        """标准预处理"""
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()
        
        # CLAHE增强
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        enhanced = clahe.apply(gray)
        
        return enhanced
    
    def _preprocess_high_contrast(self, image: np.ndarray) -> np.ndarray:
        """高对比度预处理"""
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()
        
        # 增强对比度
        clahe = cv2.createCLAHE(clipLimit=4.0, tileGridSize=(8,8))
        enhanced = clahe.apply(gray)
        
        # 二值化
        _, binary = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        return binary
    
    def _preprocess_adaptive(self, image: np.ndarray) -> np.ndarray:
        """自适应预处理"""
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()
        
        # 高斯模糊
        blurred = cv2.GaussianBlur(gray, (3, 3), 0)
        
        # 自适应阈值
        adaptive = cv2.adaptiveThreshold(
            blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
            cv2.THRESH_BINARY, 11, 2
        )
        
        return adaptive
    
    def _preprocess_morphology(self, image: np.ndarray) -> np.ndarray:
        """形态学预处理"""
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()
        
        # 增强对比度
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
        enhanced = clahe.apply(gray)
        
        # 二值化
        _, binary = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # 形态学操作
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
        morphed = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        
        return morphed
    
    def _preprocess_multi_scale(self, image: np.ndarray) -> np.ndarray:
        """多尺度预处理"""
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()
        
        # 放大图像提高小文字识别
        height, width = gray.shape
        scaled = cv2.resize(gray, (width * 2, height * 2), interpolation=cv2.INTER_CUBIC)
        
        # 增强处理
        clahe = cv2.createCLAHE(clipLimit=2.5, tileGridSize=(8,8))
        enhanced = clahe.apply(scaled)
        
        return enhanced
    
    def _recognize_with_easyocr(self, image: np.ndarray) -> List[Dict]:
        """使用EasyOCR识别"""
        if 'easyocr' not in self.engines:
            return []
        
        try:
            results = self.engines['easyocr'].readtext(image)
            formatted_results = []
            
            for result in results:
                bbox = result[0]
                text = result[1]
                confidence = result[2]
                
                # 转换边界框格式
                x1 = int(min([p[0] for p in bbox]))
                y1 = int(min([p[1] for p in bbox]))
                x2 = int(max([p[0] for p in bbox]))
                y2 = int(max([p[1] for p in bbox]))
                
                formatted_results.append({
                    'text': text,
                    'bbox': (x1, y1, x2, y2),
                    'confidence': confidence,
                    'engine': 'easyocr'
                })
            
            return formatted_results
            
        except Exception as e:
            self.logger.error(f"EasyOCR识别失败: {e}")
            return []
    
    def _recognize_with_paddleocr(self, image: np.ndarray) -> List[Dict]:
        """使用PaddleOCR识别"""
        if 'paddleocr' not in self.engines:
            return []
        
        try:
            results = self.engines['paddleocr'].ocr(image, cls=True)
            formatted_results = []
            
            if results and results[0]:
                for line in results[0]:
                    bbox = line[0]
                    text = line[1][0]
                    confidence = line[1][1]
                    
                    # 转换边界框格式
                    x1 = int(min([p[0] for p in bbox]))
                    y1 = int(min([p[1] for p in bbox]))
                    x2 = int(max([p[0] for p in bbox]))
                    y2 = int(max([p[1] for p in bbox]))
                    
                    formatted_results.append({
                        'text': text,
                        'bbox': (x1, y1, x2, y2),
                        'confidence': confidence,
                        'engine': 'paddleocr'
                    })
            
            return formatted_results
            
        except Exception as e:
            self.logger.error(f"PaddleOCR识别失败: {e}")
            return []
    
    def recognize_enhanced(self, image: np.ndarray, target_chapter: int = None) -> List[Tuple[int, Tuple[int, int, int, int]]]:
        """增强识别 - 多引擎 + 多预处理策略"""
        if not self.is_available():
            self.logger.warning("没有可用的OCR引擎")
            return []
        
        self.logger.info(f"🔍 启动增强识别，目标章节: {target_chapter}")
        
        all_results = []
        
        # 尝试不同的预处理策略
        for i, preprocess_func in enumerate(self.preprocessing_strategies):
            strategy_name = preprocess_func.__name__.replace('_preprocess_', '')
            self.logger.debug(f"📸 尝试预处理策略: {strategy_name}")
            
            try:
                # 预处理图像
                processed_image = preprocess_func(image)
                
                # 保存调试图像
                self._save_debug_image(processed_image, f"preprocess_{strategy_name}")
                
                # 使用所有可用引擎识别
                for engine_name in self.engines.keys():
                    if engine_name == 'easyocr':
                        results = self._recognize_with_easyocr(processed_image)
                    elif engine_name == 'paddleocr':
                        results = self._recognize_with_paddleocr(processed_image)
                    else:
                        continue
                    
                    if results:
                        self.logger.debug(f"  ✅ {strategy_name} + {engine_name}: {len(results)} 个结果")
                        all_results.extend(results)
                    else:
                        self.logger.debug(f"  ❌ {strategy_name} + {engine_name}: 无结果")
                        
            except Exception as e:
                self.logger.warning(f"预处理策略 {strategy_name} 失败: {e}")
        
        # 提取章节号码
        chapter_results = self._extract_chapters_from_results(all_results, target_chapter)
        
        # 结果去重和投票
        final_results = self._vote_and_filter_chapters(chapter_results, target_chapter)
        
        self.logger.info(f"🎯 增强识别完成，识别到 {len(final_results)} 个章节")
        return final_results
    
    def _extract_chapters_from_results(self, results: List[Dict], target_chapter: int = None) -> List[Dict]:
        """从OCR结果中提取章节信息"""
        chapter_results = []
        
        for result in results:
            text = result['text']
            numbers = self._extract_numbers_from_text(text)
            
            for number in numbers:
                # 章节号码范围检查
                if 1 <= number <= 30:
                    chapter_results.append({
                        'chapter': number,
                        'bbox': result['bbox'],
                        'confidence': result['confidence'],
                        'engine': result['engine'],
                        'original_text': text
                    })
        
        return chapter_results
    
    def _extract_numbers_from_text(self, text: str) -> List[int]:
        """从文本中提取数字"""
        numbers = []
        
        # 阿拉伯数字
        arabic_numbers = re.findall(r'\d+', text)
        for num_str in arabic_numbers:
            if num_str.isdigit():
                numbers.append(int(num_str))
        
        # 中文数字章节格式
        chapter_patterns = [
            r'第([一二三四五六七八九十]+)章',
            r'第([一二三四五六七八九十]+)',
            r'([一二三四五六七八九十]+)章',
            r'第(\d+)章',
            r'第(\d+)',
            r'(\d+)章',
        ]
        
        for pattern in chapter_patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                if re.match(r'[一二三四五六七八九十]+', match):
                    arabic_num = self._chinese_to_arabic(match)
                    if arabic_num:
                        numbers.append(arabic_num)
                elif match.isdigit():
                    numbers.append(int(match))
        
        return numbers
    
    def _chinese_to_arabic(self, chinese_num: str) -> Optional[int]:
        """中文数字转阿拉伯数字"""
        chinese_digits = {
            '一': 1, '二': 2, '三': 3, '四': 4, '五': 5,
            '六': 6, '七': 7, '八': 8, '九': 9, '十': 10
        }
        
        try:
            if chinese_num in chinese_digits:
                return chinese_digits[chinese_num]
            
            if '十' in chinese_num:
                if chinese_num == '十':
                    return 10
                elif chinese_num.startswith('十'):
                    return 10 + chinese_digits.get(chinese_num[1], 0)
                elif chinese_num.endswith('十'):
                    return chinese_digits.get(chinese_num[0], 0) * 10
                else:
                    parts = chinese_num.split('十')
                    if len(parts) == 2:
                        tens = chinese_digits.get(parts[0], 0) * 10
                        ones = chinese_digits.get(parts[1], 0)
                        return tens + ones
            
            return None
            
        except Exception as e:
            self.logger.debug(f"中文数字转换失败: {chinese_num}, 错误: {e}")
            return None
    
    def _vote_and_filter_chapters(self, results: List[Dict], target_chapter: int = None) -> List[Tuple[int, Tuple[int, int, int, int]]]:
        """投票筛选章节结果"""
        if not results:
            return []
        
        # 按章节号分组
        chapter_groups = {}
        for result in results:
            chapter = result['chapter']
            if chapter not in chapter_groups:
                chapter_groups[chapter] = []
            chapter_groups[chapter].append(result)
        
        final_results = []
        for chapter, group in chapter_groups.items():
            # 计算平均置信度
            avg_confidence = sum(r['confidence'] for r in group) / len(group)
            
            # 计算平均边界框
            avg_bbox = self._calculate_average_bbox([r['bbox'] for r in group])
            
            # 投票权重：出现次数 + 平均置信度
            vote_weight = len(group) + avg_confidence
            
            final_results.append({
                'chapter': chapter,
                'bbox': avg_bbox,
                'confidence': avg_confidence,
                'vote_count': len(group),
                'vote_weight': vote_weight
            })
        
        # 按投票权重排序
        final_results.sort(key=lambda x: x['vote_weight'], reverse=True)
        
        # 转换为返回格式
        return [(r['chapter'], r['bbox']) for r in final_results]
    
    def _calculate_average_bbox(self, bboxes: List[Tuple[int, int, int, int]]) -> Tuple[int, int, int, int]:
        """计算平均边界框"""
        if not bboxes:
            return (0, 0, 0, 0)
        
        x1_avg = sum(bbox[0] for bbox in bboxes) // len(bboxes)
        y1_avg = sum(bbox[1] for bbox in bboxes) // len(bboxes)
        x2_avg = sum(bbox[2] for bbox in bboxes) // len(bboxes)
        y2_avg = sum(bbox[3] for bbox in bboxes) // len(bboxes)
        
        return (x1_avg, y1_avg, x2_avg, y2_avg)
    
    def _save_debug_image(self, image: np.ndarray, name: str):
        """保存调试图像"""
        try:
            debug_dir = "debug_screenshots"
            os.makedirs(debug_dir, exist_ok=True)
            
            timestamp = int(datetime.now().timestamp())
            filename = f"{debug_dir}/enhanced_ocr_{name}_{timestamp}.png"
            cv2.imwrite(filename, image)
            
        except Exception as e:
            self.logger.debug(f"保存调试图像失败: {e}")

# 使用示例
def test_enhanced_ocr():
    """测试增强OCR"""
    ocr = EnhancedOCR()
    
    if not ocr.is_available():
        print("❌ 没有可用的OCR引擎")
        print("请安装: pip install easyocr 或 pip install paddlepaddle paddleocr")
        return
    
    print("✅ 增强OCR初始化成功")
    print(f"可用引擎: {list(ocr.engines.keys())}")
    
    # 这里可以测试实际图像
    # image = cv2.imread("test_image.png")
    # results = ocr.recognize_enhanced(image, target_chapter=28)
    # print(f"识别结果: {results}")

if __name__ == "__main__":
    test_enhanced_ocr()
