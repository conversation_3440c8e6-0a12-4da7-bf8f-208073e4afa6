# 结界突破模板准备完整指南

## 📋 **必需模板清单**

### 🎯 **核心导航模板 (必须)**

#### 1. **主界面导航**
```
📁 templates/realm_raid/
├── guild_button.png          # 阴阳寮按钮
└── realm_raid_button.png     # 结界突破按钮
```

**截图要求:**
- **guild_button.png**: 主界面右下角的"阴阳寮"按钮
- **realm_raid_button.png**: 阴阳寮界面中的"结界突破"按钮

#### 2. **结界突破界面**
```
📁 templates/realm_raid/
├── realm_list_area.png       # 结界列表区域标识
├── challenge_button.png      # 挑战按钮
└── attack_button.png         # 进攻按钮
```

**截图要求:**
- **realm_list_area.png**: 结界列表的特征区域（用于确认进入正确界面）
- **challenge_button.png**: 选择结界后的"挑战"按钮
- **attack_button.png**: 确认挑战后的"进攻"按钮

### ⚔️ **战斗流程模板 (必须)**

#### 3. **战斗控制**
```
📁 templates/realm_raid/
├── battle_start.png          # 战斗开始标识
├── auto_battle.png           # 自动战斗按钮
└── battle_end.png            # 战斗结束标识
```

**截图要求:**
- **battle_start.png**: 战斗开始时的特征界面元素
- **auto_battle.png**: 自动战斗按钮（通常在右下角）
- **battle_end.png**: 战斗结束时的特征标识

#### 4. **结果处理**
```
📁 templates/realm_raid/
├── victory.png               # 胜利标识
├── defeat.png                # 失败标识
├── confirm_button.png        # 确认按钮
└── back_button.png           # 返回按钮
```

**截图要求:**
- **victory.png**: 胜利界面的"胜利"文字或图标
- **defeat.png**: 失败界面的"失败"文字或图标
- **confirm_button.png**: 通用的"确认"按钮
- **back_button.png**: 返回上级界面的按钮

### 🎫 **状态检测模板 (重要)**

#### 5. **特殊状态**
```
📁 templates/realm_raid/
├── no_tickets.png            # 没有突破券提示
├── cooldown.png              # 冷却时间标识
└── refresh_button.png        # 刷新按钮
```

**截图要求:**
- **no_tickets.png**: "突破券不足"或类似提示文字
- **cooldown.png**: 结界冷却中的标识（通常是时间显示）
- **refresh_button.png**: 刷新结界列表的按钮

## 📸 **模板截图指南**

### 🎯 **截图基本要求**

1. **分辨率**: 建议使用1920×1080分辨率截图
2. **格式**: PNG格式，保持透明度
3. **尺寸**: 尽量截取最小的特征区域
4. **清晰度**: 确保图像清晰，无模糊

### 📐 **截图技巧**

#### **1. 按钮类模板**
```
✅ 推荐: 只截取按钮本身
❌ 避免: 包含过多背景
```

#### **2. 文字类模板**
```
✅ 推荐: 截取完整文字区域
❌ 避免: 文字被截断
```

#### **3. 状态类模板**
```
✅ 推荐: 包含足够的上下文特征
❌ 避免: 过于通用的图像
```

### 🔧 **截图工具推荐**

1. **Windows**: Snipping Tool 或 Win + Shift + S
2. **第三方**: PicPick、FastStone Capture
3. **游戏内**: 如果游戏支持，使用游戏内截图功能

## 📂 **目录结构创建**

### 1. **创建模板目录**
```bash
# 在项目根目录执行
mkdir -p templates/realm_raid
```

### 2. **验证目录结构**
```
探索脚本/
├── templates/
│   ├── explore/              # 探索功能模板
│   └── realm_raid/           # 结界突破模板 ← 新建
│       ├── guild_button.png
│       ├── realm_raid_button.png
│       ├── challenge_button.png
│       ├── attack_button.png
│       ├── auto_battle.png
│       ├── victory.png
│       ├── defeat.png
│       ├── confirm_button.png
│       └── ...
```

## 🎯 **模板制作步骤**

### **第一阶段: 导航模板 (优先级: 🔴 最高)**

1. **启动阴阳师游戏**
2. **截取主界面的"阴阳寮"按钮** → `guild_button.png`
3. **进入阴阳寮界面**
4. **截取"结界突破"按钮** → `realm_raid_button.png`
5. **进入结界突破界面**
6. **截取结界列表特征区域** → `realm_list_area.png`

### **第二阶段: 操作模板 (优先级: 🟡 高)**

1. **选择一个结界**
2. **截取"挑战"按钮** → `challenge_button.png`
3. **点击挑战**
4. **截取"进攻"按钮** → `attack_button.png`
5. **进入战斗**
6. **截取"自动战斗"按钮** → `auto_battle.png`

### **第三阶段: 结果模板 (优先级: 🟢 中)**

1. **等待战斗结束**
2. **截取胜利界面** → `victory.png`
3. **如果失败，截取失败界面** → `defeat.png`
4. **截取"确认"按钮** → `confirm_button.png`
5. **截取"返回"按钮** → `back_button.png`

### **第四阶段: 状态模板 (优先级: 🔵 低)**

1. **等待突破券用完**
2. **截取"突破券不足"提示** → `no_tickets.png`
3. **查找冷却中的结界**
4. **截取冷却标识** → `cooldown.png`
5. **截取刷新按钮** → `refresh_button.png`

## ✅ **模板验证方法**

### 1. **使用测试脚本验证**
```python
# 创建模板测试脚本
python test_templates.py
```

### 2. **手动验证步骤**
1. 将模板放入正确目录
2. 启动结界突破功能
3. 观察控制台日志
4. 检查是否能正确识别模板

### 3. **常见问题排查**
```
❌ 模板识别失败
✅ 检查文件名是否正确
✅ 检查图像是否清晰
✅ 检查截图是否包含足够特征

❌ 点击位置不准确
✅ 重新截取更精确的模板
✅ 调整rel_click_point参数
```

## 🚀 **快速开始**

### **最小可用模板集 (5个文件)**
如果时间有限，优先准备这5个核心模板：

1. `guild_button.png` - 进入阴阳寮
2. `realm_raid_button.png` - 进入结界突破
3. `challenge_button.png` - 挑战结界
4. `auto_battle.png` - 自动战斗
5. `victory.png` - 胜利确认

**有了这5个模板，基本功能就能运行！**

### **完整功能模板集 (15个文件)**
为了获得最佳体验，建议准备所有15个模板文件。

## 📞 **获取帮助**

### **模板制作遇到问题？**

1. **查看探索功能模板** - `templates/explore/` 目录有很好的参考
2. **查看日志输出** - 运行时观察控制台的模板匹配信息
3. **调整匹配阈值** - 在配置文件中降低threshold值
4. **使用调试模式** - 启用debug模式查看匹配过程

## 🎊 **总结**

### ✅ **准备完成后您将拥有:**
- **完整的结界突破自动化** - 从导航到战斗的全流程
- **智能状态检测** - 自动处理各种特殊情况
- **稳定的模板匹配** - 高准确率的图像识别
- **灵活的配置选项** - 可根据需要调整参数

### 🎯 **开始制作模板吧！**
按照优先级顺序，从导航模板开始，逐步完善整个模板库。每完成一个阶段，都可以测试相应的功能是否正常工作。

**祝您制作顺利！** 🚀
