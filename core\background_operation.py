import win32gui
import win32api
import win32con
import win32ui
import win32process
import time
import random
import numpy as np
import cv2
import logging
from typing import Tuple, Optional, Union, List
from ctypes import windll

class BackgroundOperation:
    """后台窗口操作核心类
    
    允许在不将窗口设为前台的情况下进行截图和鼠标操作
    """
    
    def __init__(self, hwnd=None):
        """初始化后台操作模块
        
        Args:
            hwnd: 窗口句柄，如果为None则需要后续设置
        """
        self.hwnd = hwnd
        self.logger = logging.getLogger('阴阳师辅助工具.后台操作')
        self.window_rect = None
        self.client_rect = None
        self.client_offset = (0, 0)
        
        if hwnd:
            self.update_window_info()
    
    def set_hwnd(self, hwnd):
        """设置窗口句柄
        
        Args:
            hwnd: 窗口句柄
            
        Returns:
            bool: 是否设置成功
        """
        if hwnd and win32gui.IsWindow(hwnd):
            self.hwnd = hwnd
            self.update_window_info()
            return True
        self.logger.error(f"无效的窗口句柄: {hwnd}")
        return False
    
    def update_window_info(self):
        """更新窗口相关信息"""
        if not self.hwnd:
            self.logger.warning("未设置窗口句柄，无法更新窗口信息")
            return
            
        try:
            # 获取窗口矩形
            self.window_rect = win32gui.GetWindowRect(self.hwnd)
            left, top, right, bottom = self.window_rect
            
            # 获取客户区矩形
            self.client_rect = win32gui.GetClientRect(self.hwnd)
            client_width, client_height = self.client_rect[2], self.client_rect[3]
            
            # 获取客户区在屏幕上的位置
            client_left, client_top = win32gui.ClientToScreen(self.hwnd, (0, 0))
            
            # 计算客户区偏移
            self.client_offset = (client_left - left, client_top - top)
            
            self.logger.debug(f"窗口信息已更新 - 窗口位置: {self.window_rect}, 客户区大小: {client_width}x{client_height}, 客户区偏移: {self.client_offset}")
        except Exception as e:
            self.logger.error(f"更新窗口信息失败: {str(e)}")
    
    def capture_background(self) -> Optional[np.ndarray]:
        """在后台捕获窗口截图
        
        使用设备上下文方法捕获窗口截图，无需将窗口设为前台
        如果BitBlt方法失败，将使用PrintWindow API作为备选方案
        
        Returns:
            np.ndarray: 窗口截图，失败则返回None
        """
        if not self.hwnd:
            self.logger.error("未设置窗口句柄，无法截图")
            return None
        
        # 更新窗口信息
        self.update_window_info()
        
        if not self.client_rect:
            self.logger.error("无法获取客户区域")
            return None
        
        # 获取客户区域尺寸
        width, height = self.client_rect[2], self.client_rect[3]
        
        # 方法1: 使用windll.gdi32.BitBlt (最优先尝试)
        try:
            # 检查窗口是否存在
            if not win32gui.IsWindow(self.hwnd):
                self.logger.error("游戏窗口不存在或已关闭")
                return None
                
            # 获取窗口大小
            left, top, right, bottom = self.window_rect
            window_width = right - left
            window_height = bottom - top
            
            self.logger.info(f"尝试windll.gdi32.BitBlt后台截图, 窗口尺寸: {window_width}x{window_height}, 位置: ({left}, {top}, {right}, {bottom})")
            
            # 创建设备上下文
            hwnd_dc = win32gui.GetWindowDC(self.hwnd)
            mfc_dc = win32ui.CreateDCFromHandle(hwnd_dc)
            save_dc = mfc_dc.CreateCompatibleDC()
            
            # 创建位图对象
            save_bitmap = win32ui.CreateBitmap()
            save_bitmap.CreateCompatibleBitmap(mfc_dc, width, height)
            save_dc.SelectObject(save_bitmap)
            
            # 使用windll.gdi32.BitBlt复制窗口内容到位图
            client_x, client_y = self.client_offset
            result = windll.gdi32.BitBlt(
                save_dc.GetSafeHdc(), 
                0, 0, 
                width, height, 
                mfc_dc.GetSafeHdc(), 
                client_x, client_y, 
                win32con.SRCCOPY
            )
            
            if result:
                # 转换为OpenCV格式
                bmpinfo = save_bitmap.GetInfo()
                bmpstr = save_bitmap.GetBitmapBits(True)
                img = np.frombuffer(bmpstr, dtype='uint8')
                img.shape = (bmpinfo['bmHeight'], bmpinfo['bmWidth'], 4)
                img = img[:, :, :3]  # 丢弃Alpha通道
                img = cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)
                
                # 检查图像是否为空或全黑
                if img is None or np.mean(img) < 5:
                    self.logger.warning("windll.gdi32.BitBlt后台截图得到空白或黑屏，尝试其他方法")
                else:
                    self.logger.info("windll.gdi32.BitBlt后台截图成功")
                    
                    # 释放资源
                    win32gui.DeleteObject(save_bitmap.GetHandle())
                    save_dc.DeleteDC()
                    mfc_dc.DeleteDC()
                    win32gui.ReleaseDC(self.hwnd, hwnd_dc)
                    
                    return img
            else:
                self.logger.warning("windll.gdi32.BitBlt操作失败，尝试其他方法")
                
            # 释放资源
            win32gui.DeleteObject(save_bitmap.GetHandle())
            save_dc.DeleteDC()
            mfc_dc.DeleteDC()
            win32gui.ReleaseDC(self.hwnd, hwnd_dc)
                
        except Exception as e:
            self.logger.warning(f"windll.gdi32.BitBlt后台截图失败: {str(e)}，尝试其他方法")
        
        # 方法2: 使用win32gui.BitBlt (第二尝试)
        try:
            # 创建设备上下文
            hwnd_dc = win32gui.GetWindowDC(self.hwnd)
            mfc_dc = win32ui.CreateDCFromHandle(hwnd_dc)
            save_dc = mfc_dc.CreateCompatibleDC()
            
            # 创建位图对象
            save_bitmap = win32ui.CreateBitmap()
            save_bitmap.CreateCompatibleBitmap(mfc_dc, width, height)
            save_dc.SelectObject(save_bitmap)
            
            try:
                # 从客户区偏移开始复制
                client_x, client_y = self.client_offset
                
                # 复制窗口客户区域到内存DC
                result = win32gui.BitBlt(
                    save_dc.GetSafeHdc(), 0, 0, width, height,
                    mfc_dc.GetSafeHdc(), client_x, client_y, win32con.SRCCOPY
                )
                
                if result:
                    # 获取位图信息
                    bmp_info = save_bitmap.GetInfo()
                    bmp_str = save_bitmap.GetBitmapBits(True)
                    
                    # 转换为numpy数组
                    img = np.frombuffer(bmp_str, dtype='uint8')
                    img.shape = (height, width, 4)
                    img = cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)
                    
                    # 检查图像是否为空或全黑
                    if img is None or np.mean(img) < 5:
                        self.logger.warning("win32gui.BitBlt后台截图得到空白或黑屏，尝试其他方法")
                    else:
                        self.logger.debug(f"win32gui.BitBlt后台截图完成: 尺寸[{width}x{height}]")
                        return img
                else:
                    self.logger.warning("win32gui.BitBlt操作失败，将尝试PrintWindow方法")
            finally:
                # 清理资源
                win32gui.DeleteObject(save_bitmap.GetHandle())
                save_dc.DeleteDC()
                mfc_dc.DeleteDC()
                win32gui.ReleaseDC(self.hwnd, hwnd_dc)
                
        except Exception as e:
            self.logger.warning(f"win32gui.BitBlt后台截图失败: {str(e)}，将尝试PrintWindow方法")
        
        # 方法3: 使用PrintWindow (备选方案)
        try:
            # 创建设备上下文
            hwnd_dc = win32gui.GetWindowDC(self.hwnd)
            mfc_dc = win32ui.CreateDCFromHandle(hwnd_dc)
            save_dc = mfc_dc.CreateCompatibleDC()
            
            # 获取整个窗口大小
            left, top, right, bottom = win32gui.GetWindowRect(self.hwnd)
            w = right - left
            h = bottom - top
            
            # 创建位图对象
            save_bitmap = win32ui.CreateBitmap()
            save_bitmap.CreateCompatibleBitmap(mfc_dc, w, h)
            save_dc.SelectObject(save_bitmap)
            
            try:
                # 使用PrintWindow捕获整个窗口
                result = win32gui.PrintWindow(self.hwnd, save_dc.GetSafeHdc(), 0)
                
                if result:
                    # 获取位图信息
                    bmp_info = save_bitmap.GetInfo()
                    bmp_str = save_bitmap.GetBitmapBits(True)
                    
                    # 转换为numpy数组
                    img = np.frombuffer(bmp_str, dtype='uint8')
                    img.shape = (h, w, 4)
                    img = cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)
                    
                    # 检查图像是否为空或全黑
                    if img is None or np.mean(img) < 5:
                        self.logger.warning("PrintWindow后台截图得到空白或黑屏")
                        return None
                    
                    # 裁剪出客户区
                    client_x, client_y = self.client_offset
                    if client_x >= 0 and client_y >= 0 and client_x + width <= w and client_y + height <= h:
                        img = img[client_y:client_y+height, client_x:client_x+width]
                        self.logger.debug(f"PrintWindow后台截图完成并裁剪: 尺寸[{width}x{height}]")
                        return img
                    else:
                        self.logger.debug(f"PrintWindow后台截图完成但未裁剪: 尺寸[{w}x{h}]")
                        return img
                else:
                    self.logger.error("PrintWindow操作失败")
            finally:
                # 清理资源
                win32gui.DeleteObject(save_bitmap.GetHandle())
                save_dc.DeleteDC()
                mfc_dc.DeleteDC()
                win32gui.ReleaseDC(self.hwnd, hwnd_dc)
                
        except Exception as e:
            self.logger.error(f"PrintWindow后台截图失败: {str(e)}")
            
        return None

    def click_background(self, x: int, y: int, delay: float = 0.1, use_random: bool = True, offset_range: int = 5) -> bool:
        """在后台进行鼠标点击
        
        在不将窗口设为前台的情况下执行鼠标点击操作
        
        Args:
            x: 客户区中的X坐标
            y: 客户区中的Y坐标
            delay: 点击后延迟时间(秒)
            use_random: 是否使用随机偏移
            offset_range: 随机偏移范围(像素)
            
        Returns:
            bool: 点击是否成功
        """
        if not self.hwnd:
            self.logger.error("未设置窗口句柄，无法点击")
            return False
        
        try:
            # 更新窗口信息
            self.update_window_info()
            
            # 应用随机偏移
            if use_random:
                offset_x = random.randint(-offset_range, offset_range)
                offset_y = random.randint(-offset_range, offset_range)
                x += offset_x
                y += offset_y
            
            # 将客户区坐标转换为屏幕坐标
            screen_x, screen_y = win32gui.ClientToScreen(self.hwnd, (x, y))
            
            # 获取窗口线程ID和进程ID
            _, pid = win32process.GetWindowThreadProcessId(self.hwnd)
            
            # 发送点击消息给窗口
            lParam = win32api.MAKELONG(x, y)
            
            # 发送鼠标按下消息
            win32gui.PostMessage(self.hwnd, win32con.WM_LBUTTONDOWN, win32con.MK_LBUTTON, lParam)
            time.sleep(0.05)  # 短暂等待模拟按下
            
            # 发送鼠标释放消息
            win32gui.PostMessage(self.hwnd, win32con.WM_LBUTTONUP, 0, lParam)
            
            self.logger.debug(f"后台点击成功: 客户区坐标[{x},{y}] -> 屏幕坐标[{screen_x},{screen_y}]")
            
            # 延迟
            if delay > 0:
                time.sleep(delay)
                
            return True
        except Exception as e:
            self.logger.error(f"后台点击操作失败: {str(e)}")
            return False
            
    def right_click_background(self, x: int, y: int, delay: float = 0.1) -> bool:
        """在后台进行鼠标右键点击
        
        Args:
            x: 客户区中的X坐标
            y: 客户区中的Y坐标
            delay: 点击后延迟时间(秒)
            
        Returns:
            bool: 点击是否成功
        """
        if not self.hwnd:
            self.logger.error("未设置窗口句柄，无法点击")
            return False
        
        try:
            # 更新窗口信息
            self.update_window_info()
            
            # 将客户区坐标转换为屏幕坐标 (仅用于日志)
            screen_x, screen_y = win32gui.ClientToScreen(self.hwnd, (x, y))
            
            # 构造坐标参数
            lParam = win32api.MAKELONG(x, y)
            
            # 发送鼠标右键按下消息
            win32gui.PostMessage(self.hwnd, win32con.WM_RBUTTONDOWN, win32con.MK_RBUTTON, lParam)
            time.sleep(0.05)  # 短暂等待模拟按下
            
            # 发送鼠标右键释放消息
            win32gui.PostMessage(self.hwnd, win32con.WM_RBUTTONUP, 0, lParam)
            
            self.logger.debug(f"后台右键点击成功: 客户区坐标[{x},{y}] -> 屏幕坐标[{screen_x},{screen_y}]")
            
            # 延迟
            if delay > 0:
                time.sleep(delay)
                
            return True
        except Exception as e:
            self.logger.error(f"后台右键点击操作失败: {str(e)}")
            return False
    
    def drag_background(self, start_x: int, start_y: int, end_x: int, end_y: int, 
                        duration: float = 0.5, delay: float = 0.1) -> bool:
        """在后台执行鼠标拖拽
        
        Args:
            start_x: 开始点X坐标
            start_y: 开始点Y坐标
            end_x: 结束点X坐标
            end_y: 结束点Y坐标
            duration: 拖拽持续时间(秒)
            delay: 完成后延迟时间(秒)
            
        Returns:
            bool: 拖拽是否成功
        """
        if not self.hwnd:
            self.logger.error("未设置窗口句柄，无法执行拖拽")
            return False
        
        try:
            # 更新窗口信息
            self.update_window_info()
            
            # 将开始结束坐标转换为lParam格式
            start_lParam = win32api.MAKELONG(start_x, start_y)
            end_lParam = win32api.MAKELONG(end_x, end_y)
            
            # 计算移动步数
            steps = max(int(duration * 10), 5)  # 至少5步
            dx = (end_x - start_x) / steps
            dy = (end_y - start_y) / steps
            
            # 发送鼠标按下消息
            win32gui.PostMessage(self.hwnd, win32con.WM_LBUTTONDOWN, win32con.MK_LBUTTON, start_lParam)
            time.sleep(0.05)  # 短暂等待模拟按下
            
            # 发送鼠标移动消息
            for i in range(1, steps):
                current_x = int(start_x + dx * i)
                current_y = int(start_y + dy * i)
                current_lParam = win32api.MAKELONG(current_x, current_y)
                
                win32gui.PostMessage(self.hwnd, win32con.WM_MOUSEMOVE, win32con.MK_LBUTTON, current_lParam)
                time.sleep(duration / steps)
            
            # 发送最后一个移动消息确保到达目标位置
            win32gui.PostMessage(self.hwnd, win32con.WM_MOUSEMOVE, win32con.MK_LBUTTON, end_lParam)
            time.sleep(0.05)
            
            # 发送鼠标释放消息
            win32gui.PostMessage(self.hwnd, win32con.WM_LBUTTONUP, 0, end_lParam)
            
            self.logger.debug(f"后台拖拽成功: 从 [{start_x},{start_y}] 到 [{end_x},{end_y}]")
            
            # 延迟
            if delay > 0:
                time.sleep(delay)
                
            return True
        except Exception as e:
            self.logger.error(f"后台拖拽操作失败: {str(e)}")
            return False
    
    def send_key(self, key_code: int, delay: float = 0.1) -> bool:
        """发送按键消息
        
        Args:
            key_code: 按键代码
            delay: 按键后延迟时间(秒)
            
        Returns:
            bool: 是否成功
        """
        if not self.hwnd:
            self.logger.error("未设置窗口句柄，无法发送按键")
            return False
        
        try:
            # 发送按键按下消息
            win32gui.PostMessage(self.hwnd, win32con.WM_KEYDOWN, key_code, 0)
            time.sleep(0.05)  # 短暂等待模拟按下
            
            # 发送按键释放消息
            win32gui.PostMessage(self.hwnd, win32con.WM_KEYUP, key_code, 0)
            
            self.logger.debug(f"后台发送按键成功: 键码 {key_code}")
            
            # 延迟
            if delay > 0:
                time.sleep(delay)
                
            return True
        except Exception as e:
            self.logger.error(f"后台发送按键操作失败: {str(e)}")
            return False
            
    def is_window_minimized(self) -> bool:
        """判断窗口是否最小化
        
        Returns:
            bool: 是否最小化
        """
        if not self.hwnd:
            return False
        
        return win32gui.IsIconic(self.hwnd)

    def scroll_background(self, x: int, y: int, direction: int, scroll_count: int = 1) -> bool:
        """在后台执行鼠标滚轮滚动

        Args:
            x: 滚动位置X坐标（客户区坐标）
            y: 滚动位置Y坐标（客户区坐标）
            direction: 滚动方向，正值向上，负值向下
            scroll_count: 滚动次数

        Returns:
            bool: 滚动是否成功
        """
        if not self.hwnd:
            self.logger.error("未设置窗口句柄，无法执行后台滚动")
            return False

        try:
            # 更新窗口信息
            self.update_window_info()

            # 构造坐标参数
            lParam = win32api.MAKELONG(x, y)

            # 执行滚动
            for _ in range(scroll_count):
                # direction > 0 向上滚动，direction < 0 向下滚动
                # Windows滚轮消息：正值向上，负值向下
                wheel_delta = 120 if direction > 0 else -120

                # 发送滚轮消息
                # wParam的高16位是滚轮增量，低16位是按键状态
                wParam = (wheel_delta << 16) | 0  # 没有按键状态

                win32gui.PostMessage(self.hwnd, win32con.WM_MOUSEWHEEL, wParam, lParam)
                time.sleep(0.05)  # 滚动间隔

            self.logger.debug(f"后台滚动成功: 位置[{x},{y}], 方向[{direction}], 次数[{scroll_count}]")
            return True

        except Exception as e:
            self.logger.error(f"后台滚动操作失败: {str(e)}")
            return False
    
    def is_window_foreground(self) -> bool:
        """判断窗口是否在前台
        
        Returns:
            bool: 是否在前台
        """
        if not self.hwnd:
            return False
        
        return self.hwnd == win32gui.GetForegroundWindow() 