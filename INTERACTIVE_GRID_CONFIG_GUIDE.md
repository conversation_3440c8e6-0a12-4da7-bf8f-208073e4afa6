# 🖱️ 交互式网格配置指南

## 🎯 **功能概述**

全新的交互式网格配置工具，支持鼠标拖拽和缩放操作，让网格配置变得直观和高效！

### ✨ **主要特性**
- 🖱️ **鼠标拖拽**: 直接拖拽蓝色手柄调整网格区域
- 🔄 **滚轮缩放**: 使用鼠标滚轮缩放结界项大小
- 🎯 **实时预览**: 所有调整立即显示效果
- 🎨 **可视化操作**: 告别复杂的数值输入
- 📐 **精确控制**: 支持滑块微调间距参数

## 🚀 **启动方法**

### **方法一：从主界面启动**
1. 运行 `python main_gui_v2.py`
2. 切换到"🏰 结界突破"标签页
3. 点击"🖱️ 拖拽配置"按钮

### **方法二：直接启动**
```bash
python features/realm_raid/interactive_grid_config.py
```

### **方法三：测试启动**
```bash
python test_interactive_config.py
```

## 🎮 **操作指南**

### **🖱️ 鼠标操作**

#### **拖拽网格区域**
- **蓝色圆形手柄**: 拖拽调整网格边界
  - 左手柄: 调整左边界
  - 右手柄: 调整右边界  
  - 上手柄: 调整上边界
  - 下手柄: 调整下边界

#### **调整结界项尺寸**
- **红色结界项**: 直接拖拽调整尺寸
  - 水平拖拽: 调整宽度
  - 垂直拖拽: 调整高度

#### **缩放操作**
- **鼠标滚轮**: 在预览区域滚动
  - 向上滚动: 放大结界项
  - 向下滚动: 缩小结界项

### **🎛️ 控制面板操作**

#### **网格布局**
- **行数**: 设置网格行数 (1-5)
- **列数**: 设置网格列数 (1-5)

#### **间距调整**
- **水平间距滑块**: 调整结界项之间的水平间距
- **垂直间距滑块**: 调整结界项之间的垂直间距

#### **快速预设**
- **标准 3×3**: 默认推荐配置
- **紧凑 3×3**: 适合小屏幕
- **宽松 3×3**: 适合大屏幕

## 🎨 **界面说明**

### **预览区域**
```
┌─────────────────────────────────────┐
│  🔵 ← 拖拽手柄                      │
│  ┌─────────────────────────────┐    │
│  │ 🔴 🔴 🔴  ← 结界项          │    │
│  │ 0  1  2                     │    │
│  │                             │    │
│  │ 🔴 🔴 🔴                    │    │
│  │ 3  4  5                     │    │
│  │                             │    │
│  │ 🔴 🔴 🔴                    │    │
│  │ 6  7  8                     │    │
│  └─────────────────────────────┘    │
│                                 🔵  │
└─────────────────────────────────────┘
```

### **状态信息**
- **网格区域**: 显示当前边界坐标
- **结界尺寸**: 显示当前结界项尺寸
- **缩放倍数**: 显示当前缩放级别

## 💡 **使用技巧**

### **精确调整方法**
1. **粗调**: 使用鼠标拖拽快速调整大致位置
2. **细调**: 使用滑块进行精确的间距调整
3. **缩放**: 使用滚轮调整结界项大小
4. **预设**: 使用快速预设作为起点

### **最佳实践**
- 🎯 **先调整网格区域**: 确保覆盖整个结界列表
- 📏 **再调整结界尺寸**: 确保每个结界项精确覆盖
- 🔄 **最后微调间距**: 避免结界项重叠
- ✅ **保存前测试**: 确认配置效果满意

### **常见配置场景**
- **1920×1080**: 使用标准预设
- **1366×768**: 使用紧凑预设
- **2560×1440**: 使用宽松预设
- **自定义分辨率**: 手动调整网格区域

## 🔧 **故障排除**

### **❌ 界面打不开**
**解决方案**:
```bash
pip install PyQt5
python test_interactive_config.py
```

### **❌ 拖拽不响应**
**解决方案**:
- 确保鼠标在手柄附近
- 尝试重新打开界面
- 检查鼠标驱动

### **❌ 滚轮缩放无效**
**解决方案**:
- 确保鼠标在预览区域内
- 检查鼠标滚轮设置
- 尝试使用触摸板滚动

### **❌ 配置保存失败**
**解决方案**:
- 检查文件写入权限
- 确保磁盘空间充足
- 手动创建配置文件

## 🎊 **总结**

### ✅ **交互式配置优势**
- **🚀 直观操作**: 所见即所得的配置体验
- **⚡ 快速调整**: 鼠标操作比数值输入更快
- **🎯 精确控制**: 支持像素级精确调整
- **👁️ 实时反馈**: 立即看到调整效果
- **🎨 用户友好**: 降低配置门槛

### 🚀 **开始使用**
现在您可以通过直观的鼠标操作，轻松配置完美的3×3网格了！

**点击"🖱️ 拖拽配置"按钮，体验全新的配置方式！** 🎯

## 📞 **获取帮助**

如果遇到问题，请：
1. 查看控制台错误信息
2. 尝试重新启动程序
3. 使用测试脚本验证功能
4. 检查PyQt5安装状态
