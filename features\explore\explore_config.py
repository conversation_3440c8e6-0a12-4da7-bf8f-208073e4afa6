"""
探索功能配置模块

存储探索功能的默认配置和设置
"""

# 默认模板路径
DEFAULT_TEMPLATES = {
    'tansuo': 'templates/explore/tansuo.png',         # 探索图标
    'explore_icon': 'templates/explore/explore_icon.png', # 进入副本
    'yao': 'templates/explore/yao.png',               # 探索界面元素
    # 'chapter_btn': 'templates/explore/28.png',      # 章节选择 - 改为动态配置
    'challenge_btn': 'templates/explore/challenge_btn.png', # 挑战按钮
    'victory': 'templates/explore/victory.png',       # 胜利画面
    'reward': 'templates/explore/reward.png',         # 奖励画面
    'boss_btn': 'templates/explore/boss_btn.png',     # 首领按钮
    'treasure_chest': 'templates/explore/treasure_chest.png', # 宝箱图标
    'get_reward': 'templates/explore/get_reward.png', # 获取奖励按钮
    'ready_btn': 'templates/explore/ready_btn.png',   # 准备按钮
}

# 章节模板映射（用于回退）
CHAPTER_TEMPLATES = {
    28: 'templates/explore/28.png',  # 28章模板
    # 可以在这里添加更多章节的图像模板
}

# 默认参数
DEFAULT_SETTINGS = {
    # 探索设置
    'auto_restart': True,
    'auto_use_ap': True,
    'move_forward': True,
    'no_monster_threshold': 30,
    
    # 章节设置
    'target_chapter': 28,           # 目标章节号
    'use_ocr_first': True,          # 优先使用OCR识别章节
    'fallback_to_template': True,   # OCR失败时回退到图像模板
    
    # 延迟设置
    'min_delay': 0.8,
    'max_delay': 1.5,
    
    # 休息设置
    'use_breaks': True,
    'break_interval': 1800,  # 30分钟
    'break_duration': 3,  # 3分钟
    
    # 操作设置
    'template_match_threshold': 0.8,
    'use_random_click': True,
    'click_radius': 5,
    'use_human_like_move': True,
}

# 状态描述
STATE_DESCRIPTIONS = {
    'idle': '等待开始',
    'chapter_menu': '章节选择',
    'zjyz': '选择章节',
    'qrzj': '确认进入',
    'explore_menu': '探索副本',
    'exploring': '探索中',
}

# 统计项描述
STATS_DESCRIPTIONS = {
    'battles': '战斗次数',
    'monsters': '怪物数量',
    'treasures': '宝箱数量',
    'bosses': 'Boss数量',
    'restarts': '重启次数',
    'victories': '胜利次数',
    'rewards': '奖励次数',
}