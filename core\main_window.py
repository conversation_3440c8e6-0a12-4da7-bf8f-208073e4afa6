"""主窗口 - 集成通用功能"""

import sys
import logging
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                            QTabWidget, QSplitter, QGroupBox, QLabel, 
                            QPushButton, QComboBox, QCheckBox, QSpinBox,
                            QTextEdit, QMenuBar, QMenu, QAction, QStatusBar,
                            QMessageBox, QFileDialog)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QIcon, QFont

from core.gui_common import WindowFinderWidget, LogViewWidget, StatsWidget
from core.logging_config import setup_logging
from core.config_manager import config_manager
from PyQt5.QtGui import QPainter, QFontMetrics
from PyQt5.QtCore import QRect


class LeftSideTabWidget(QWidget):
    """左侧标签页组件，文字水平显示"""

    currentChanged = pyqtSignal(int)  # 标签页切换信号

    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_index = 0
        self.tabs = []
        self.tab_widgets = []
        self.setup_ui()

    def setup_ui(self):
        """设置界面"""
        layout = QHBoxLayout()
        layout.setContentsMargins(5, 5, 5, 5)  # 减小边距
        layout.setSpacing(0)
        self.setLayout(layout)

        # 左侧按钮区域
        self.button_widget = QWidget()
        self.button_widget.setFixedWidth(180)  # 增加宽度确保文字完整显示
        self.button_widget.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                border-right: 1px solid #dee2e6;
            }
        """)

        self.button_layout = QVBoxLayout()
        self.button_layout.setContentsMargins(5, 5, 5, 5)  # 减小边距
        self.button_layout.setSpacing(1)  # 减小按钮间距
        self.button_widget.setLayout(self.button_layout)

        # 右侧内容区域
        self.content_widget = QWidget()
        self.content_widget.setStyleSheet("""
            QWidget {
                background-color: white;
                border: 1px solid #dee2e6;
            }
        """)

        self.content_layout = QVBoxLayout()
        self.content_layout.setContentsMargins(10, 10, 10, 10)  # 适中的边距
        self.content_layout.setSpacing(5)  # 减小组件间距
        self.content_widget.setLayout(self.content_layout)

        layout.addWidget(self.button_widget)
        layout.addWidget(self.content_widget)

    def addTab(self, widget, text):
        """添加标签页"""
        # 获取当前索引
        current_index = len(self.tabs)

        # 创建按钮
        button = QPushButton(text)
        button.setFixedHeight(40)  # 减小高度从50到40
        button.setStyleSheet("""
            QPushButton {
                background-color: #f8f9fa;
                border: none;
                border-bottom: 1px solid #dee2e6;
                text-align: center;
                padding: 8px 10px;
                font-size: 13px;
                font-weight: bold;
                color: #495057;
            }
            QPushButton:hover {
                background-color: #e9ecef;
                color: #2c3e50;
            }
            QPushButton:checked {
                background-color: #ffffff;
                color: #2c3e50;
                border-right: 3px solid #3498db;
                font-weight: bold;
            }
        """)
        button.setCheckable(True)

        # 修复信号连接 - 使用正确的索引
        button.clicked.connect(lambda checked, idx=current_index: self.set_current_index(idx))

        # 添加到列表
        self.tabs.append(text)
        self.tab_widgets.append(widget)
        self.button_layout.addWidget(button)

        # 如果是第一个标签页，设为选中
        if len(self.tabs) == 1:
            button.setChecked(True)
            self.show_widget(widget)

        return current_index

    def set_current_index(self, index):
        """设置当前标签页"""
        logging.info(f"🔄 切换标签页到索引: {index}, 标签页总数: {len(self.tabs)}")

        if 0 <= index < len(self.tabs):
            # 更新按钮状态
            for i in range(self.button_layout.count()):
                button = self.button_layout.itemAt(i).widget()
                if isinstance(button, QPushButton):
                    button.setChecked(i == index)
                    logging.debug(f"按钮 {i} 设置为 {'选中' if i == index else '未选中'}")

            # 显示对应的widget
            if index < len(self.tab_widgets):
                widget = self.tab_widgets[index]
                logging.info(f"显示标签页内容: {self.tabs[index]}, widget: {type(widget).__name__}")
                self.show_widget(widget)

            # 发送信号
            if self.current_index != index:
                old_index = self.current_index
                self.current_index = index
                logging.info(f"发送标签页切换信号: {old_index} -> {index}")
                self.currentChanged.emit(index)
        else:
            logging.error(f"❌ 无效的标签页索引: {index}, 有效范围: 0-{len(self.tabs)-1}")

    def show_widget(self, widget):
        """显示指定的widget"""
        logging.debug(f"开始显示widget: {type(widget).__name__ if widget else 'None'}")

        # 清空当前内容
        removed_count = 0
        for i in reversed(range(self.content_layout.count())):
            child = self.content_layout.takeAt(i)
            if child.widget():
                child.widget().setParent(None)
                removed_count += 1

        logging.debug(f"清空了 {removed_count} 个旧widget")

        # 添加新widget
        if widget:
            self.content_layout.addWidget(widget)
            logging.debug(f"✅ 已添加新widget: {type(widget).__name__}")
        else:
            logging.warning("⚠️ 尝试显示空widget")

    def tabText(self, index):
        """获取标签页文本"""
        if 0 <= index < len(self.tabs):
            return self.tabs[index]
        return ""

    def currentIndex(self):
        """获取当前标签页索引"""
        return self.current_index

class GlobalSettingsWidget(QWidget):
    """全局设置面板"""
    
    settings_changed = pyqtSignal(dict)  # 设置变更信号
    
    def __init__(self):
        super().__init__()
        self._initializing = True  # 标记正在初始化
        self.setup_ui()
        self._initializing = False  # 初始化完成
        self.load_settings()
    
    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout()
        layout.setContentsMargins(5, 5, 5, 5)  # 减小边距
        layout.setSpacing(8)  # 减小组件间距
        self.setLayout(layout)
        
        # 操作模式设置
        mode_group = QGroupBox("操作模式")
        mode_layout = QVBoxLayout()
        mode_layout.setContentsMargins(8, 8, 8, 8)  # 减小边距
        mode_layout.setSpacing(4)  # 减小间距
        mode_group.setLayout(mode_layout)

        # 操作模式选择（单选按钮组）
        from PyQt5.QtWidgets import QRadioButton, QButtonGroup

        self.operation_mode_group = QButtonGroup()

        # 前台模式选项
        self.foreground_mode_radio = QRadioButton("前台模式运行")
        self.foreground_mode_radio.setToolTip(
            "前台模式特点：\n"
            "• 可以看到鼠标移动和点击过程\n"
            "• 兼容性好，几乎所有应用都支持\n"
            "• 操作轨迹真实，更像人类操作\n"
            "• 调试方便，可以直观看到操作过程\n"
            "• 需要窗口在前台，会占用屏幕空间"
        )
        self.operation_mode_group.addButton(self.foreground_mode_radio, 0)
        mode_layout.addWidget(self.foreground_mode_radio)

        # 后台模式选项
        self.background_mode_radio = QRadioButton("后台模式运行")
        self.background_mode_radio.setToolTip(
            "后台模式特点：\n"
            "• 窗口可以最小化，不占用屏幕空间\n"
            "• 不干扰用户其他操作\n"
            "• 不控制鼠标，用户可以正常使用电脑\n"
            "• 更隐蔽，不容易被发现\n"
            "• 某些应用可能不响应后台消息"
        )
        self.operation_mode_group.addButton(self.background_mode_radio, 1)
        mode_layout.addWidget(self.background_mode_radio)

        # 默认选择后台模式
        self.background_mode_radio.setChecked(True)

        # 模式说明
        mode_info_layout = QHBoxLayout()
        mode_info_label = QLabel("💡 建议：调试时使用前台模式，正式运行时使用后台模式")
        mode_info_label.setStyleSheet("""
            color: #666;
            font-size: 11px;
            padding: 4px;
            word-wrap: break-word;
        """)
        mode_info_label.setWordWrap(True)  # 启用自动换行
        mode_info_layout.addWidget(mode_info_label)
        mode_layout.addLayout(mode_info_layout)

        # 其他设置
        self.minimize_on_start_check = QCheckBox("启动时最小化到托盘")
        self.minimize_on_start_check.toggled.connect(self.on_settings_changed)
        mode_layout.addWidget(self.minimize_on_start_check)
        
        layout.addWidget(mode_group)
        
        # 日志设置
        log_group = QGroupBox("日志设置")
        log_layout = QVBoxLayout()
        log_layout.setContentsMargins(8, 8, 8, 8)
        log_layout.setSpacing(4)
        log_group.setLayout(log_layout)
        
        # 日志级别
        log_level_layout = QHBoxLayout()
        log_level_layout.addWidget(QLabel("日志级别:"))
        self.log_level_combo = QComboBox()
        # 设置下拉框样式
        self.log_level_combo.setStyleSheet("""
            QComboBox {
                font-size: 13px;
                padding: 4px 8px;
                border: 1px solid #ccc;
                border-radius: 3px;
                background-color: white;
                min-width: 80px;
            }
            QComboBox QAbstractItemView {
                font-size: 13px;
                selection-background-color: #e3f2fd;
            }
        """)
        self.log_level_combo.addItems(["DEBUG", "INFO", "WARNING", "ERROR"])
        self.log_level_combo.setCurrentText("INFO")
        self.log_level_combo.currentTextChanged.connect(self.on_settings_changed)
        log_level_layout.addWidget(self.log_level_combo)
        log_level_layout.addStretch()
        log_layout.addLayout(log_level_layout)
        
        # 文件日志
        self.file_log_check = QCheckBox("启用文件日志")
        self.file_log_check.setChecked(True)
        self.file_log_check.toggled.connect(self.on_settings_changed)
        log_layout.addWidget(self.file_log_check)
        
        # 自动清理日志
        cleanup_layout = QHBoxLayout()
        self.auto_cleanup_check = QCheckBox("自动清理日志")
        self.auto_cleanup_check.toggled.connect(self.on_settings_changed)
        cleanup_layout.addWidget(self.auto_cleanup_check)

        cleanup_layout.addWidget(QLabel("(保留"))

        self.cleanup_days_spin = QSpinBox()
        self.cleanup_days_spin.setRange(1, 365)
        self.cleanup_days_spin.setValue(30)
        self.cleanup_days_spin.setSuffix(" 天)")
        self.cleanup_days_spin.valueChanged.connect(self.on_settings_changed)
        cleanup_layout.addWidget(self.cleanup_days_spin)
        cleanup_layout.addStretch()
        log_layout.addLayout(cleanup_layout)
        
        layout.addWidget(log_group)
        
        # 性能设置
        perf_group = QGroupBox("性能设置")
        perf_layout = QVBoxLayout()
        perf_group.setLayout(perf_layout)
        
        # 截图质量
        quality_layout = QHBoxLayout()
        quality_layout.addWidget(QLabel("截图质量:"))
        self.screenshot_quality_combo = QComboBox()
        # 设置下拉框样式
        self.screenshot_quality_combo.setStyleSheet("""
            QComboBox {
                font-size: 13px;
                padding: 4px 8px;
                border: 1px solid #ccc;
                border-radius: 3px;
                background-color: white;
                min-width: 80px;
            }
            QComboBox QAbstractItemView {
                font-size: 13px;
                selection-background-color: #e3f2fd;
            }
        """)
        self.screenshot_quality_combo.addItems(["高质量", "标准", "快速"])
        self.screenshot_quality_combo.setCurrentText("标准")
        self.screenshot_quality_combo.currentTextChanged.connect(self.on_settings_changed)
        quality_layout.addWidget(self.screenshot_quality_combo)
        quality_layout.addStretch()
        perf_layout.addLayout(quality_layout)
        
        # OCR设置
        self.gpu_ocr_check = QCheckBox("使用GPU加速OCR (如果可用)")
        self.gpu_ocr_check.toggled.connect(self.on_settings_changed)
        perf_layout.addWidget(self.gpu_ocr_check)
        
        layout.addWidget(perf_group)
        
        # 安全设置
        safety_group = QGroupBox("安全设置")
        safety_layout = QVBoxLayout()
        safety_group.setLayout(safety_layout)
        
        # 全局延迟
        delay_layout = QHBoxLayout()
        delay_layout.addWidget(QLabel("全局操作延迟:"))
        self.global_delay_spin = QSpinBox()
        self.global_delay_spin.setRange(100, 5000)
        self.global_delay_spin.setValue(1000)
        self.global_delay_spin.setSuffix(" 毫秒")
        self.global_delay_spin.valueChanged.connect(self.on_settings_changed)
        delay_layout.addWidget(self.global_delay_spin)
        delay_layout.addStretch()
        safety_layout.addLayout(delay_layout)
        
        # 错误处理
        self.auto_recovery_check = QCheckBox("自动错误恢复")
        self.auto_recovery_check.setChecked(True)
        self.auto_recovery_check.toggled.connect(self.on_settings_changed)
        safety_layout.addWidget(self.auto_recovery_check)
        
        layout.addWidget(safety_group)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.save_button = QPushButton("保存设置")
        self.save_button.clicked.connect(self.save_settings)
        button_layout.addWidget(self.save_button)
        
        self.reset_button = QPushButton("重置默认")
        self.reset_button.clicked.connect(self.reset_settings)
        button_layout.addWidget(self.reset_button)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)

        layout.addStretch()

        # 在UI完全设置完成后连接信号
        self.foreground_mode_radio.toggled.connect(self.on_operation_mode_changed)
        self.background_mode_radio.toggled.connect(self.on_operation_mode_changed)
    
    def on_operation_mode_changed(self):
        """操作模式变更回调"""
        # 如果正在初始化，跳过处理
        if getattr(self, '_initializing', False):
            return

        # 获取当前选择的模式
        if self.foreground_mode_radio.isChecked():
            operation_mode = "foreground"
        else:
            operation_mode = "background"

        # 保存到配置
        from core.config_manager import config_manager
        config_manager.set_and_save("operation_mode", operation_mode)

        # 发送设置变更信号（只有在初始化完成后）
        if hasattr(self, 'minimize_on_start_check'):
            self.on_settings_changed()

        # 记录日志
        mode_text = "前台" if operation_mode == "foreground" else "后台"
        logging.info(f"🔄 操作模式已切换为: {mode_text}模式")

    def on_settings_changed(self):
        """设置变更回调"""
        settings = self.get_current_settings()
        self.settings_changed.emit(settings)
    
    def get_current_settings(self):
        """获取当前设置"""
        # 获取操作模式
        operation_mode = "foreground" if self.foreground_mode_radio.isChecked() else "background"

        return {
            'operation_mode': operation_mode,
            'background_mode': operation_mode == "background",  # 保持向后兼容
            'minimize_on_start': self.minimize_on_start_check.isChecked(),
            'log_level': self.log_level_combo.currentText(),
            'file_log_enabled': self.file_log_check.isChecked(),
            'auto_cleanup_logs': self.auto_cleanup_check.isChecked(),
            'cleanup_days': self.cleanup_days_spin.value(),
            'screenshot_quality': self.screenshot_quality_combo.currentText(),
            'gpu_ocr': self.gpu_ocr_check.isChecked(),
            'global_delay': self.global_delay_spin.value(),
            'auto_recovery': self.auto_recovery_check.isChecked()
        }
    
    def load_settings(self):
        """加载设置"""
        try:
            settings = config_manager.get("global_settings", {})

            # 加载操作模式设置
            operation_mode = config_manager.get("operation_mode", "background")
            if operation_mode == "foreground":
                self.foreground_mode_radio.setChecked(True)
            else:
                self.background_mode_radio.setChecked(True)

            self.minimize_on_start_check.setChecked(settings.get('minimize_on_start', False))
            self.log_level_combo.setCurrentText(settings.get('log_level', 'INFO'))
            self.file_log_check.setChecked(settings.get('file_log_enabled', True))
            self.auto_cleanup_check.setChecked(settings.get('auto_cleanup_logs', False))
            self.cleanup_days_spin.setValue(settings.get('cleanup_days', 30))
            self.screenshot_quality_combo.setCurrentText(settings.get('screenshot_quality', '标准'))
            self.gpu_ocr_check.setChecked(settings.get('gpu_ocr', False))
            self.global_delay_spin.setValue(settings.get('global_delay', 1000))
            self.auto_recovery_check.setChecked(settings.get('auto_recovery', True))

        except Exception as e:
            logging.error(f"加载设置失败: {e}")
    
    def save_settings(self):
        """保存设置"""
        try:
            settings = self.get_current_settings()
            config_manager.set("global_settings", settings)
            config_manager.save_config()

            QMessageBox.information(self, "成功", "设置已保存")
            logging.info("全局设置已保存")
            
        except Exception as e:
            error_msg = f"保存设置失败: {e}"
            logging.error(error_msg)
            QMessageBox.critical(self, "错误", error_msg)
    
    def reset_settings(self):
        """重置设置"""
        reply = QMessageBox.question(self, "确认", "确定要重置所有设置为默认值吗？")
        if reply == QMessageBox.Yes:
            # 重置操作模式为后台模式
            self.background_mode_radio.setChecked(True)
            self.minimize_on_start_check.setChecked(False)
            self.log_level_combo.setCurrentText('INFO')
            self.file_log_check.setChecked(True)
            self.auto_cleanup_check.setChecked(False)
            self.cleanup_days_spin.setValue(30)
            self.screenshot_quality_combo.setCurrentText('标准')
            self.gpu_ocr_check.setChecked(False)
            self.global_delay_spin.setValue(1000)
            self.auto_recovery_check.setChecked(True)

            logging.info("设置已重置为默认值")


class MainWindow(QMainWindow):
    """主窗口类 - 集成通用功能"""
    
    # 全局信号
    window_selected = pyqtSignal(int, str)  # 窗口选择信号
    settings_changed = pyqtSignal(dict)     # 设置变更信号
    
    def __init__(self):
        super().__init__()
        self.current_hwnd = None
        self.current_title = None
        self.setup_logging()
        self.setup_ui()
        self.setup_connections()
        
        logging.info("🚀 主窗口初始化完成")
    
    def setup_logging(self):
        """设置日志系统"""
        setup_logging(
            log_level=logging.INFO,
            enable_file_log=True,
            log_dir="logs"
        )
    
    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("阴阳师自动化工具 v2.0")
        self.setGeometry(100, 100, 1200, 750)  # 减小默认窗口大小

        # 设置菜单栏
        self.setup_menu_bar()

        # 设置状态栏
        self.setup_status_bar()

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局 - 水平分割
        main_splitter = QSplitter(Qt.Horizontal)
        central_layout = QHBoxLayout()
        central_layout.setContentsMargins(5, 5, 5, 5)  # 减小窗口边距
        central_layout.setSpacing(0)
        central_layout.addWidget(main_splitter)
        central_widget.setLayout(central_layout)

        # 左侧功能标签页（包含内容区域）
        self.setup_left_tabs(main_splitter)
    
    def setup_menu_bar(self):
        """设置菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu('文件')
        
        # 导入配置
        import_action = QAction('导入配置', self)
        import_action.triggered.connect(self.import_config)
        file_menu.addAction(import_action)
        
        # 导出配置
        export_action = QAction('导出配置', self)
        export_action.triggered.connect(self.export_config)
        file_menu.addAction(export_action)
        
        file_menu.addSeparator()
        
        # 退出
        exit_action = QAction('退出', self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 工具菜单
        tools_menu = menubar.addMenu('工具')
        
        # 日志查看器
        log_viewer_action = QAction('日志查看器', self)
        log_viewer_action.triggered.connect(self.open_log_viewer)
        tools_menu.addAction(log_viewer_action)
        
        # 清理日志
        cleanup_logs_action = QAction('清理日志', self)
        cleanup_logs_action.triggered.connect(self.cleanup_logs)
        tools_menu.addAction(cleanup_logs_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu('帮助')
        
        # 关于
        about_action = QAction('关于', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_status_bar(self):
        """设置状态栏"""
        self.status_bar = self.statusBar()
        
        # 当前窗口状态
        self.window_status_label = QLabel("未选择窗口")
        self.status_bar.addWidget(self.window_status_label)
        
        # 运行状态
        self.running_status_label = QLabel("就绪")
        self.status_bar.addPermanentWidget(self.running_status_label)
    
    def setup_left_tabs(self, parent_splitter):
        """设置左侧功能标签页"""
        # 创建左侧标签页容器 - 使用自定义类
        self.left_tabs = LeftSideTabWidget()
        # 让组件占用所有可用空间，不设置固定限制





        # 通用设置标签页
        self.setup_general_tab()

        # 探索副本标签页
        self.setup_explore_tab()

        # 结界突破标签页
        self.setup_realm_raid_tab()

        # 日志查看标签页
        self.setup_log_tab()

        # 连接标签页切换信号
        self.left_tabs.currentChanged.connect(self.on_tab_changed)

        parent_splitter.addWidget(self.left_tabs)

    def setup_general_tab(self):
        """设置通用设置标签页"""
        general_widget = QWidget()
        general_widget.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)  # 减小边距
        layout.setSpacing(8)  # 减小组件间距
        general_widget.setLayout(layout)

        # 添加标题
        title_label = QLabel("🔧 通用设置")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                padding: 8px;
                background-color: #e9ecef;
                border-radius: 4px;
                margin-bottom: 5px;
            }
        """)
        layout.addWidget(title_label)

        # 窗口选择器
        window_group = QGroupBox("游戏窗口")
        window_layout = QVBoxLayout()
        window_group.setLayout(window_layout)

        self.window_finder = WindowFinderWidget()
        window_layout.addWidget(self.window_finder)
        layout.addWidget(window_group)

        # 全局设置
        settings_group = QGroupBox("全局设置")
        settings_layout = QVBoxLayout()
        settings_group.setLayout(settings_layout)

        self.global_settings = GlobalSettingsWidget()
        settings_layout.addWidget(self.global_settings)
        layout.addWidget(settings_group)

        self.left_tabs.addTab(general_widget, "🔧 通用设置")

    def setup_explore_tab(self):
        """设置探索副本标签页"""
        from features.explore.explore_gui_simple import ExploreGUI

        # 创建容器widget
        explore_container = QWidget()
        explore_container.setStyleSheet("""
            QWidget {
                background-color: #e8f5e8;
            }
        """)
        layout = QVBoxLayout()
        explore_container.setLayout(layout)

        # 添加标题
        title_label = QLabel("🗺️ 探索副本")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #2e7d32;
                padding: 10px;
                background-color: #c8e6c9;
                border-radius: 6px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # 添加探索GUI
        self.explore_tab = ExploreGUI()
        layout.addWidget(self.explore_tab)

        self.left_tabs.addTab(explore_container, "🗺️ 探索副本")

    def setup_realm_raid_tab(self):
        """设置结界突破标签页"""
        from features.realm_raid.realm_raid_gui_simple import RealmRaidGUI
        self.realm_raid_tab = RealmRaidGUI()
        self.left_tabs.addTab(self.realm_raid_tab, "🏰 结界突破")

    def setup_log_tab(self):
        """设置日志查看标签页"""
        log_widget = QWidget()
        layout = QVBoxLayout()
        log_widget.setLayout(layout)

        # 日志显示
        log_group = QGroupBox("运行日志")
        log_layout = QVBoxLayout()
        log_group.setLayout(log_layout)

        self.log_widget = LogViewWidget()
        log_layout.addWidget(self.log_widget)
        layout.addWidget(log_group)

        self.left_tabs.addTab(log_widget, "📝 运行日志")

    def on_tab_changed(self, index):
        """标签页切换回调"""
        tab_text = self.left_tabs.tabText(index)
        logging.info(f"🔄 切换到标签页: {tab_text}")

        # 更新状态栏
        self.running_status_label.setText(f"当前页面: {tab_text}")





    def setup_connections(self):
        """设置信号连接"""
        # 窗口选择信号
        self.window_finder.window_selected.connect(self.on_window_selected)

        # 设置变更信号
        self.global_settings.settings_changed.connect(self.on_settings_changed)
    
    def on_window_selected(self, hwnd, title):
        """窗口选择回调"""
        self.current_hwnd = hwnd
        self.current_title = title
        
        # 更新状态栏
        self.window_status_label.setText(f"已选择: {title} ({hwnd})")
        
        # 发送全局信号
        self.window_selected.emit(hwnd, title)
        
        logging.info(f"🪟 全局窗口选择: {title} (句柄: {hwnd})")
    
    def on_settings_changed(self, settings):
        """设置变更回调"""
        # 发送全局信号
        self.settings_changed.emit(settings)
        
        # 应用日志级别变更
        if 'log_level' in settings:
            level_map = {
                'DEBUG': logging.DEBUG,
                'INFO': logging.INFO,
                'WARNING': logging.WARNING,
                'ERROR': logging.ERROR
            }
            new_level = level_map.get(settings['log_level'], logging.INFO)
            logging.getLogger().setLevel(new_level)
            logging.info(f"日志级别已更改为: {settings['log_level']}")
    
    def get_current_hwnd(self):
        """获取当前选中的窗口句柄"""
        return self.current_hwnd
    
    def get_current_title(self):
        """获取当前选中的窗口标题"""
        return self.current_title
    
    def import_config(self):
        """导入配置"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "导入配置文件", "", "JSON文件 (*.json)"
        )
        if file_path:
            try:
                config_manager.load_from_file(file_path)
                QMessageBox.information(self, "成功", "配置导入成功")
                logging.info(f"配置已从 {file_path} 导入")
            except Exception as e:
                error_msg = f"导入配置失败: {e}"
                logging.error(error_msg)
                QMessageBox.critical(self, "错误", error_msg)
    
    def export_config(self):
        """导出配置"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出配置文件", "config_backup.json", "JSON文件 (*.json)"
        )
        if file_path:
            try:
                config_manager.save_to_file(file_path)
                QMessageBox.information(self, "成功", "配置导出成功")
                logging.info(f"配置已导出到 {file_path}")
            except Exception as e:
                error_msg = f"导出配置失败: {e}"
                logging.error(error_msg)
                QMessageBox.critical(self, "错误", error_msg)
    
    def open_log_viewer(self):
        """打开日志查看器"""
        try:
            import subprocess
            subprocess.Popen([sys.executable, "tools/log_viewer.py"])
            logging.info("日志查看器已启动")
        except Exception as e:
            error_msg = f"启动日志查看器失败: {e}"
            logging.error(error_msg)
            QMessageBox.critical(self, "错误", error_msg)
    
    def cleanup_logs(self):
        """清理日志"""
        reply = QMessageBox.question(self, "确认", "确定要清理旧日志文件吗？")
        if reply == QMessageBox.Yes:
            try:
                # 这里可以添加日志清理逻辑
                QMessageBox.information(self, "成功", "日志清理完成")
                logging.info("日志文件已清理")
            except Exception as e:
                error_msg = f"清理日志失败: {e}"
                logging.error(error_msg)
                QMessageBox.critical(self, "错误", error_msg)
    
    def show_about(self):
        """显示关于对话框"""
        about_text = """
        阴阳师自动化工具 v2.0
        
        功能特性：
        • 探索副本自动化
        • 结界突破自动化
        • 智能窗口管理
        • 完整日志系统
        • 灵活配置管理
        
        技术支持：
        • Python 3.7+
        • PyQt5
        • OpenCV
        • EasyOCR
        
        使用说明：
        1. 选择游戏窗口
        2. 配置相关参数
        3. 启动对应功能
        4. 查看运行日志
        """
        QMessageBox.about(self, "关于", about_text)
