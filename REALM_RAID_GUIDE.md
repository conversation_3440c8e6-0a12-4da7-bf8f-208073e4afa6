# 结界突破功能使用指南

## 📋 功能概述

结界突破功能是一个自动化的阴阳师结界突破工具，支持智能选择目标结界、自动战斗和结果处理。

## 🎯 核心特性

### 🗺️ 3x3网格定位系统
- **精确定位**：将结界列表划分为3x3网格，每个位置都有固定坐标
- **自适应分辨率**：支持不同分辨率的自动适配
- **状态检测**：自动识别结界的可用性和冷却状态

### 🎲 智能选择策略
- **随机选择**：完全随机，最安全
- **优先级选择**：从左上角开始，效率最高
- **中心向外**：从中心位置向外扩散选择
- **角落优先**：优先选择四个角落位置
- **避免中心**：避免选择中心位置
- **智能网格**：综合策略，推荐使用

### 🎫 突破券检测
- **实时监控**：自动检测突破券数量
- **智能等待**：券不足时自动等待恢复
- **OCR识别**：通过图像识别获取券数量

## ⚙️ 配置说明

### 基本设置
- **最大突破次数**：单次运行的最大突破次数（1-50）
- **战斗超时时间**：单次战斗的最大等待时间（60-300秒）
- **自动战斗**：是否启用自动战斗功能
- **失败重试**：战斗失败后是否重试

### 选择策略
- **策略类型**：选择结界的策略算法
- **随机化顺序**：是否随机化选择顺序
- **跳过冷却**：是否跳过冷却中的结界

### 安全设置
- **随机延迟**：操作间的随机延迟时间（1-20秒）
- **休息间隔**：定期休息的间隔时间（10-120分钟）

## 🚀 使用步骤

### 1. 启动程序
```bash
# 运行主程序
python main_gui.py

# 或者单独测试结界突破
python test_realm_raid.py
```

### 2. 选择游戏窗口
- 点击"刷新窗口列表"
- 从下拉框中选择阴阳师游戏窗口
- 点击"测试连接"验证连接

### 3. 配置参数
- 根据需要调整基本设置
- 选择合适的选择策略
- 设置安全参数

### 4. 开始突破
- 确保游戏已进入主界面
- 点击"开始突破"按钮
- 观察日志输出和统计信息

### 5. 停止功能
- 点击"停止突破"按钮
- 等待当前操作完成后停止

## 📁 文件结构

```
features/realm_raid/
├── __init__.py                 # 模块初始化
├── realm_raid_bot.py          # 核心机器人类
├── realm_raid_config.py       # 配置文件
└── realm_raid_gui.py          # GUI界面

templates/realm_raid/           # 模板图像文件夹
├── guild_button.png           # 阴阳寮按钮
├── realm_raid_button.png      # 结界突破按钮
├── challenge_button.png       # 挑战按钮
├── attack_button.png          # 进攻按钮
├── auto_battle.png           # 自动战斗按钮
├── victory.png               # 胜利标识
├── defeat.png                # 失败标识
└── ...                       # 其他模板
```

## 🛠️ 技术实现

### 网格定位算法
```python
# 3x3网格配置
REALM_GRID_CONFIG = {
    "grid_area": {"left": 0.12, "top": 0.20, "right": 0.88, "bottom": 0.80},
    "layout": {"rows": 3, "columns": 3},
    "item_size": {"width": 0.22, "height": 0.15}
}
```

### 智能选择逻辑
```python
# 优先级分组
priority_groups = {
    "high": [0, 2, 6, 8],      # 四个角
    "medium": [1, 3, 5, 7],    # 四条边
    "low": [4]                 # 中心位置
}
```

### 突破券检测
```python
# OCR配置
RAID_TICKET_CONFIG = {
    "ticket_area": {"left": 0.75, "top": 0.05, "right": 0.95, "bottom": 0.15},
    "ocr_config": {"number_only": True, "min_confidence": 0.7}
}
```

## ⚠️ 注意事项

### 安全提醒
- **合规使用**：请遵守游戏规则，谨慎使用自动化工具
- **适度使用**：建议间歇性使用，避免长时间连续运行
- **监控运行**：运行时请注意观察，及时处理异常情况

### 环境要求
- **操作系统**：Windows 10/11
- **Python版本**：3.7+
- **依赖库**：PyQt5, OpenCV, numpy, PIL
- **游戏版本**：阴阳师PC版

### 故障排除
1. **无法识别窗口**：检查游戏是否正常运行，尝试重启程序
2. **模板匹配失败**：检查模板图像是否存在，更新模板文件
3. **突破券检测错误**：调整OCR配置参数，检查券显示区域
4. **网格定位不准**：根据实际分辨率调整网格配置

## 📊 性能优化

### 推荐配置
- **策略选择**：smart_grid（平衡效率和安全性）
- **随机延迟**：2-5秒（避免检测）
- **休息间隔**：30-60分钟（保持账号安全）
- **最大次数**：10-20次（单次运行）

### 效率提升
- 使用后台模式减少窗口干扰
- 合理设置战斗超时时间
- 启用自动战斗功能
- 定期更新模板图像

## 🔄 更新日志

### v1.0.0 (2024-01-20)
- ✅ 实现3x3网格定位系统
- ✅ 添加6种智能选择策略
- ✅ 集成突破券检测功能
- ✅ 完善GUI界面和配置选项
- ✅ 添加安全机制和错误处理

## 📞 技术支持

如果遇到问题或需要帮助，请：
1. 查看日志输出获取错误信息
2. 检查配置文件和模板文件
3. 参考故障排除章节
4. 提交详细的问题描述和日志

---

**免责声明**：本工具仅供学习和研究使用，使用者需自行承担使用风险。
