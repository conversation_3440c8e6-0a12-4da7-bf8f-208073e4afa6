#!/usr/bin/env python3
"""测试交互式网格配置"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from PyQt5.QtWidgets import QApplication
    from features.realm_raid.interactive_grid_config import InteractiveGridConfigDialog
    import json
    
    def test_interactive_config():
        """测试交互式网格配置"""
        app = QApplication(sys.argv)
        
        # 默认配置
        default_config = {
            "grid_area": {
                "left": 0.12,
                "top": 0.20,
                "right": 0.88,
                "bottom": 0.80
            },
            "layout": {
                "rows": 3,
                "columns": 3,
                "item_spacing": {
                    "horizontal": 0.02,
                    "vertical": 0.03
                }
            },
            "item_size": {
                "width": 0.22,
                "height": 0.15
            }
        }
        
        print("🚀 启动交互式网格配置测试...")
        
        dialog = InteractiveGridConfigDialog(None, default_config)
        
        if dialog.exec_() == dialog.Accepted:
            config = dialog.get_config()
            print("✅ 用户配置:")
            print(json.dumps(config, indent=2, ensure_ascii=False))
            
            # 保存测试配置
            with open("test_config.json", 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            print("💾 配置已保存到 test_config.json")
        else:
            print("❌ 用户取消了配置")
        
        app.quit()

    if __name__ == "__main__":
        test_interactive_config()
        
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保已安装 PyQt5: pip install PyQt5")
except Exception as e:
    print(f"❌ 运行错误: {e}")
    import traceback
    traceback.print_exc()
