#!/usr/bin/env python3
"""测试窗口句柄传递"""

import sys
import os
import win32gui

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def find_game_windows():
    """查找游戏窗口"""
    windows = []
    game_titles = ["阴阳师", "Onmyoji", "网易"]
    
    def enum_callback(hwnd, windows_list):
        if win32gui.IsWindowVisible(hwnd):
            title = win32gui.GetWindowText(hwnd)
            if title and any(game_title in title for game_title in game_titles):
                rect = win32gui.GetWindowRect(hwnd)
                windows_list.append({
                    'hwnd': hwnd,
                    'title': title,
                    'rect': rect,
                    'width': rect[2] - rect[0],
                    'height': rect[3] - rect[1]
                })
        return True
        
    win32gui.EnumWindows(enum_callback, windows)
    return windows

def test_realm_raid_bot_with_hwnd(hwnd):
    """测试RealmRaidBot是否能正确使用窗口句柄"""
    try:
        from features.realm_raid.realm_raid_bot import RealmRaidBot
        
        print(f"🤖 创建RealmRaidBot实例，句柄: {hwnd}")
        bot = RealmRaidBot(hwnd)
        
        print("📸 测试截图功能...")
        screenshot = bot.take_screenshot()
        
        if screenshot is not None:
            print(f"✅ 截图成功！尺寸: {screenshot.size}")
            return True
        else:
            print("❌ 截图失败！")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 测试窗口句柄传递...")
    
    # 1. 查找游戏窗口
    print("\n1. 查找游戏窗口...")
    game_windows = find_game_windows()
    
    if not game_windows:
        print("❌ 未找到游戏窗口！请确保阴阳师游戏正在运行。")
        return
    
    print(f"✅ 找到 {len(game_windows)} 个游戏窗口:")
    for i, window in enumerate(game_windows):
        print(f"  {i+1}. {window['title']} (句柄: {window['hwnd']}, 大小: {window['width']}×{window['height']})")
    
    # 2. 测试第一个窗口
    test_window = game_windows[0]
    hwnd = test_window['hwnd']
    
    print(f"\n2. 测试窗口句柄: {hwnd}")
    print(f"   窗口标题: {test_window['title']}")
    print(f"   窗口大小: {test_window['width']}×{test_window['height']}")
    
    # 3. 测试RealmRaidBot
    print(f"\n3. 测试RealmRaidBot...")
    success = test_realm_raid_bot_with_hwnd(hwnd)
    
    if success:
        print("✅ 窗口句柄传递测试成功！")
        print("✅ RealmRaidBot可以正确使用窗口句柄！")
    else:
        print("❌ 窗口句柄传递测试失败！")
    
    # 4. 测试覆盖工具
    print(f"\n4. 测试覆盖工具...")
    try:
        from PyQt5.QtWidgets import QApplication
        from features.realm_raid.true_game_overlay import TrueGameOverlayController
        
        # 创建模拟的主窗口类
        class MockMainWindow:
            def __init__(self, hwnd):
                self.hwnd = hwnd
            
            def get_current_hwnd(self):
                return self.hwnd
        
        app = QApplication(sys.argv)
        
        # 创建模拟主窗口
        mock_main = MockMainWindow(hwnd)
        
        # 创建覆盖工具
        overlay = TrueGameOverlayController(mock_main)
        
        if overlay.game_window:
            print("✅ 覆盖工具自动连接成功！")
            print(f"   连接窗口: {overlay.game_window['title']}")
            print(f"   窗口句柄: {overlay.game_window['hwnd']}")
        else:
            print("❌ 覆盖工具自动连接失败！")
        
        overlay.show()
        print("✅ 覆盖工具界面已显示，可以手动测试！")
        
        # 不自动退出，让用户手动测试
        print("\n🎯 覆盖工具已启动，您可以:")
        print("   1. 点击'🟢 显示覆盖'测试覆盖效果")
        print("   2. 点击'🔍 调试信息'查看详细信息")
        print("   3. 关闭窗口退出测试")
        
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ 覆盖工具测试失败: {e}")

if __name__ == "__main__":
    main()
