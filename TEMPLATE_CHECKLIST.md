# 结界突破模板检查清单

## 🎯 制作进度跟踪

### 阶段一: 核心导航 (必须完成)
- [ ] guild_button.png - 阴阳寮按钮
- [ ] realm_raid_button.png - 结界突破按钮  
- [ ] challenge_button.png - 挑战按钮

### 阶段二: 战斗控制 (重要)
- [ ] attack_button.png - 进攻按钮
- [ ] auto_battle.png - 自动战斗按钮
- [ ] victory.png - 胜利标识

### 阶段三: 结果处理 (建议)
- [ ] defeat.png - 失败标识
- [ ] confirm_button.png - 确认按钮
- [ ] back_button.png - 返回按钮

### 阶段四: 状态检测 (可选)
- [ ] no_tickets.png - 没有突破券
- [ ] cooldown.png - 冷却时间
- [ ] refresh_button.png - 刷新按钮
- [ ] realm_list_area.png - 结界列表区域
- [ ] battle_start.png - 战斗开始
- [ ] battle_end.png - 战斗结束

## 📝 制作说明

1. 启动阴阳师游戏
2. 按照阶段顺序逐个截图
3. 保存到 templates/realm_raid/ 目录
4. 运行 python test_templates.py 验证
5. 勾选已完成的项目

## ✅ 完成标准

- 阶段一完成：基本功能可用
- 阶段二完成：自动战斗可用  
- 阶段三完成：结果处理完善
- 阶段四完成：功能完全自动化

祝制作顺利！🚀
