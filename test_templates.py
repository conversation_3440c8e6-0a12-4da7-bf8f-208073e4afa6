#!/usr/bin/env python3
"""结界突破模板测试脚本"""

import os
import cv2
import logging
from pathlib import Path
from features.realm_raid.realm_raid_config import REALM_RAID_TEMPLATES

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def check_template_files():
    """检查模板文件是否存在"""
    print("🔍 检查结界突破模板文件")
    print("=" * 50)
    
    missing_files = []
    existing_files = []
    
    for template_name, config in REALM_RAID_TEMPLATES.items():
        template_path = config["path"]
        description = config["description"]
        
        if os.path.exists(template_path):
            # 检查文件大小
            file_size = os.path.getsize(template_path)
            if file_size > 0:
                existing_files.append((template_name, template_path, description))
                print(f"✅ {template_name}: {description}")
                print(f"   路径: {template_path}")
                print(f"   大小: {file_size} bytes")
            else:
                missing_files.append((template_name, template_path, description, "文件为空"))
                print(f"⚠️ {template_name}: {description} (文件为空)")
        else:
            missing_files.append((template_name, template_path, description, "文件不存在"))
            print(f"❌ {template_name}: {description}")
            print(f"   缺失路径: {template_path}")
        print()
    
    return existing_files, missing_files

def validate_template_images(existing_files):
    """验证模板图像质量"""
    print("🖼️ 验证模板图像质量")
    print("=" * 50)
    
    valid_templates = []
    invalid_templates = []
    
    for template_name, template_path, description in existing_files:
        try:
            # 尝试加载图像
            image = cv2.imread(template_path)
            
            if image is None:
                invalid_templates.append((template_name, template_path, "无法读取图像"))
                print(f"❌ {template_name}: 无法读取图像文件")
                continue
            
            height, width = image.shape[:2]
            
            # 检查图像尺寸
            if width < 10 or height < 10:
                invalid_templates.append((template_name, template_path, "图像尺寸过小"))
                print(f"⚠️ {template_name}: 图像尺寸过小 ({width}x{height})")
            elif width > 500 or height > 500:
                invalid_templates.append((template_name, template_path, "图像尺寸过大"))
                print(f"⚠️ {template_name}: 图像尺寸较大 ({width}x{height}) - 建议缩小")
            else:
                valid_templates.append((template_name, template_path, description))
                print(f"✅ {template_name}: 图像质量良好 ({width}x{height})")
                
        except Exception as e:
            invalid_templates.append((template_name, template_path, f"验证失败: {e}"))
            print(f"❌ {template_name}: 验证失败 - {e}")
    
    return valid_templates, invalid_templates

def create_missing_template_guide(missing_files):
    """创建缺失模板制作指南"""
    if not missing_files:
        return
    
    print("📋 缺失模板制作指南")
    print("=" * 50)
    
    # 按优先级分组
    priority_groups = {
        "🔴 第一优先级 (基本功能 - 必须)": [
            "guild_button", "realm_raid_button", "attack_button", "auto_battle", "victory"
        ],
        "🟡 第二优先级 (完善功能 - 重要)": [
            "challenge_button", "realm_list_area", "defeat"
        ],
        "🟢 第三优先级 (结果处理 - 建议)": [
            "confirm_button", "battle_start", "battle_end"
        ],
        "🔵 第四优先级 (状态检测 - 可选)": [
            "no_tickets", "cooldown", "refresh_button", "back_button", "realm_background"
        ]
    }
    
    for priority_name, template_names in priority_groups.items():
        group_missing = [
            (name, path, desc, reason) for name, path, desc, reason in missing_files
            if name in template_names
        ]
        
        if group_missing:
            print(f"\n{priority_name}")
            print("-" * 30)
            
            for template_name, template_path, description, reason in group_missing:
                print(f"📸 {template_name}: {description}")
                print(f"   保存路径: {template_path}")
                print(f"   状态: {reason}")
                
                # 提供截图指导
                if template_name == "guild_button":
                    print("   📝 截图指导: 主界面右下角的'阴阳寮'按钮")
                elif template_name == "realm_raid_button":
                    print("   📝 截图指导: 阴阳寮界面中的'结界突破'按钮")
                elif template_name == "challenge_button":
                    print("   📝 截图指导: 选择结界后出现的'挑战'按钮")
                elif template_name == "auto_battle":
                    print("   📝 截图指导: 战斗界面右下角的'自动'按钮")
                elif template_name == "victory":
                    print("   📝 截图指导: 战斗胜利后的'胜利'文字或图标")
                
                print()

def create_template_directories():
    """创建模板目录结构"""
    print("📁 创建模板目录结构")
    print("=" * 50)
    
    template_dir = Path("templates/realm_raid")
    
    if not template_dir.exists():
        template_dir.mkdir(parents=True, exist_ok=True)
        print(f"✅ 已创建目录: {template_dir}")
    else:
        print(f"✅ 目录已存在: {template_dir}")
    
    # 创建README文件
    readme_path = template_dir / "README.md"
    if not readme_path.exists():
        readme_content = """# 结界突破模板文件

## 文件说明

这个目录包含结界突破功能所需的所有模板图像文件。

### 必需文件列表

#### 导航模板
- `guild_button.png` - 阴阳寮按钮
- `realm_raid_button.png` - 结界突破按钮

#### 操作模板  
- `challenge_button.png` - 挑战按钮
- `attack_button.png` - 进攻按钮
- `auto_battle.png` - 自动战斗按钮

#### 结果模板
- `victory.png` - 胜利标识
- `defeat.png` - 失败标识
- `confirm_button.png` - 确认按钮

### 制作要求

1. **格式**: PNG格式
2. **尺寸**: 建议50-200像素范围
3. **清晰度**: 确保图像清晰无模糊
4. **特征性**: 截取最具特征的部分

### 测试方法

运行 `python test_templates.py` 来验证模板文件。
"""
        
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        print(f"✅ 已创建说明文件: {readme_path}")

def generate_template_checklist():
    """生成模板检查清单"""
    print("📋 生成模板检查清单")
    print("=" * 50)
    
    checklist_content = """# 结界突破模板检查清单

## 🎯 制作进度跟踪

### 阶段一: 核心导航 (必须完成)
- [ ] guild_button.png - 阴阳寮按钮
- [ ] realm_raid_button.png - 结界突破按钮  
- [ ] challenge_button.png - 挑战按钮

### 阶段二: 战斗控制 (重要)
- [ ] attack_button.png - 进攻按钮
- [ ] auto_battle.png - 自动战斗按钮
- [ ] victory.png - 胜利标识

### 阶段三: 结果处理 (建议)
- [ ] defeat.png - 失败标识
- [ ] confirm_button.png - 确认按钮
- [ ] back_button.png - 返回按钮

### 阶段四: 状态检测 (可选)
- [ ] no_tickets.png - 没有突破券
- [ ] cooldown.png - 冷却时间
- [ ] refresh_button.png - 刷新按钮
- [ ] realm_list_area.png - 结界列表区域
- [ ] battle_start.png - 战斗开始
- [ ] battle_end.png - 战斗结束

## 📝 制作说明

1. 启动阴阳师游戏
2. 按照阶段顺序逐个截图
3. 保存到 templates/realm_raid/ 目录
4. 运行 python test_templates.py 验证
5. 勾选已完成的项目

## ✅ 完成标准

- 阶段一完成：基本功能可用
- 阶段二完成：自动战斗可用  
- 阶段三完成：结果处理完善
- 阶段四完成：功能完全自动化

祝制作顺利！🚀
"""
    
    with open("TEMPLATE_CHECKLIST.md", 'w', encoding='utf-8') as f:
        f.write(checklist_content)
    
    print("✅ 已生成检查清单: TEMPLATE_CHECKLIST.md")

def main():
    """主函数"""
    print("🚀 结界突破模板测试工具")
    print("=" * 60)
    
    # 创建目录结构
    create_template_directories()
    print()
    
    # 检查模板文件
    existing_files, missing_files = check_template_files()
    print()
    
    # 验证现有模板
    if existing_files:
        valid_templates, invalid_templates = validate_template_images(existing_files)
        print()
    else:
        valid_templates = []
        invalid_templates = []
    
    # 生成缺失模板指南
    if missing_files:
        create_missing_template_guide(missing_files)
        print()
    
    # 生成检查清单
    generate_template_checklist()
    print()
    
    # 总结报告
    print("📊 测试总结")
    print("=" * 30)
    print(f"✅ 有效模板: {len(valid_templates)} 个")
    print(f"⚠️ 无效模板: {len(invalid_templates)} 个")
    print(f"❌ 缺失模板: {len(missing_files)} 个")
    print(f"📋 总计需要: {len(REALM_RAID_TEMPLATES)} 个")
    
    completion_rate = len(valid_templates) / len(REALM_RAID_TEMPLATES) * 100
    print(f"🎯 完成度: {completion_rate:.1f}%")
    
    if completion_rate >= 100:
        print("\n🎉 恭喜！所有模板已准备完成！")
    elif completion_rate >= 80:
        print("\n🎊 很好！大部分模板已完成，功能基本可用！")
    elif completion_rate >= 50:
        print("\n👍 不错！一半以上模板已完成，继续加油！")
    elif completion_rate >= 20:
        print("\n💪 开始了！已有部分模板，请继续制作！")
    else:
        print("\n📸 开始制作模板吧！请参考 TEMPLATE_PREPARATION_GUIDE.md")
    
    print(f"\n📖 详细指南: TEMPLATE_PREPARATION_GUIDE.md")
    print(f"📋 制作清单: TEMPLATE_CHECKLIST.md")

if __name__ == "__main__":
    main()
