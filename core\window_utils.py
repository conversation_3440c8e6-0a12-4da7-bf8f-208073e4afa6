import win32gui
import win32api
import win32con
import psutil
import logging
import time
from typing import List, Tuple, Optional, Callable, Dict, Union

class WindowUtils:
    """窗口操作工具类
    
    提供查找、获取和操作Windows窗口的方法
    """
    
    @staticmethod
    def find_window_by_title(title: str) -> Optional[int]:
        """通过标题查找窗口
        
        Args:
            title: 窗口标题（部分匹配）
            
        Returns:
            int: 窗口句柄，如果未找到则返回None
        """
        logger = logging.getLogger('阴阳师辅助工具.窗口')
        result = None
        
        try:
            def callback(hwnd, hwnds):
                if win32gui.IsWindowVisible(hwnd) and win32gui.IsWindowEnabled(hwnd):
                    window_title = win32gui.GetWindowText(hwnd)
                    if title.lower() in window_title.lower():
                        hwnds.append(hwnd)
                return True
                
            hwnds = []
            win32gui.EnumWindows(callback, hwnds)
            
            if hwnds:
                # 优先选择主窗口（没有父窗口的窗口）
                for hwnd in hwnds:
                    if win32gui.GetParent(hwnd) == 0:
                        result = hwnd
                        break
                        
                # 如果没有找到主窗口，返回第一个匹配的窗口
                if not result and hwnds:
                    result = hwnds[0]
                    
                logger.info(f"找到窗口: {win32gui.GetWindowText(result)}, 句柄: {result}")
            else:
                logger.warning(f"未找到标题包含 '{title}' 的窗口")
        except Exception as e:
            logger.error(f"查找窗口时出错: {str(e)}")
            
        return result
    
    @staticmethod
    def find_all_windows_by_title(title: str) -> List[Tuple[int, str]]:
        """查找所有标题匹配的窗口
        
        Args:
            title: 窗口标题，支持部分匹配
            
        Returns:
            list: 窗口句柄和标题的列表 [(hwnd, title), ...]
        """
        result = []
        
        def callback(hwnd, hwnds):
            if win32gui.IsWindowVisible(hwnd):
                window_title = win32gui.GetWindowText(hwnd)
                if title.lower() in window_title.lower():
                    hwnds.append((hwnd, window_title))
            return True
            
        win32gui.EnumWindows(callback, result)
        return result
    
    @staticmethod
    def get_window_info(hwnd: int) -> Dict[str, Union[str, Tuple[int, int, int, int]]]:
        """获取窗口信息
        
        Args:
            hwnd: 窗口句柄
            
        Returns:
            dict: 窗口信息
        """
        logger = logging.getLogger('阴阳师辅助工具.窗口')
        info = {
            'title': '',
            'rect': (0, 0, 0, 0),
            'size': (0, 0),
            'position': (0, 0)
        }
        
        try:
            # 获取窗口标题
            info['title'] = win32gui.GetWindowText(hwnd)
            
            # 获取窗口矩形
            rect = win32gui.GetWindowRect(hwnd)
            info['rect'] = rect
            
            # 计算窗口尺寸和位置
            left, top, right, bottom = rect
            width = right - left
            height = bottom - top
            
            info['size'] = (width, height)
            info['position'] = (left, top)
            
            logger.debug(f"窗口信息: 标题[{info['title']}], 位置[{left},{top}], 尺寸[{width}x{height}]")
        except Exception as e:
            logger.error(f"获取窗口信息时出错: {str(e)}")
            
        return info
    
    @staticmethod
    def find_child_windows(hwnd: int) -> List[int]:
        """获取子窗口句柄列表
        
        Args:
            hwnd: 父窗口句柄
            
        Returns:
            list: 子窗口句柄列表
        """
        result = []
        
        def callback(child_hwnd, hwnds):
            hwnds.append(child_hwnd)
            return True
            
        try:
            win32gui.EnumChildWindows(hwnd, callback, result)
        except Exception:
            pass
            
        return result
    
    @staticmethod
    def activate_window(hwnd: int) -> bool:
        """激活窗口（使窗口成为前台窗口）
        
        Args:
            hwnd: 窗口句柄
            
        Returns:
            bool: 是否成功激活
        """
        if not hwnd:
            return False
            
        try:
            # 检查窗口是否最小化
            if win32gui.IsIconic(hwnd):
                # 恢复窗口
                win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
            
            # 设置为前台窗口
            win32gui.SetForegroundWindow(hwnd)
            
            return True
        except Exception as e:
            logging.error(f"激活窗口失败: {str(e)}")
            return False
    
    @staticmethod
    def hide_window(hwnd: int) -> bool:
        """隐藏窗口
        
        Args:
            hwnd: 窗口句柄
            
        Returns:
            bool: 是否成功隐藏
        """
        if not hwnd:
            return False
            
        try:
            win32gui.ShowWindow(hwnd, win32con.SW_HIDE)
            return True
        except Exception as e:
            logging.error(f"隐藏窗口失败: {str(e)}")
            return False
    
    @staticmethod
    def show_window(hwnd: int) -> bool:
        """显示窗口
        
        Args:
            hwnd: 窗口句柄
            
        Returns:
            bool: 是否成功显示
        """
        if not hwnd:
            return False
            
        try:
            win32gui.ShowWindow(hwnd, win32con.SW_SHOW)
            return True
        except Exception as e:
            logging.error(f"显示窗口失败: {str(e)}")
            return False
            
    @staticmethod
    def move_window(hwnd: int, x: int, y: int, width: int, height: int, repaint: bool = True) -> bool:
        """移动并调整窗口大小
        
        Args:
            hwnd: 窗口句柄
            x: 左上角X坐标
            y: 左上角Y坐标
            width: 宽度
            height: 高度
            repaint: 是否重绘
            
        Returns:
            bool: 是否成功移动
        """
        if not hwnd:
            return False
            
        try:
            win32gui.MoveWindow(hwnd, x, y, width, height, repaint)
            return True
        except Exception as e:
            logging.error(f"移动窗口失败: {str(e)}")
            return False
    
    @staticmethod
    def find_game_window() -> Optional[int]:
        """查找阴阳师游戏窗口
        
        根据常见的窗口标题和进程名查找
        
        Returns:
            int: 窗口句柄，如果未找到则返回None
        """
        logger = logging.getLogger('阴阳师辅助工具.窗口')
        logger.info("开始查找阴阳师游戏窗口")
        
        # 常见的窗口标题和进程名
        window_titles = [
            "阴阳师", "Onmyoji", "网易游戏", "NetEase Games", 
            "阴阳师-网易游戏", "阴阳师-网易", "阴阳师魂灵", "阴阳师本格派"
        ]
        
        process_names = [
            "onmyoji.exe", "YYSOnmyoji.exe", "yys.exe", "Onmyoji.exe"
        ]
        
        # 1. 首先通过标题查找
        for title in window_titles:
            try:
                hwnd = WindowUtils.find_window_by_title(title)
                if hwnd:
                    logger.info(f"通过标题找到游戏窗口: {win32gui.GetWindowText(hwnd)}")
                    return hwnd
            except Exception as e:
                logger.error(f"通过标题查找窗口时出错: {str(e)}")
        
        # 2. 通过进程名查找
        try:
            logger.info("通过标题未找到，尝试通过进程名查找")
            for proc in psutil.process_iter(['pid', 'name']):
                for proc_name in process_names:
                    if proc.info['name'] and proc.info['name'].lower() == proc_name.lower():
                        pid = proc.info['pid']
                        logger.info(f"找到游戏进程: {proc_name}, PID: {pid}")
                        
                        # 查找对应的窗口
                        def callback(hwnd, ctx):
                            try:
                                # 获取窗口对应的进程ID
                                _, window_pid = win32process.GetWindowThreadProcessId(hwnd)
                                if window_pid == pid and win32gui.IsWindowVisible(hwnd):
                                    ctx.append(hwnd)
                            except Exception:
                                pass
                            return True
                            
                        windows = []
                        win32gui.EnumWindows(callback, windows)
                        
                        # 找到窗口
                        if windows:
                            # 优先选择主窗口
                            main_window = None
                            for window in windows:
                                if win32gui.GetParent(window) == 0:
                                    main_window = window
                                    break
                                    
                            hwnd = main_window if main_window else windows[0]
                            window_title = win32gui.GetWindowText(hwnd)
                            logger.info(f"找到游戏窗口: {window_title}, 句柄: {hwnd}")
                            return hwnd
        except Exception as e:
            logger.error(f"通过进程名查找窗口时出错: {str(e)}")
        
        logger.warning("未找到阴阳师游戏窗口")
        return None
    
    @staticmethod
    def bring_window_to_front(hwnd: int) -> bool:
        """将窗口置于前台
        
        Args:
            hwnd: 窗口句柄
            
        Returns:
            bool: 是否成功
        """
        logger = logging.getLogger('阴阳师辅助工具.窗口')
        logger.info(f"尝试将窗口 {hwnd} 置于前台")
        
        if not hwnd:
            logger.warning("无效的窗口句柄")
            return False
            
        try:
            # 如果窗口最小化，还原它
            if win32gui.IsIconic(hwnd):
                logger.info("窗口最小化，还原窗口")
                win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
                time.sleep(0.5)  # 等待还原完成
                
            # 设置为前台窗口
            win32gui.SetForegroundWindow(hwnd)
            
            # 检查是否成功
            foreground_hwnd = win32gui.GetForegroundWindow()
            if foreground_hwnd == hwnd:
                logger.info("窗口已成功置于前台")
                return True
            else:
                title = win32gui.GetWindowText(foreground_hwnd)
                logger.warning(f"无法置于前台，当前前台窗口: {title} ({foreground_hwnd})")
                
                # 再次尝试
                logger.info("再次尝试置于前台")
                win32gui.BringWindowToTop(hwnd)
                win32gui.SetForegroundWindow(hwnd)
                time.sleep(0.2)
                
                foreground_hwnd = win32gui.GetForegroundWindow()
                if foreground_hwnd == hwnd:
                    logger.info("第二次尝试成功")
                    return True
                    
                logger.warning("多次尝试后仍然无法置于前台")
                return False
        except Exception as e:
            logger.error(f"置于前台时出错: {str(e)}")
            return False 