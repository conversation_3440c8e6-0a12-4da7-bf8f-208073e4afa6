# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个阴阳师游戏自动化辅助工具，采用模块化设计，支持多种游戏功能的自动化。项目采用Python开发，具有完整的图形界面和命令行接口。

## 常用命令

### 运行应用程序
```bash
# GUI版本（推荐）
python main_gui.py

# 旧版本GUI
python main.py

# 命令行版本探索功能
python main.py explore --title "游戏窗口标题"
python main.py explore --hwnd 12345
```

### 测试和开发
```bash
# 测试章节选择功能
python test_chapter_selection.py

# 运行特定模块测试（如果有的话）
python -m pytest tests/  # 如果存在测试目录
```

### 依赖管理
```bash
# 安装依赖（需要先创建requirements.txt）
pip install -r requirements.txt

# 主要依赖包括：
# - PyQt5 (GUI框架)
# - opencv-python (图像处理)
# - Pillow (图像操作)
# - numpy (数值计算)
# - pywin32 (Windows API调用)
```

## 项目架构

### 核心设计理念
- **模块化设计**：核心功能与具体业务功能分离
- **自适应模板系统**：支持不同分辨率和屏幕尺寸
- **后台操作支持**：可在窗口最小化状态下运行
- **反检测机制**：模拟人类操作行为

### 目录结构
```
core/                    # 核心功能模块
├── base_bot.py         # 基础自动化机器人类（继承AntiDetection）
├── adaptive_template.py # 自适应模板匹配系统
├── background_operation.py # 后台操作核心
├── template_manager.py  # 模板图像管理
├── window_utils.py     # 窗口操作工具
├── image_utils.py      # 图像识别工具
├── auto_clicker.py     # 自动点击工具
├── anti_detection.py   # 反检测机制
├── screenshot.py       # 屏幕截图工具
├── ocr_utils.py        # OCR文字识别
└── chapter_selector.py # 章节选择功能

features/               # 功能模块
└── explore/           # 探索功能
    ├── explore_bot.py    # 探索机器人实现
    ├── explore_gui.py    # 探索GUI界面
    └── explore_config.py # 探索配置管理

config/                # 配置文件
├── config.json        # 主配置文件
└── explore_settings.json # 探索功能配置

ui/                    # 用户界面（如果存在）
main.py               # 命令行主程序
main_gui.py           # GUI主程序
```

### 核心类和继承关系
- `BaseBot` 继承自 `AntiDetection`，提供基础自动化功能
- `ExploreBot` 继承自 `BaseBot`，实现探索副本功能
- 所有功能机器人都应继承自 `BaseBot` 以获得核心功能

### 自适应模板系统
- 使用相对坐标系统（0.0-1.0）而非绝对像素坐标
- 支持动态分辨率缩放和模板匹配
- 基准分辨率通常为1920x1080
- 模板加载时需指定相对点击位置

### 后台操作系统
- 支持窗口最小化状态下的截图和操作
- 使用Windows消息系统发送鼠标和键盘事件
- 可根据窗口状态自动切换前台/后台模式

## 开发指南

### 添加新功能模块
1. 在 `features/` 下创建新功能目录
2. 创建 `xxx_bot.py`（继承BaseBot）、`xxx_gui.py`、`xxx_config.py`
3. 在主界面添加新功能标签页
4. 将功能特有模板放在 `templates/xxx/` 目录

### 使用自适应模板
```python
# 初始化时指定基准分辨率
bot = SomeBot(hwnd=game_hwnd, base_resolution=(1920, 1080))

# 加载模板并指定相对点击位置
bot.load_template("button", "templates/button.png", rel_click_point=(0.5, 0.5))

# 使用模板进行操作
bot.click_template("button")
```

### 启用后台操作
```python
# 启用后台模式
bot.enable_background_mode(True)

# 正常使用，会自动调用后台实现
bot.click(100, 200)
screenshot = bot.take_screenshot()
```

## 配置文件说明

### config.json
- `operation_mode`: 操作模式（"foreground" 或 "background"）

### explore_settings.json
- `use_background_mode`: 是否使用后台模式

## 注意事项

### 安全和合规
- 这是游戏自动化工具，使用时需注意游戏规则
- 包含反检测机制，但仍需谨慎使用
- 不要长时间连续运行

### 开发约定
- 所有新功能都应继承BaseBot类
- 使用相对坐标而非绝对坐标
- 模板文件按功能分类存放
- GUI组件使用PyQt5框架
- 日志记录到onmyoji_log.txt文件

### 窗口处理
- 使用win32gui进行窗口操作
- 支持通过窗口标题或句柄查找游戏窗口
- 自动处理窗口尺寸变化和分辨率适配

### Cursor规则文件
项目包含三个Cursor规则文件：
- `jieshou.mdc`: 快速接手代码分析规则
- `jishuguwen.mdc`: 技术顾问需求分析规则  
- `qianduankaifa.mdc`: 前后端开发文档规则