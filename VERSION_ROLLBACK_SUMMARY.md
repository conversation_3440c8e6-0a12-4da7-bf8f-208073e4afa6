# 📋 版本回退总结 - 回退到 v0.0.1

## 🔄 回退操作

### 执行的命令
```bash
# 1. 保存当前更改到stash
git stash push -m "保存当前OAS风格重新设计的更改"

# 2. 硬回退到v0.0.1标签
git reset --hard v0.0.1
```

### 回退结果
- ✅ **成功回退到v0.0.1版本**
- ✅ **当前HEAD**: 188be8d (tag: v0.0.1, origin/master)
- ✅ **分支**: dev-v0.0.1
- ✅ **更改已保存**: stash@{0} - "保存当前OAS风格重新设计的更改"

## 📊 当前状态

### 版本信息
- **当前版本**: v0.0.1
- **提交哈希**: 188be8d
- **分支**: dev-v0.0.1
- **标签**: v0.0.1

### 文件结构
```
阴阳师自动化工具 v0.0.1
├── main.py                    # 主程序入口
├── main_gui.py               # GUI主界面
├── config.json               # 配置文件
├── core/                     # 核心功能模块
│   ├── base_bot.py          # 基础机器人类
│   ├── window_utils.py      # 窗口工具
│   ├── screenshot.py        # 截图功能
│   ├── template_manager.py  # 模板管理
│   └── ...                  # 其他核心模块
└── features/                # 功能模块
    └── explore/             # 探索功能
        ├── explore_gui.py   # 探索GUI
        ├── explore_bot.py   # 探索机器人
        └── explore_config.py # 探索配置
```

### 功能特性
- 🗺️ **探索副本功能**: 完整的探索自动化
- 🎮 **GUI界面**: PyQt5图形用户界面
- ⚙️ **配置管理**: JSON配置文件
- 🖼️ **模板匹配**: 图像识别和模板匹配
- 🪟 **窗口管理**: 游戏窗口检测和操作

## 💾 保存的更改

### Stash内容
保存在 `stash@{0}` 中的更改包括：

1. **OAS风格重新设计**
   - oas_style_redesign.py - 基于OAS架构的后端系统
   - oas_web_gui.py - Web前端界面
   - templates/index.html - 响应式Web界面

2. **结界突破V2.0系统**
   - features/barrier_breakthrough_v2/ - 完整的V2.0系统
   - 智能识别、任务调度、配置管理等

3. **窗口集成改进**
   - 统一窗口管理
   - 智能窗口检测
   - 完善的错误处理

4. **文档和示例**
   - 详细的架构文档
   - 使用指南和示例代码
   - 测试脚本和诊断工具

## 🔄 如何恢复更改

如果需要恢复之前的OAS风格设计，可以使用：

```bash
# 恢复stash中的更改
git stash pop

# 或者查看stash内容
git stash show stash@{0}

# 或者应用特定的stash
git stash apply stash@{0}
```

## 📋 v0.0.1 版本特性

### 核心功能
- ✅ **探索副本自动化**: 完整的探索功能
- ✅ **图形用户界面**: PyQt5 GUI
- ✅ **窗口检测**: 自动检测游戏窗口
- ✅ **模板匹配**: 图像识别系统
- ✅ **配置管理**: JSON配置文件

### 技术架构
- **语言**: Python 3.x
- **GUI框架**: PyQt5
- **图像处理**: OpenCV + PIL
- **配置**: JSON
- **日志**: Python logging

### 使用方法
```bash
# 启动GUI界面
python main_gui.py

# 或启动命令行版本
python main.py
```

## 🎯 当前可用功能

### 1. 探索副本
- **章节选择**: 支持多个章节
- **难度设置**: 普通/困难
- **自动邀请**: 智能邀请好友
- **轮数控制**: 设置执行轮数
- **实时监控**: 显示执行状态

### 2. 系统功能
- **窗口管理**: 自动检测和连接游戏窗口
- **模板匹配**: 高精度图像识别
- **配置保存**: 自动保存用户设置
- **日志记录**: 详细的操作日志
- **错误处理**: 完善的异常处理机制

## 🔧 配置文件

### config.json 结构
```json
{
  "window": {
    "title": "阴阳师",
    "hwnd": null
  },
  "explore": {
    "chapter": 28,
    "difficulty": "困难",
    "max_rounds": 100,
    "auto_invite": true
  },
  "system": {
    "log_level": "INFO",
    "template_threshold": 0.8
  }
}
```

## 📝 使用说明

### 启动步骤
1. **启动程序**: `python main_gui.py`
2. **设置游戏窗口**: 在探索标签页中设置游戏窗口
3. **配置参数**: 设置章节、难度等参数
4. **开始探索**: 点击"开始探索"按钮

### 注意事项
- 确保游戏窗口可见且未最小化
- 检查模板文件是否存在
- 确认游戏分辨率和模板匹配
- 定期检查日志文件

## 🏷️ 版本标签

- **v0.0.1**: 初始版本，包含基础探索功能
- **当前位置**: HEAD -> dev-v0.0.1 (188be8d)

## 📞 技术支持

如果遇到问题：
1. 查看 `onmyoji_log.txt` 日志文件
2. 检查配置文件 `config.json`
3. 确认模板文件完整性
4. 验证游戏窗口连接状态

---

## 🎉 总结

✅ **版本回退成功**: 已成功回退到v0.0.1版本  
✅ **功能完整**: 探索副本功能正常可用  
✅ **更改已保存**: OAS风格设计保存在stash中  
✅ **系统稳定**: 回到已验证的稳定版本  

现在您可以使用v0.0.1版本的稳定功能，如需要可以随时恢复之前的更改。
