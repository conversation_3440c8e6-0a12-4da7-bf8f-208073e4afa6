<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="25e6c8c2-48e4-4d82-9bfb-0f85c9666d02" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/.idea/vcs.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/vcs.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/config.json" beforeDir="false" afterPath="$PROJECT_DIR$/config.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/coordinate_fix.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/core/background_operation.py" beforeDir="false" afterPath="$PROJECT_DIR$/core/background_operation.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/core/base_bot.py" beforeDir="false" afterPath="$PROJECT_DIR$/core/base_bot.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/core/gui_common.py" beforeDir="false" afterPath="$PROJECT_DIR$/core/gui_common.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cursor-memory-bank/.cursor/rules/isolation_rules/Core/command-execution.mdc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/cursor-memory-bank/.cursor/rules/isolation_rules/Core/complexity-decision-tree.mdc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/cursor-memory-bank/.cursor/rules/isolation_rules/Core/creative-phase-enforcement.mdc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/cursor-memory-bank/.cursor/rules/isolation_rules/Core/creative-phase-metrics.mdc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/cursor-memory-bank/.cursor/rules/isolation_rules/Core/file-verification.mdc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/cursor-memory-bank/.cursor/rules/isolation_rules/Core/platform-awareness.mdc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/cursor-memory-bank/.cursor/rules/isolation_rules/Level3/planning-comprehensive.mdc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/cursor-memory-bank/.cursor/rules/isolation_rules/Level3/task-tracking-intermediate.mdc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/cursor-memory-bank/.cursor/rules/isolation_rules/Phases/CreativePhase/creative-phase-architecture.mdc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/cursor-memory-bank/.cursor/rules/isolation_rules/main.mdc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/cursor-memory-bank/.cursor/rules/isolation_rules/visual-maps/archive-mode-map.mdc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/cursor-memory-bank/.cursor/rules/isolation_rules/visual-maps/creative-mode-map.mdc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/cursor-memory-bank/.cursor/rules/isolation_rules/visual-maps/implement-mode-map.mdc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/cursor-memory-bank/.cursor/rules/isolation_rules/visual-maps/plan-mode-map.mdc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/cursor-memory-bank/.cursor/rules/isolation_rules/visual-maps/qa-mode-map.mdc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/cursor-memory-bank/.cursor/rules/isolation_rules/visual-maps/reflect-mode-map.mdc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/cursor-memory-bank/.cursor/rules/isolation_rules/visual-maps/van-mode-map.mdc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/cursor-memory-bank/.cursor/rules/isolation_rules/visual-maps/van_mode_split/van-complexity-determination.mdc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/cursor-memory-bank/.cursor/rules/isolation_rules/visual-maps/van_mode_split/van-file-verification.mdc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/cursor-memory-bank/.cursor/rules/isolation_rules/visual-maps/van_mode_split/van-mode-map.mdc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/cursor-memory-bank/.cursor/rules/isolation_rules/visual-maps/van_mode_split/van-platform-detection.mdc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/cursor-memory-bank/.cursor/rules/isolation_rules/visual-maps/van_mode_split/van-qa-checks/build-test.mdc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/cursor-memory-bank/.cursor/rules/isolation_rules/visual-maps/van_mode_split/van-qa-checks/config-check.mdc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/cursor-memory-bank/.cursor/rules/isolation_rules/visual-maps/van_mode_split/van-qa-checks/dependency-check.mdc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/cursor-memory-bank/.cursor/rules/isolation_rules/visual-maps/van_mode_split/van-qa-checks/environment-check.mdc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/cursor-memory-bank/.cursor/rules/isolation_rules/visual-maps/van_mode_split/van-qa-checks/file-verification.mdc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/cursor-memory-bank/.cursor/rules/isolation_rules/visual-maps/van_mode_split/van-qa-main.mdc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/cursor-memory-bank/.cursor/rules/isolation_rules/visual-maps/van_mode_split/van-qa-utils/common-fixes.mdc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/cursor-memory-bank/.cursor/rules/isolation_rules/visual-maps/van_mode_split/van-qa-utils/mode-transitions.mdc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/cursor-memory-bank/.cursor/rules/isolation_rules/visual-maps/van_mode_split/van-qa-utils/reports.mdc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/cursor-memory-bank/.cursor/rules/isolation_rules/visual-maps/van_mode_split/van-qa-utils/rule-calling-guide.mdc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/cursor-memory-bank/.cursor/rules/isolation_rules/visual-maps/van_mode_split/van-qa-utils/rule-calling-help.mdc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/cursor-memory-bank/.cursor/rules/isolation_rules/visual-maps/van_mode_split/van-qa-validation.md.old" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/features/explore/explore_bot.py" beforeDir="false" afterPath="$PROJECT_DIR$/features/explore/explore_bot.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/features/explore/explore_config.py" beforeDir="false" afterPath="$PROJECT_DIR$/features/explore/explore_config.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/features/explore/explore_gui.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/main_gui.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/explore/treasure_chest.png" beforeDir="false" afterPath="$PROJECT_DIR$/templates/explore/treasure_chest.png" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitHubPullRequestSearchHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPEN&quot;,
    &quot;assignee&quot;: &quot;x0aokl&quot;
  }
}</component>
  <component name="GithubPullRequestsUISettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;url&quot;: &quot;https://github.com/vanzan01/cursor-memory-bank.git&quot;,
    &quot;accountId&quot;: &quot;********-e81b-4586-8787-28bf748a232a&quot;
  }
}</component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2wFmVBjJ3CoLQxSCIXbYp50huIW" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Python.main_gui.executor&quot;: &quot;Run&quot;,
    &quot;Python.main_gui_v2.executor&quot;: &quot;Run&quot;,
    &quot;Python.onmyoji_bot.executor&quot;: &quot;Run&quot;,
    &quot;Python.onmyoji_explore.executor&quot;: &quot;Run&quot;,
    &quot;Python.onmyoji_gui.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;dev-v0.0.1&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/阴阳师&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;project.propVCSSupport.DirectoryMappings&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager">
    <configuration default="true" type="PythonConfigurationType" factoryName="Python">
      <module name="探索脚本" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="Tox" factoryName="Tox">
      <module name="探索脚本" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="tests" factoryName="Autodetect">
      <module name="探索脚本" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="_new_additionalArguments" value="&quot;&quot;" />
      <option name="_new_target" value="&quot;&quot;" />
      <option name="_new_targetType" value="&quot;PATH&quot;" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="tests" factoryName="Doctests">
      <module name="探索脚本" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="" />
      <option name="CLASS_NAME" value="" />
      <option name="METHOD_NAME" value="" />
      <option name="FOLDER_NAME" value="" />
      <option name="TEST_TYPE" value="TEST_SCRIPT" />
      <option name="PATTERN" value="" />
      <option name="USE_PATTERN" value="false" />
      <method v="2" />
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-PY-243.26053.29" />
        <option value="bundled-python-sdk-b1dbf8ef85a6-4df51de95216-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-243.26053.29" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="25e6c8c2-48e4-4d82-9bfb-0f85c9666d02" name="更改" comment="" />
      <created>1745645717578</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1745645717578</updated>
      <workItem from="1745645718788" duration="9167000" />
      <workItem from="1745722551439" duration="19811000" />
      <workItem from="1746091744783" duration="1403000" />
      <workItem from="1746115111503" duration="210000" />
      <workItem from="1746445101919" duration="265000" />
      <workItem from="1746445471878" duration="388000" />
      <workItem from="1746613566346" duration="9000" />
      <workItem from="1747965918586" duration="13000" />
      <workItem from="1747965933214" duration="1000" />
      <workItem from="1747965967568" duration="437000" />
      <workItem from="1750648541409" duration="7322000" />
      <workItem from="1750760735497" duration="25000" />
      <workItem from="1752341712599" duration="462000" />
      <workItem from="1752973658383" duration="4170000" />
      <workItem from="1753029105685" duration="1685000" />
      <workItem from="1753058694063" duration="5090000" />
      <workItem from="1753147177921" duration="2735000" />
      <workItem from="1753199772803" duration="3672000" />
      <workItem from="1753233794637" duration="3145000" />
      <workItem from="1753298807905" duration="3036000" />
      <workItem from="1753455606842" duration="3362000" />
      <workItem from="1753631209676" duration="4674000" />
      <workItem from="1753847481423" duration="1935000" />
      <workItem from="1753876694091" duration="335000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/$onmyoji_bot.coverage" NAME="onmyoji_bot 覆盖结果" MODIFIED="1745741367083" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/$onmyoji_explore.coverage" NAME="onmyoji_explore 覆盖结果" MODIFIED="1745767834287" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/$onmyoji_gui.coverage" NAME="onmyoji_gui 覆盖结果" MODIFIED="1747966209436" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/$main_gui.coverage" NAME="main_gui 覆盖结果" MODIFIED="1752977509687" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/$main_gui_v2.coverage" NAME="main_gui_v2 覆盖结果" MODIFIED="1753848884278" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
  </component>
</project>