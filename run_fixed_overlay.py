#!/usr/bin/env python3
"""启动修复的游戏窗口覆盖工具"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from features.realm_raid.fixed_game_overlay import main

if __name__ == "__main__":
    print("🎯 启动修复的游戏窗口覆盖工具...")
    print("✨ 特点：")
    print("   • 控制界面完全不透明")
    print("   • 完整的网格配置功能")
    print("   • 实时调整网格参数")
    print("   • 透明覆盖层直接显示在游戏窗口上")
    print("   • 自动窗口句柄传递")
    main()
