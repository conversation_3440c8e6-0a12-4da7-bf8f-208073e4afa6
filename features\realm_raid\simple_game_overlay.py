#!/usr/bin/env python3
"""简单的游戏界面覆盖工具"""

import sys
import json
import cv2
import numpy as np
import win32gui
import win32ui
import win32con
from PyQt5.QtWidgets import (QApplication, QWidget, QVBoxLayout, QHBoxLayout,
                             QLabel, QSlider, QPushButton, QGroupBox, 
                             QGridLayout, QMessageBox, QSpinBox)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QPixmap, QImage
import mss
import time

class SimpleGameOverlay(QWidget):
    """简单的游戏界面覆盖工具"""
    
    def __init__(self):
        super().__init__()
        self.game_window = None
        self.config = self.get_default_config()
        self.overlay_timer = QTimer()
        self.overlay_timer.timeout.connect(self.draw_overlay)
        self.is_overlaying = False
        self.setup_ui()
        
    def setup_ui(self):
        """设置界面"""
        self.setWindowTitle("游戏界面网格覆盖 (简化版)")
        self.setFixedSize(450, 650)
        self.setWindowFlags(Qt.WindowStaysOnTopHint)
        
        layout = QVBoxLayout()
        
        # 标题
        title = QLabel("游戏界面网格覆盖调整")
        title.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50; margin: 10px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 说明
        info = QLabel("通过截图方式在游戏界面上显示网格覆盖")
        info.setStyleSheet("color: #7f8c8d; margin: 5px;")
        info.setAlignment(Qt.AlignCenter)
        layout.addWidget(info)
        
        # 游戏窗口连接
        connection_group = self.create_connection_group()
        layout.addWidget(connection_group)
        
        # 覆盖控制
        overlay_group = self.create_overlay_group()
        layout.addWidget(overlay_group)
        
        # 网格配置
        config_group = self.create_config_group()
        layout.addWidget(config_group)
        
        # 操作按钮
        button_group = self.create_button_group()
        layout.addWidget(button_group)
        
        self.setLayout(layout)
        
    def create_connection_group(self):
        """创建连接组"""
        group = QGroupBox("游戏窗口")
        layout = QVBoxLayout()
        
        # 连接按钮
        self.connect_btn = QPushButton("🎮 连接游戏窗口")
        self.connect_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        self.connect_btn.clicked.connect(self.connect_game_window)
        layout.addWidget(self.connect_btn)
        
        # 状态显示
        self.status_label = QLabel("未连接游戏窗口")
        self.status_label.setStyleSheet("color: #e74c3c; font-weight: bold; padding: 5px;")
        self.status_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.status_label)
        
        group.setLayout(layout)
        return group
        
    def create_overlay_group(self):
        """创建覆盖控制组"""
        group = QGroupBox("覆盖层控制")
        layout = QVBoxLayout()
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        self.start_overlay_btn = QPushButton("🟢 开始覆盖")
        self.start_overlay_btn.setEnabled(False)
        self.start_overlay_btn.setStyleSheet("background-color: #27ae60; color: white; font-weight: bold; padding: 8px;")
        self.start_overlay_btn.clicked.connect(self.start_overlay)
        button_layout.addWidget(self.start_overlay_btn)
        
        self.stop_overlay_btn = QPushButton("🔴 停止覆盖")
        self.stop_overlay_btn.setEnabled(False)
        self.stop_overlay_btn.setStyleSheet("background-color: #e74c3c; color: white; font-weight: bold; padding: 8px;")
        self.stop_overlay_btn.clicked.connect(self.stop_overlay)
        button_layout.addWidget(self.stop_overlay_btn)
        
        layout.addLayout(button_layout)
        
        # 状态显示
        self.overlay_status = QLabel("覆盖层未启动")
        self.overlay_status.setStyleSheet("color: #7f8c8d; padding: 5px;")
        self.overlay_status.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.overlay_status)
        
        # 说明
        help_text = QLabel("启动覆盖后，网格将显示在游戏界面上\n拖拽滑块可实时调整网格位置")
        help_text.setStyleSheet("color: #95a5a6; font-size: 10px; padding: 5px;")
        help_text.setAlignment(Qt.AlignCenter)
        layout.addWidget(help_text)
        
        group.setLayout(layout)
        return group
        
    def create_config_group(self):
        """创建配置组"""
        group = QGroupBox("网格配置 (实时调整)")
        layout = QVBoxLayout()
        
        # 网格区域
        area_group = QGroupBox("网格区域")
        area_layout = QGridLayout()
        
        # 左边界
        area_layout.addWidget(QLabel("左:"), 0, 0)
        self.left_slider = QSlider(Qt.Horizontal)
        self.left_slider.setRange(0, 50)
        self.left_slider.setValue(12)
        self.left_slider.valueChanged.connect(self.update_config)
        area_layout.addWidget(self.left_slider, 0, 1)
        self.left_label = QLabel("12%")
        area_layout.addWidget(self.left_label, 0, 2)
        
        # 上边界
        area_layout.addWidget(QLabel("上:"), 1, 0)
        self.top_slider = QSlider(Qt.Horizontal)
        self.top_slider.setRange(0, 50)
        self.top_slider.setValue(20)
        self.top_slider.valueChanged.connect(self.update_config)
        area_layout.addWidget(self.top_slider, 1, 1)
        self.top_label = QLabel("20%")
        area_layout.addWidget(self.top_label, 1, 2)
        
        # 右边界
        area_layout.addWidget(QLabel("右:"), 2, 0)
        self.right_slider = QSlider(Qt.Horizontal)
        self.right_slider.setRange(50, 100)
        self.right_slider.setValue(88)
        self.right_slider.valueChanged.connect(self.update_config)
        area_layout.addWidget(self.right_slider, 2, 1)
        self.right_label = QLabel("88%")
        area_layout.addWidget(self.right_label, 2, 2)
        
        # 下边界
        area_layout.addWidget(QLabel("下:"), 3, 0)
        self.bottom_slider = QSlider(Qt.Horizontal)
        self.bottom_slider.setRange(50, 100)
        self.bottom_slider.setValue(80)
        self.bottom_slider.valueChanged.connect(self.update_config)
        area_layout.addWidget(self.bottom_slider, 3, 1)
        self.bottom_label = QLabel("80%")
        area_layout.addWidget(self.bottom_label, 3, 2)
        
        area_group.setLayout(area_layout)
        layout.addWidget(area_group)
        
        # 网格布局
        grid_group = QGroupBox("网格布局")
        grid_layout = QGridLayout()
        
        grid_layout.addWidget(QLabel("行数:"), 0, 0)
        self.rows_spin = QSpinBox()
        self.rows_spin.setRange(1, 5)
        self.rows_spin.setValue(3)
        self.rows_spin.valueChanged.connect(self.update_config)
        grid_layout.addWidget(self.rows_spin, 0, 1)
        
        grid_layout.addWidget(QLabel("列数:"), 0, 2)
        self.columns_spin = QSpinBox()
        self.columns_spin.setRange(1, 5)
        self.columns_spin.setValue(3)
        self.columns_spin.valueChanged.connect(self.update_config)
        grid_layout.addWidget(self.columns_spin, 0, 3)
        
        grid_layout.addWidget(QLabel("水平间距:"), 1, 0)
        self.h_spacing_slider = QSlider(Qt.Horizontal)
        self.h_spacing_slider.setRange(0, 10)
        self.h_spacing_slider.setValue(2)
        self.h_spacing_slider.valueChanged.connect(self.update_config)
        grid_layout.addWidget(self.h_spacing_slider, 1, 1)
        self.h_spacing_label = QLabel("2%")
        grid_layout.addWidget(self.h_spacing_label, 1, 2)
        
        grid_layout.addWidget(QLabel("垂直间距:"), 2, 0)
        self.v_spacing_slider = QSlider(Qt.Horizontal)
        self.v_spacing_slider.setRange(0, 10)
        self.v_spacing_slider.setValue(3)
        self.v_spacing_slider.valueChanged.connect(self.update_config)
        grid_layout.addWidget(self.v_spacing_slider, 2, 1)
        self.v_spacing_label = QLabel("3%")
        grid_layout.addWidget(self.v_spacing_label, 2, 2)
        
        grid_group.setLayout(grid_layout)
        layout.addWidget(grid_group)
        
        # 结界项尺寸
        size_group = QGroupBox("结界项尺寸")
        size_layout = QGridLayout()
        
        size_layout.addWidget(QLabel("宽度:"), 0, 0)
        self.width_slider = QSlider(Qt.Horizontal)
        self.width_slider.setRange(10, 40)
        self.width_slider.setValue(22)
        self.width_slider.valueChanged.connect(self.update_config)
        size_layout.addWidget(self.width_slider, 0, 1)
        self.width_label = QLabel("22%")
        size_layout.addWidget(self.width_label, 0, 2)
        
        size_layout.addWidget(QLabel("高度:"), 1, 0)
        self.height_slider = QSlider(Qt.Horizontal)
        self.height_slider.setRange(10, 30)
        self.height_slider.setValue(15)
        self.height_slider.valueChanged.connect(self.update_config)
        size_layout.addWidget(self.height_slider, 1, 1)
        self.height_label = QLabel("15%")
        size_layout.addWidget(self.height_label, 1, 2)
        
        size_group.setLayout(size_layout)
        layout.addWidget(size_group)
        
        group.setLayout(layout)
        return group
        
    def create_button_group(self):
        """创建按钮组"""
        group = QGroupBox("操作")
        layout = QVBoxLayout()
        
        save_btn = QPushButton("💾 保存配置")
        save_btn.setStyleSheet("background-color: #27ae60; color: white; font-weight: bold; padding: 10px;")
        save_btn.clicked.connect(self.save_config)
        layout.addWidget(save_btn)
        
        reset_btn = QPushButton("🔄 重置默认")
        reset_btn.clicked.connect(self.reset_config)
        layout.addWidget(reset_btn)
        
        group.setLayout(layout)
        return group
        
    def get_default_config(self):
        """获取默认配置"""
        return {
            "grid_area": {
                "left": 0.12,
                "top": 0.20,
                "right": 0.88,
                "bottom": 0.80
            },
            "layout": {
                "rows": 3,
                "columns": 3,
                "item_spacing": {
                    "horizontal": 0.02,
                    "vertical": 0.03
                }
            },
            "item_size": {
                "width": 0.22,
                "height": 0.15
            }
        }
        
    def connect_game_window(self):
        """连接游戏窗口"""
        try:
            game_windows = self.find_game_windows()
            
            if not game_windows:
                QMessageBox.warning(self, "警告", "未找到游戏窗口！\n请确保阴阳师游戏正在运行。")
                return
                
            self.game_window = game_windows[0]
            
            self.status_label.setText(f"✅ 已连接: {self.game_window['title']}")
            self.status_label.setStyleSheet("color: #27ae60; font-weight: bold; padding: 5px;")
            
            self.start_overlay_btn.setEnabled(True)
            self.connect_btn.setText("🔄 重新连接")
            
            QMessageBox.information(self, "连接成功", 
                f"已连接游戏窗口！\n\n"
                f"窗口: {self.game_window['title']}\n"
                f"大小: {self.game_window['width']}×{self.game_window['height']}\n\n"
                f"现在可以开始覆盖，在游戏界面上显示网格！")
                
        except Exception as e:
            QMessageBox.critical(self, "连接失败", f"连接游戏窗口失败: {e}")
            
    def find_game_windows(self):
        """查找游戏窗口"""
        windows = []
        game_titles = ["阴阳师", "Onmyoji", "网易"]
        
        def enum_callback(hwnd, windows_list):
            if win32gui.IsWindowVisible(hwnd):
                title = win32gui.GetWindowText(hwnd)
                if title and any(game_title in title for game_title in game_titles):
                    rect = win32gui.GetWindowRect(hwnd)
                    windows_list.append({
                        'hwnd': hwnd,
                        'title': title,
                        'rect': rect,
                        'width': rect[2] - rect[0],
                        'height': rect[3] - rect[1]
                    })
            return True
            
        win32gui.EnumWindows(enum_callback, windows)
        return windows
        
    def start_overlay(self):
        """开始覆盖"""
        if not self.game_window:
            QMessageBox.warning(self, "警告", "请先连接游戏窗口！")
            return
            
        try:
            self.is_overlaying = True
            self.overlay_timer.start(200)  # 200ms刷新一次
            
            self.start_overlay_btn.setEnabled(False)
            self.stop_overlay_btn.setEnabled(True)
            self.overlay_status.setText("✅ 覆盖层正在运行")
            self.overlay_status.setStyleSheet("color: #27ae60; padding: 5px;")
            
            QMessageBox.information(self, "覆盖启动", 
                "覆盖层已启动！\n\n"
                "现在您可以在游戏界面上看到网格覆盖层！\n"
                "拖拽滑块可以实时调整网格位置！\n\n"
                "注意：覆盖层会定期刷新显示。")
                
        except Exception as e:
            QMessageBox.critical(self, "启动失败", f"启动覆盖层失败: {e}")
            
    def stop_overlay(self):
        """停止覆盖"""
        try:
            self.is_overlaying = False
            self.overlay_timer.stop()
            
            self.start_overlay_btn.setEnabled(True)
            self.stop_overlay_btn.setEnabled(False)
            self.overlay_status.setText("覆盖层已停止")
            self.overlay_status.setStyleSheet("color: #7f8c8d; padding: 5px;")
            
        except Exception as e:
            QMessageBox.critical(self, "停止失败", f"停止覆盖层失败: {e}")
            
    def draw_overlay(self):
        """绘制覆盖层"""
        if not self.is_overlaying or not self.game_window:
            return
            
        try:
            # 使用mss截图
            with mss.mss() as sct:
                # 获取游戏窗口区域
                rect = self.game_window['rect']
                monitor = {
                    "top": rect[1],
                    "left": rect[0], 
                    "width": rect[2] - rect[0],
                    "height": rect[3] - rect[1]
                }
                
                # 截图
                screenshot = sct.grab(monitor)
                img = np.array(screenshot)
                img = cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)
                
                # 绘制网格覆盖层
                self.draw_grid_on_image(img)
                
                # 显示覆盖后的图像
                self.show_overlay_image(img)
                
        except Exception as e:
            print(f"绘制覆盖层失败: {e}")
            
    def draw_grid_on_image(self, img):
        """在图像上绘制网格"""
        height, width = img.shape[:2]
        
        # 计算网格区域
        grid_area = self.config["grid_area"]
        grid_left = int(grid_area["left"] * width)
        grid_top = int(grid_area["top"] * height)
        grid_right = int(grid_area["right"] * width)
        grid_bottom = int(grid_area["bottom"] * height)
        
        # 绘制网格区域边框（黄色）
        cv2.rectangle(img, (grid_left, grid_top), (grid_right, grid_bottom), (0, 255, 255), 3)
        
        # 绘制结界项
        layout = self.config["layout"]
        item_size = self.config["item_size"]
        
        item_width = int(item_size["width"] * width)
        item_height = int(item_size["height"] * height)
        spacing_h = int(layout["item_spacing"]["horizontal"] * width)
        spacing_v = int(layout["item_spacing"]["vertical"] * height)
        
        for row in range(layout["rows"]):
            for col in range(layout["columns"]):
                x = grid_left + col * (item_width + spacing_h)
                y = grid_top + row * (item_height + spacing_v)
                
                # 绘制结界项边框（红色）
                cv2.rectangle(img, (x, y), (x + item_width, y + item_height), (0, 0, 255), 2)
                
                # 绘制中心点（绿色）
                center_x = x + item_width // 2
                center_y = y + item_height // 2
                cv2.circle(img, (center_x, center_y), 5, (0, 255, 0), -1)
                
                # 绘制索引
                index = row * layout["columns"] + col
                cv2.putText(img, str(index), (x + 10, y + 25), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
        
        # 绘制说明文字
        info_lines = [
            "Grid Overlay - Real-time Adjustment",
            "Yellow: Grid Area | Red: Realm Items | Green: Centers"
        ]
        
        for i, line in enumerate(info_lines):
            cv2.putText(img, line, (20, 30 + i * 25), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            
    def show_overlay_image(self, img):
        """显示覆盖图像（这里可以选择不同的显示方式）"""
        # 方案1: 保存为临时文件并用系统默认程序打开
        # cv2.imwrite("temp_overlay.png", img)
        
        # 方案2: 使用OpenCV显示（会弹出新窗口）
        cv2.imshow("Game Overlay", img)
        cv2.waitKey(1)
        
    def update_config(self):
        """更新配置"""
        self.config = {
            "grid_area": {
                "left": self.left_slider.value() / 100.0,
                "top": self.top_slider.value() / 100.0,
                "right": self.right_slider.value() / 100.0,
                "bottom": self.bottom_slider.value() / 100.0
            },
            "layout": {
                "rows": self.rows_spin.value(),
                "columns": self.columns_spin.value(),
                "item_spacing": {
                    "horizontal": self.h_spacing_slider.value() / 100.0,
                    "vertical": self.v_spacing_slider.value() / 100.0
                }
            },
            "item_size": {
                "width": self.width_slider.value() / 100.0,
                "height": self.height_slider.value() / 100.0
            }
        }
        
        # 更新标签
        self.left_label.setText(f"{self.left_slider.value()}%")
        self.top_label.setText(f"{self.top_slider.value()}%")
        self.right_label.setText(f"{self.right_slider.value()}%")
        self.bottom_label.setText(f"{self.bottom_slider.value()}%")
        self.h_spacing_label.setText(f"{self.h_spacing_slider.value()}%")
        self.v_spacing_label.setText(f"{self.v_spacing_slider.value()}%")
        self.width_label.setText(f"{self.width_slider.value()}%")
        self.height_label.setText(f"{self.height_slider.value()}%")
        
    def save_config(self):
        """保存配置"""
        try:
            with open("custom_grid_config.json", 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
                
            QMessageBox.information(self, "保存成功", 
                "网格配置已保存成功！\n\n"
                "文件: custom_grid_config.json\n"
                "重启程序后自动生效。")
        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"保存配置失败: {e}")
            
    def reset_config(self):
        """重置配置"""
        self.config = self.get_default_config()
        self.load_config_to_ui()
        self.update_config()
        
    def load_config_to_ui(self):
        """加载配置到界面"""
        config = self.config
        
        self.left_slider.setValue(int(config["grid_area"]["left"] * 100))
        self.top_slider.setValue(int(config["grid_area"]["top"] * 100))
        self.right_slider.setValue(int(config["grid_area"]["right"] * 100))
        self.bottom_slider.setValue(int(config["grid_area"]["bottom"] * 100))
        
        self.rows_spin.setValue(config["layout"]["rows"])
        self.columns_spin.setValue(config["layout"]["columns"])
        self.h_spacing_slider.setValue(int(config["layout"]["item_spacing"]["horizontal"] * 100))
        self.v_spacing_slider.setValue(int(config["layout"]["item_spacing"]["vertical"] * 100))
        
        self.width_slider.setValue(int(config["item_size"]["width"] * 100))
        self.height_slider.setValue(int(config["item_size"]["height"] * 100))
        
    def closeEvent(self, event):
        """关闭事件"""
        if self.is_overlaying:
            self.stop_overlay()
        cv2.destroyAllWindows()
        event.accept()

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    controller = SimpleGameOverlay()
    controller.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
