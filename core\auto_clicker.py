import time
import random
import win32api
import win32con
import win32gui
import logging
from typing import Tuple, List, Optional

class AutoClicker:
    """自动点击器类
    
    提供模拟鼠标点击和人类操作功能
    """
    
    def __init__(self, hwnd=None):
        """初始化点击器
        
        Args:
            hwnd: 窗口句柄，如果为None则点击绝对坐标
        """
        self.hwnd = hwnd
        self.logger = logging.getLogger('阴阳师辅助工具.点击')
        self.client_offset = (0, 0)  # 客户区偏移量
        
    def set_hwnd(self, hwnd):
        """设置窗口句柄
        
        Args:
            hwnd: 窗口句柄
            
        Returns:
            bool: 是否设置成功
        """
        if hwnd and win32gui.IsWindow(hwnd):
            self.hwnd = hwnd
            self.update_client_offset()
            return True
        return False
    
    def update_client_offset(self):
        """更新客户区相对于窗口的偏移量"""
        if not self.hwnd:
            self.logger.warning("未设置窗口句柄，无法获取客户区偏移")
            self.client_offset = (0, 0)
            return
        
        try:
            # 获取窗口矩形
            window_rect = win32gui.GetWindowRect(self.hwnd)
            window_left, window_top, _, _ = window_rect
            
            # 获取客户区左上角在屏幕上的坐标
            client_left, client_top = win32gui.ClientToScreen(self.hwnd, (0, 0))
            
            # 计算偏移
            offset_x = client_left - window_left
            offset_y = client_top - window_top
            
            self.client_offset = (offset_x, offset_y)
            self.logger.debug(f"客户区偏移: [{offset_x},{offset_y}]")
        except Exception as e:
            self.logger.error(f"获取客户区偏移失败: {str(e)}")
            self.client_offset = (0, 0)
        
    def click(self, x: int, y: int, delay: float = 0.1) -> bool:
        """执行鼠标点击
        
        Args:
            x: X坐标
            y: Y坐标
            delay: 点击后延迟时间(秒)
            
        Returns:
            bool: 是否成功点击
        """
        try:
            # 如果指定了窗口句柄，执行窗口点击
            if self.hwnd:
                # 确保窗口在前台
                win32gui.SetForegroundWindow(self.hwnd)
                time.sleep(0.1)  # 等待窗口激活
                
                # 获取窗口客户区在屏幕上的位置 - 直接使用ClientToScreen
                screen_x, screen_y = win32gui.ClientToScreen(self.hwnd, (x, y))
                
                self.logger.debug(f"窗口客户区点击: 客户区坐标[{x},{y}] -> 屏幕坐标[{screen_x},{screen_y}]")
            else:
                # 直接使用屏幕坐标
                screen_x = x
                screen_y = y
                self.logger.debug(f"屏幕直接点击坐标: [{screen_x}, {screen_y}]")
            
            # 移动鼠标到目标位置
            win32api.SetCursorPos((screen_x, screen_y))
            
            # 执行鼠标点击
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)
            time.sleep(0.05)  # 短暂停顿模拟按下
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)
            
            # 延迟
            if delay > 0:
                time.sleep(delay)
                
            return True
        except Exception as e:
            self.logger.error(f"点击失败: {str(e)}")
            return False
            
    def right_click(self, x: int, y: int, delay: float = 0.1) -> bool:
        """执行鼠标右键点击
        
        Args:
            x: X坐标
            y: Y坐标
            delay: 点击后延迟时间(秒)
            
        Returns:
            bool: 是否成功点击
        """
        try:
            # 如果指定了窗口句柄，执行窗口点击
            if self.hwnd:
                # 确保窗口在前台
                win32gui.SetForegroundWindow(self.hwnd)
                time.sleep(0.1)  # 等待窗口激活
                
                # 获取窗口客户区在屏幕上的位置 - 直接使用ClientToScreen
                screen_x, screen_y = win32gui.ClientToScreen(self.hwnd, (x, y))
                
                self.logger.debug(f"窗口客户区右键点击: 客户区坐标[{x},{y}] -> 屏幕坐标[{screen_x},{screen_y}]")
            else:
                # 直接使用屏幕坐标
                screen_x = x
                screen_y = y
                self.logger.debug(f"屏幕直接右键点击坐标: [{screen_x}, {screen_y}]")
            
            # 移动鼠标到目标位置
            win32api.SetCursorPos((screen_x, screen_y))
            
            # 执行鼠标右键点击
            win32api.mouse_event(win32con.MOUSEEVENTF_RIGHTDOWN, 0, 0, 0, 0)
            time.sleep(0.05)  # 短暂停顿模拟按下
            win32api.mouse_event(win32con.MOUSEEVENTF_RIGHTUP, 0, 0, 0, 0)
            
            # 延迟
            if delay > 0:
                time.sleep(delay)
                
            return True
        except Exception as e:
            self.logger.error(f"右键点击失败: {str(e)}")
            return False
            
    def double_click(self, x: int, y: int, delay: float = 0.1) -> bool:
        """执行鼠标双击
        
        Args:
            x: X坐标
            y: Y坐标
            delay: 点击后延迟时间(秒)
            
        Returns:
            bool: 是否成功双击
        """
        try:
            # 第一次点击
            result1 = self.click(x, y, 0.05)
            
            # 第二次点击
            result2 = self.click(x, y, 0)
            
            # 延迟
            if delay > 0:
                time.sleep(delay)
                
            return result1 and result2
        except Exception as e:
            self.logger.error(f"双击失败: {str(e)}")
            return False
            
    def click_with_offset(self, x: int, y: int, offset_range: int = 5, delay: float = 0.1) -> bool:
        """带随机偏移的点击，模拟人类行为
        
        Args:
            x: 目标X坐标
            y: 目标Y坐标
            offset_range: 偏移范围(像素)
            delay: 点击后延迟时间(秒)
            
        Returns:
            bool: 是否成功点击
        """
        # 计算随机偏移
        offset_x = random.randint(-offset_range, offset_range)
        offset_y = random.randint(-offset_range, offset_range)
        
        # 应用偏移
        target_x = x + offset_x
        target_y = y + offset_y
        
        self.logger.debug(f"带偏移点击: 原始[{x},{y}] -> 偏移[{target_x},{target_y}]")
        
        # 执行点击
        return self.click(target_x, target_y, delay)
        
    def drag(self, start_x: int, start_y: int, end_x: int, end_y: int, 
             duration: float = 0.5, delay: float = 0.1) -> bool:
        """执行鼠标拖拽
        
        Args:
            start_x: 起始X坐标
            start_y: 起始Y坐标
            end_x: 结束X坐标
            end_y: 结束Y坐标
            duration: 拖拽持续时间(秒)
            delay: 拖拽后延迟时间(秒)
            
        Returns:
            bool: 是否成功拖拽
        """
        try:
            # 如果指定了窗口句柄，执行窗口内拖拽
            if self.hwnd:
                # 确保窗口在前台
                win32gui.SetForegroundWindow(self.hwnd)
                time.sleep(0.1)  # 等待窗口激活
                
                # 获取窗口客户区在屏幕上的位置 - 直接使用ClientToScreen
                screen_start_x, screen_start_y = win32gui.ClientToScreen(self.hwnd, (start_x, start_y))
                screen_end_x, screen_end_y = win32gui.ClientToScreen(self.hwnd, (end_x, end_y))
                
                self.logger.debug(f"窗口客户区拖拽: 从 客户区[{start_x},{start_y}]->屏幕[{screen_start_x},{screen_start_y}] 到 客户区[{end_x},{end_y}]->屏幕[{screen_end_x},{screen_end_y}]")
            else:
                # 直接使用屏幕坐标
                screen_start_x = start_x
                screen_start_y = start_y
                screen_end_x = end_x
                screen_end_y = end_y
                self.logger.debug(f"屏幕拖拽: 从[{screen_start_x},{screen_start_y}] 到 [{screen_end_x},{screen_end_y}]")
            
            # 移动到起始位置
            win32api.SetCursorPos((screen_start_x, screen_start_y))
            
            # 模拟按下鼠标左键
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)
            
            # 计算每一步的移动
            steps = max(int(duration * 20), 10)  # 至少10步
            dx = (screen_end_x - screen_start_x) / steps
            dy = (screen_end_y - screen_start_y) / steps
            interval = duration / steps
            
            # 逐步移动鼠标
            current_x, current_y = screen_start_x, screen_start_y
            for _ in range(steps):
                current_x += dx
                current_y += dy
                win32api.SetCursorPos((int(current_x), int(current_y)))
                time.sleep(interval)
            
            # 确保到达目标位置
            win32api.SetCursorPos((screen_end_x, screen_end_y))
            
            # 释放鼠标左键
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)
            
            # 延迟
            if delay > 0:
                time.sleep(delay)
                
            return True
        except Exception as e:
            self.logger.error(f"拖拽失败: {str(e)}")
            # 确保释放鼠标
            try:
                win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)
            except:
                pass
            return False
            
    def drag_path(self, points: List[Tuple[int, int]], duration: float = 0.5, 
                 delay: float = 0.1) -> bool:
        """按照路径拖拽鼠标
        
        Args:
            points: 路径点列表，每个点为(x, y)坐标
            duration: 拖拽持续时间(秒)
            delay: 拖拽后延迟时间(秒)
            
        Returns:
            bool: 是否成功拖拽
        """
        if not points or len(points) < 2:
            self.logger.error("路径点数量不足")
            return False
            
        try:
            # 如果指定了窗口句柄，执行窗口内拖拽
            if self.hwnd:
                # 确保窗口在前台
                win32gui.SetForegroundWindow(self.hwnd)
                time.sleep(0.1)  # 等待窗口激活
                
                # 转换为屏幕坐标 - 直接使用ClientToScreen
                screen_points = [win32gui.ClientToScreen(self.hwnd, (x, y)) for x, y in points]
                
                self.logger.debug(f"窗口客户区路径拖拽: {len(screen_points)}个点")
            else:
                # 直接使用屏幕坐标
                screen_points = points
                self.logger.debug(f"屏幕路径拖拽: {len(screen_points)}个点")
                
            # 移动到起始位置
            start_x, start_y = screen_points[0]
            win32api.SetCursorPos((start_x, start_y))
            
            # 模拟按下鼠标左键
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)
            
            # 计算每个点之间的时间间隔
            step_duration = duration / (len(screen_points) - 1)
            
            # 沿路径移动鼠标
            for point in screen_points[1:]:
                x, y = point
                win32api.SetCursorPos((x, y))
                time.sleep(step_duration)
            
            # 释放鼠标左键
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)
            
            # 延迟
            if delay > 0:
                time.sleep(delay)
                
            return True
        except Exception as e:
            self.logger.error(f"路径拖拽失败: {str(e)}")
            # 确保释放鼠标
            try:
                win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)
            except:
                pass
            return False 