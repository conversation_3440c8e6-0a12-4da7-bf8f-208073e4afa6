#!/usr/bin/env python3
"""游戏界面网格映射配置工具"""

import sys
import cv2
import numpy as np
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                             QWidget, QPushButton, QLabel, QSlider, QSpinBox,
                             QDoubleSpinBox, QGroupBox, QGridLayout, QMessageBox,
                             QCheckBox, QFrame)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QPixmap, QPainter, QPen, QBrush, QColor, QFont
import json
import os
import time
import logging
from core.window_manager import WindowManager
from core.image_utils import ImageUtils

class GameOverlayConfig(QMainWindow):
    """游戏界面网格映射配置工具"""
    
    config_changed = pyqtSignal(dict)
    
    def __init__(self):
        super().__init__()
        self.window_manager = WindowManager()
        self.image_utils = ImageUtils()
        self.game_window = None
        self.current_screenshot = None
        self.config = self.get_default_config()
        self.setup_ui()
        self.setup_timer()
    
    def setup_ui(self):
        """设置界面"""
        self.setWindowTitle("游戏界面网格映射配置")
        self.setGeometry(100, 100, 1200, 800)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout()
        
        # 左侧控制面板
        control_panel = self.create_control_panel()
        main_layout.addWidget(control_panel, 1)
        
        # 右侧游戏界面显示
        game_display = self.create_game_display()
        main_layout.addWidget(game_display, 3)
        
        central_widget.setLayout(main_layout)
    
    def create_control_panel(self):
        """创建控制面板"""
        panel = QWidget()
        panel.setFixedWidth(350)
        layout = QVBoxLayout()
        
        # 标题
        title = QLabel("网格配置控制面板")
        title.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50; margin: 10px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 游戏窗口选择
        window_group = self.create_window_selection_group()
        layout.addWidget(window_group)
        
        # 网格区域配置
        grid_area_group = self.create_grid_area_controls()
        layout.addWidget(grid_area_group)
        
        # 网格布局配置
        grid_layout_group = self.create_grid_layout_controls()
        layout.addWidget(grid_layout_group)
        
        # 结界项尺寸配置
        item_size_group = self.create_item_size_controls()
        layout.addWidget(item_size_group)
        
        # 显示选项
        display_group = self.create_display_options()
        layout.addWidget(display_group)
        
        # 操作按钮
        button_group = self.create_action_buttons()
        layout.addWidget(button_group)
        
        layout.addStretch()
        panel.setLayout(layout)
        return panel
    
    def create_window_selection_group(self):
        """创建窗口选择组"""
        group = QGroupBox("游戏窗口")
        layout = QVBoxLayout()
        
        # 刷新窗口按钮
        refresh_btn = QPushButton("🔄 刷新游戏窗口")
        refresh_btn.clicked.connect(self.refresh_game_windows)
        layout.addWidget(refresh_btn)
        
        # 窗口状态显示
        self.window_status = QLabel("未选择游戏窗口")
        self.window_status.setStyleSheet("color: #e74c3c; font-weight: bold;")
        layout.addWidget(self.window_status)
        
        # 自动刷新选项
        self.auto_refresh_cb = QCheckBox("自动刷新截图 (1秒)")
        self.auto_refresh_cb.setChecked(True)
        self.auto_refresh_cb.stateChanged.connect(self.toggle_auto_refresh)
        layout.addWidget(self.auto_refresh_cb)
        
        group.setLayout(layout)
        return group
    
    def create_grid_area_controls(self):
        """创建网格区域控制组"""
        group = QGroupBox("网格区域 (拖拽滑块调整)")
        layout = QGridLayout()
        
        # 左边界
        layout.addWidget(QLabel("左边界:"), 0, 0)
        self.left_slider = QSlider(Qt.Horizontal)
        self.left_slider.setRange(0, 100)
        self.left_slider.setValue(12)
        self.left_slider.valueChanged.connect(self.update_config)
        layout.addWidget(self.left_slider, 0, 1)
        self.left_label = QLabel("12%")
        layout.addWidget(self.left_label, 0, 2)
        
        # 上边界
        layout.addWidget(QLabel("上边界:"), 1, 0)
        self.top_slider = QSlider(Qt.Horizontal)
        self.top_slider.setRange(0, 100)
        self.top_slider.setValue(20)
        self.top_slider.valueChanged.connect(self.update_config)
        layout.addWidget(self.top_slider, 1, 1)
        self.top_label = QLabel("20%")
        layout.addWidget(self.top_label, 1, 2)
        
        # 右边界
        layout.addWidget(QLabel("右边界:"), 2, 0)
        self.right_slider = QSlider(Qt.Horizontal)
        self.right_slider.setRange(0, 100)
        self.right_slider.setValue(88)
        self.right_slider.valueChanged.connect(self.update_config)
        layout.addWidget(self.right_slider, 2, 1)
        self.right_label = QLabel("88%")
        layout.addWidget(self.right_label, 2, 2)
        
        # 下边界
        layout.addWidget(QLabel("下边界:"), 3, 0)
        self.bottom_slider = QSlider(Qt.Horizontal)
        self.bottom_slider.setRange(0, 100)
        self.bottom_slider.setValue(80)
        self.bottom_slider.valueChanged.connect(self.update_config)
        layout.addWidget(self.bottom_slider, 3, 1)
        self.bottom_label = QLabel("80%")
        layout.addWidget(self.bottom_label, 3, 2)
        
        group.setLayout(layout)
        return group
    
    def create_grid_layout_controls(self):
        """创建网格布局控制组"""
        group = QGroupBox("网格布局")
        layout = QGridLayout()
        
        # 行数
        layout.addWidget(QLabel("行数:"), 0, 0)
        self.rows_spin = QSpinBox()
        self.rows_spin.setRange(1, 5)
        self.rows_spin.setValue(3)
        self.rows_spin.valueChanged.connect(self.update_config)
        layout.addWidget(self.rows_spin, 0, 1)
        
        # 列数
        layout.addWidget(QLabel("列数:"), 0, 2)
        self.columns_spin = QSpinBox()
        self.columns_spin.setRange(1, 5)
        self.columns_spin.setValue(3)
        self.columns_spin.valueChanged.connect(self.update_config)
        layout.addWidget(self.columns_spin, 0, 3)
        
        # 水平间距
        layout.addWidget(QLabel("水平间距:"), 1, 0)
        self.h_spacing_slider = QSlider(Qt.Horizontal)
        self.h_spacing_slider.setRange(0, 10)
        self.h_spacing_slider.setValue(2)
        self.h_spacing_slider.valueChanged.connect(self.update_config)
        layout.addWidget(self.h_spacing_slider, 1, 1)
        self.h_spacing_label = QLabel("2%")
        layout.addWidget(self.h_spacing_label, 1, 2)
        
        # 垂直间距
        layout.addWidget(QLabel("垂直间距:"), 2, 0)
        self.v_spacing_slider = QSlider(Qt.Horizontal)
        self.v_spacing_slider.setRange(0, 10)
        self.v_spacing_slider.setValue(3)
        self.v_spacing_slider.valueChanged.connect(self.update_config)
        layout.addWidget(self.v_spacing_slider, 2, 1)
        self.v_spacing_label = QLabel("3%")
        layout.addWidget(self.v_spacing_label, 2, 2)
        
        group.setLayout(layout)
        return group
    
    def create_item_size_controls(self):
        """创建结界项尺寸控制组"""
        group = QGroupBox("结界项尺寸")
        layout = QGridLayout()
        
        # 宽度
        layout.addWidget(QLabel("宽度:"), 0, 0)
        self.width_slider = QSlider(Qt.Horizontal)
        self.width_slider.setRange(5, 50)
        self.width_slider.setValue(22)
        self.width_slider.valueChanged.connect(self.update_config)
        layout.addWidget(self.width_slider, 0, 1)
        self.width_label = QLabel("22%")
        layout.addWidget(self.width_label, 0, 2)
        
        # 高度
        layout.addWidget(QLabel("高度:"), 1, 0)
        self.height_slider = QSlider(Qt.Horizontal)
        self.height_slider.setRange(5, 50)
        self.height_slider.setValue(15)
        self.height_slider.valueChanged.connect(self.update_config)
        layout.addWidget(self.height_slider, 1, 1)
        self.height_label = QLabel("15%")
        layout.addWidget(self.height_label, 1, 2)
        
        group.setLayout(layout)
        return group
    
    def create_display_options(self):
        """创建显示选项组"""
        group = QGroupBox("显示选项")
        layout = QVBoxLayout()
        
        # 显示网格边框
        self.show_grid_border_cb = QCheckBox("显示网格区域边框")
        self.show_grid_border_cb.setChecked(True)
        self.show_grid_border_cb.stateChanged.connect(self.update_display)
        layout.addWidget(self.show_grid_border_cb)
        
        # 显示结界项
        self.show_items_cb = QCheckBox("显示结界项")
        self.show_items_cb.setChecked(True)
        self.show_items_cb.stateChanged.connect(self.update_display)
        layout.addWidget(self.show_items_cb)
        
        # 显示索引
        self.show_index_cb = QCheckBox("显示索引编号")
        self.show_index_cb.setChecked(True)
        self.show_index_cb.stateChanged.connect(self.update_display)
        layout.addWidget(self.show_index_cb)
        
        # 显示中心点
        self.show_center_cb = QCheckBox("显示中心点")
        self.show_center_cb.setChecked(True)
        self.show_center_cb.stateChanged.connect(self.update_display)
        layout.addWidget(self.show_center_cb)
        
        group.setLayout(layout)
        return group
    
    def create_action_buttons(self):
        """创建操作按钮组"""
        group = QGroupBox("操作")
        layout = QVBoxLayout()
        
        # 保存配置
        save_btn = QPushButton("💾 保存配置")
        save_btn.setStyleSheet("QPushButton { background-color: #27ae60; color: white; font-weight: bold; padding: 8px; }")
        save_btn.clicked.connect(self.save_config)
        layout.addWidget(save_btn)
        
        # 重置默认
        reset_btn = QPushButton("🔄 重置默认")
        reset_btn.clicked.connect(self.reset_to_default)
        layout.addWidget(reset_btn)
        
        # 测试点击
        test_btn = QPushButton("🎯 测试点击")
        test_btn.setStyleSheet("QPushButton { background-color: #3498db; color: white; font-weight: bold; padding: 8px; }")
        test_btn.clicked.connect(self.test_click_positions)
        layout.addWidget(test_btn)
        
        group.setLayout(layout)
        return group
    
    def create_game_display(self):
        """创建游戏界面显示区域"""
        frame = QFrame()
        frame.setStyleSheet("border: 2px solid #34495e; background-color: #2c3e50;")
        layout = QVBoxLayout()
        
        # 标题
        title = QLabel("游戏界面实时预览")
        title.setStyleSheet("color: white; font-size: 14px; font-weight: bold; padding: 10px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 游戏截图显示
        self.game_display_label = QLabel()
        self.game_display_label.setAlignment(Qt.AlignCenter)
        self.game_display_label.setStyleSheet("background-color: #34495e; border: 1px solid #7f8c8d;")
        self.game_display_label.setMinimumSize(800, 600)
        layout.addWidget(self.game_display_label)
        
        # 状态信息
        self.display_status = QLabel("等待游戏窗口...")
        self.display_status.setStyleSheet("color: #bdc3c7; padding: 10px;")
        self.display_status.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.display_status)
        
        frame.setLayout(layout)
        return frame
    
    def setup_timer(self):
        """设置定时器"""
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.update_game_display)
        self.refresh_timer.start(1000)  # 1秒刷新一次
    
    def get_default_config(self):
        """获取默认配置"""
        return {
            "grid_area": {
                "left": 0.12,
                "top": 0.20,
                "right": 0.88,
                "bottom": 0.80
            },
            "layout": {
                "rows": 3,
                "columns": 3,
                "item_spacing": {
                    "horizontal": 0.02,
                    "vertical": 0.03
                }
            },
            "item_size": {
                "width": 0.22,
                "height": 0.15
            }
        }
    
    def refresh_game_windows(self):
        """刷新游戏窗口"""
        try:
            windows = self.window_manager.find_game_windows()
            if windows:
                self.game_window = windows[0]  # 选择第一个找到的窗口
                self.window_status.setText(f"✅ 已连接: {self.game_window['title']}")
                self.window_status.setStyleSheet("color: #27ae60; font-weight: bold;")
                self.update_game_display()
            else:
                self.game_window = None
                self.window_status.setText("❌ 未找到游戏窗口")
                self.window_status.setStyleSheet("color: #e74c3c; font-weight: bold;")
        except Exception as e:
            QMessageBox.warning(self, "错误", f"刷新游戏窗口失败: {e}")
    
    def toggle_auto_refresh(self, state):
        """切换自动刷新"""
        if state == Qt.Checked:
            self.refresh_timer.start(1000)
        else:
            self.refresh_timer.stop()
    
    def update_config(self):
        """更新配置"""
        self.config = {
            "grid_area": {
                "left": self.left_slider.value() / 100.0,
                "top": self.top_slider.value() / 100.0,
                "right": self.right_slider.value() / 100.0,
                "bottom": self.bottom_slider.value() / 100.0
            },
            "layout": {
                "rows": self.rows_spin.value(),
                "columns": self.columns_spin.value(),
                "item_spacing": {
                    "horizontal": self.h_spacing_slider.value() / 100.0,
                    "vertical": self.v_spacing_slider.value() / 100.0
                }
            },
            "item_size": {
                "width": self.width_slider.value() / 100.0,
                "height": self.height_slider.value() / 100.0
            }
        }
        
        # 更新标签显示
        self.left_label.setText(f"{self.left_slider.value()}%")
        self.top_label.setText(f"{self.top_slider.value()}%")
        self.right_label.setText(f"{self.right_slider.value()}%")
        self.bottom_label.setText(f"{self.bottom_slider.value()}%")
        self.h_spacing_label.setText(f"{self.h_spacing_slider.value()}%")
        self.v_spacing_label.setText(f"{self.v_spacing_slider.value()}%")
        self.width_label.setText(f"{self.width_slider.value()}%")
        self.height_label.setText(f"{self.height_slider.value()}%")
        
        # 发送配置变更信号
        self.config_changed.emit(self.config)
        
        # 更新显示
        self.update_display()
    
    def update_display(self):
        """更新显示"""
        if self.current_screenshot is not None:
            self.draw_grid_overlay()
    
    def update_game_display(self):
        """更新游戏界面显示"""
        if not self.game_window:
            return

        try:
            # 截取游戏窗口
            screenshot = self.capture_window_screenshot(self.game_window['hwnd'])
            if screenshot is not None:
                self.current_screenshot = screenshot
                self.draw_grid_overlay()
                self.display_status.setText(f"✅ 实时显示 - {screenshot.shape[1]}×{screenshot.shape[0]}")
            else:
                self.display_status.setText("❌ 截图失败")
        except Exception as e:
            self.display_status.setText(f"❌ 更新失败: {e}")

    def capture_window_screenshot(self, hwnd):
        """截取窗口截图"""
        try:
            import win32gui
            import win32ui
            import win32con
            from PIL import Image

            # 获取窗口位置和大小
            left, top, right, bottom = win32gui.GetWindowRect(hwnd)
            width = right - left
            height = bottom - top

            # 创建设备上下文
            hwndDC = win32gui.GetWindowDC(hwnd)
            mfcDC = win32ui.CreateDCFromHandle(hwndDC)
            saveDC = mfcDC.CreateCompatibleDC()

            # 创建位图
            saveBitMap = win32ui.CreateBitmap()
            saveBitMap.CreateCompatibleBitmap(mfcDC, width, height)
            saveDC.SelectObject(saveBitMap)

            # 截图
            result = saveDC.BitBlt((0, 0), (width, height), mfcDC, (0, 0), win32con.SRCCOPY)

            if result:
                # 转换为numpy数组
                bmpinfo = saveBitMap.GetInfo()
                bmpstr = saveBitMap.GetBitmapBits(True)

                # 转换为PIL图像
                img = Image.frombuffer('RGB', (bmpinfo['bmWidth'], bmpinfo['bmHeight']), bmpstr, 'raw', 'BGRX', 0, 1)

                # 转换为numpy数组
                screenshot = np.array(img)
                screenshot = cv2.cvtColor(screenshot, cv2.COLOR_RGB2BGR)

                # 清理资源
                win32gui.DeleteObject(saveBitMap.GetHandle())
                saveDC.DeleteDC()
                mfcDC.DeleteDC()
                win32gui.ReleaseDC(hwnd, hwndDC)

                return screenshot
            else:
                # 清理资源
                win32gui.DeleteObject(saveBitMap.GetHandle())
                saveDC.DeleteDC()
                mfcDC.DeleteDC()
                win32gui.ReleaseDC(hwnd, hwndDC)
                return None

        except Exception as e:
            logging.error(f"截图失败: {e}")
            return None
    
    def draw_grid_overlay(self):
        """绘制网格覆盖层"""
        if self.current_screenshot is None:
            return
        
        # 复制截图
        overlay_image = self.current_screenshot.copy()
        height, width = overlay_image.shape[:2]
        
        # 计算网格区域
        grid_area = self.config["grid_area"]
        grid_left = int(grid_area["left"] * width)
        grid_top = int(grid_area["top"] * height)
        grid_right = int(grid_area["right"] * width)
        grid_bottom = int(grid_area["bottom"] * height)
        
        # 绘制网格区域边框
        if self.show_grid_border_cb.isChecked():
            cv2.rectangle(overlay_image, (grid_left, grid_top), (grid_right, grid_bottom), (0, 255, 255), 3)
        
        # 绘制结界项
        if self.show_items_cb.isChecked():
            layout = self.config["layout"]
            item_size = self.config["item_size"]
            
            item_width = int(item_size["width"] * width)
            item_height = int(item_size["height"] * height)
            spacing_h = int(layout["item_spacing"]["horizontal"] * width)
            spacing_v = int(layout["item_spacing"]["vertical"] * height)
            
            for row in range(layout["rows"]):
                for col in range(layout["columns"]):
                    x = grid_left + col * (item_width + spacing_h)
                    y = grid_top + row * (item_height + spacing_v)
                    
                    # 绘制结界项矩形
                    cv2.rectangle(overlay_image, (x, y), (x + item_width, y + item_height), (0, 0, 255), 2)
                    
                    # 绘制中心点
                    if self.show_center_cb.isChecked():
                        center_x = x + item_width // 2
                        center_y = y + item_height // 2
                        cv2.circle(overlay_image, (center_x, center_y), 5, (255, 0, 0), -1)
                    
                    # 绘制索引
                    if self.show_index_cb.isChecked():
                        index = row * layout["columns"] + col
                        text_x = x + 10
                        text_y = y + 25
                        cv2.putText(overlay_image, str(index), (text_x, text_y), 
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
        
        # 转换为QPixmap并显示
        self.display_opencv_image(overlay_image)
    
    def display_opencv_image(self, image):
        """显示OpenCV图像"""
        # 转换BGR到RGB
        rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # 缩放图像以适应显示区域
        display_width = self.game_display_label.width() - 20
        display_height = self.game_display_label.height() - 20
        
        if display_width > 0 and display_height > 0:
            h, w = rgb_image.shape[:2]
            scale = min(display_width / w, display_height / h)
            new_w = int(w * scale)
            new_h = int(h * scale)
            
            resized_image = cv2.resize(rgb_image, (new_w, new_h))
            
            # 转换为QPixmap
            from PyQt5.QtGui import QImage
            q_image = QImage(resized_image.data, new_w, new_h, new_w * 3, QImage.Format_RGB888)
            pixmap = QPixmap.fromImage(q_image)
            
            self.game_display_label.setPixmap(pixmap)
    
    def save_config(self):
        """保存配置"""
        try:
            # 保存到JSON文件
            with open("custom_grid_config.json", 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            
            QMessageBox.information(self, "保存成功", "网格配置已保存到 custom_grid_config.json")
        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"保存配置失败: {e}")
    
    def reset_to_default(self):
        """重置为默认配置"""
        self.config = self.get_default_config()
        self.load_config_to_ui()
        self.update_display()
    
    def load_config_to_ui(self):
        """加载配置到界面"""
        config = self.config
        
        # 网格区域
        self.left_slider.setValue(int(config["grid_area"]["left"] * 100))
        self.top_slider.setValue(int(config["grid_area"]["top"] * 100))
        self.right_slider.setValue(int(config["grid_area"]["right"] * 100))
        self.bottom_slider.setValue(int(config["grid_area"]["bottom"] * 100))
        
        # 网格布局
        self.rows_spin.setValue(config["layout"]["rows"])
        self.columns_spin.setValue(config["layout"]["columns"])
        self.h_spacing_slider.setValue(int(config["layout"]["item_spacing"]["horizontal"] * 100))
        self.v_spacing_slider.setValue(int(config["layout"]["item_spacing"]["vertical"] * 100))
        
        # 结界项尺寸
        self.width_slider.setValue(int(config["item_size"]["width"] * 100))
        self.height_slider.setValue(int(config["item_size"]["height"] * 100))
    
    def test_click_positions(self):
        """测试点击位置"""
        if not self.game_window:
            QMessageBox.warning(self, "警告", "请先连接游戏窗口")
            return
        
        QMessageBox.information(self, "测试点击", 
            "将在游戏窗口上依次点击所有网格位置\n每个位置间隔1秒\n请观察点击位置是否准确")
        
        # 这里可以添加实际的点击测试逻辑
        # 暂时只显示消息
        QMessageBox.information(self, "测试完成", "点击位置测试完成")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    window = GameOverlayConfig()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
