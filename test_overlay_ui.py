#!/usr/bin/env python3
"""测试覆盖工具界面"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from features.realm_raid.true_game_overlay import TrueGameOverlayController

def main():
    """主函数"""
    print("🎯 启动覆盖工具界面测试...")
    
    app = QApplication(sys.argv)
    
    # 创建覆盖工具（不传递父窗口，测试独立运行）
    overlay = TrueGameOverlayController()
    overlay.show()
    
    print("✅ 覆盖工具界面已启动！")
    print("🔍 请检查：")
    print("   1. 界面是否完全不透明")
    print("   2. 所有文字是否显示完整")
    print("   3. 按钮和标签是否正常显示")
    print("   4. 窗口大小是否合适")
    print("   5. 样式是否美观")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
