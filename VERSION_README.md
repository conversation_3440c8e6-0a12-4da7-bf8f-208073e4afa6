# 项目版本说明

## 当前版本结构

### 新版本 (v2) - 推荐使用 ✅
- **主程序**: `main_gui_v2.py`
- **架构**: 基于 `core/main_window.py` 的统一主窗口
- **探索GUI**: `features/explore/explore_gui_simple.py`
- **结界突破GUI**: `features/realm_raid/realm_raid_gui_simple.py`
- **特点**: 简洁界面、统一管理、更好的用户体验

### 旧版本 (v1) - 已移至 legacy_v1/ 📦
- **主程序**: `legacy_v1/main_gui.py`
- **探索GUI**: `legacy_v1/features/explore/explore_gui.py`
- **结界突破GUI**: `legacy_v1/features/realm_raid/realm_raid_gui.py`
- **特点**: 功能完整但界面复杂

### 命令行版本 💻
- **主程序**: `main.py`
- **用途**: 命令行操作，适合自动化脚本

## 使用建议

1. **日常使用**: 运行 `main_gui_v2.py` ⭐
2. **命令行**: 运行 `main.py`
3. **旧版本**: 如需使用旧版本，运行 `legacy_v1/main_gui.py`

## 文件说明

### 核心文件 (两个版本共用)
- `core/` - 核心功能模块
- `features/*/explore_bot.py` - 探索机器人
- `features/*/explore_config.py` - 配置文件
- `templates/` - 模板图像
- `config.json` - 全局配置

### 版本特定文件
- **v2版本使用**: `*_simple.py` 文件 (简洁版)
- **v1版本使用**: 完整功能的 `*.py` 文件 (复杂版)

## 版本分离完成 ✅

### 已移动到 legacy_v1/
- ✅ `main_gui.py` → `legacy_v1/main_gui.py`
- ✅ `features/explore/explore_gui.py` → `legacy_v1/features/explore/explore_gui.py`
- ✅ `features/realm_raid/realm_raid_gui.py` → `legacy_v1/features/realm_raid/realm_raid_gui.py`

### 已清理的文件
- ✅ 测试脚本文件
- ✅ 调试文件
- ✅ 临时文件

### 当前目录保留
- ✅ `main_gui_v2.py` (推荐使用)
- ✅ `main.py` (命令行版本)
- ✅ `core/` (核心模块)
- ✅ `features/*/explore_gui_simple.py` (新版GUI)
- ✅ `templates/` (模板文件)
- ✅ `config.json` (配置文件)

## 重要提醒 ⚠️

1. **推荐使用新版本**: `main_gui_v2.py` 有更好的用户体验
2. **旧版本备份**: 如果需要旧版本功能，可以在 `legacy_v1/` 中找到
3. **配置兼容**: 新旧版本共享相同的配置文件和模板
4. **功能完整**: 新版本包含所有核心功能，界面更简洁

## 版本历史

- **v1**: 原始版本，功能完整但界面复杂
- **v2**: 重构版本，简化界面，统一管理，更好的用户体验
- **当前**: 使用v2版本，v1版本作为备份保留
