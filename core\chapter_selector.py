#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
章节选择器
统一管理章节识别和选择逻辑，支持OCR和图像模板两种方式
"""

import logging
import cv2
import numpy as np
import os
from typing import Optional, Tuple, List
from core.ocr_utils import ocr_utils
from core.image_utils import ImageUtils

class ChapterSelector:
    """章节选择器类"""
    
    def __init__(self, base_bot=None):
        """
        初始化章节选择器
        
        Args:
            base_bot: BaseBot实例，用于截图和点击操作
        """
        self.base_bot = base_bot
        self.logger = logging.getLogger('ChapterSelector')
        self.target_chapter = 28  # 默认目标章节
        
        # 回退的图像模板路径
        self.chapter_templates = {
            28: 'templates/explore/28.png'
        }
    
    def set_target_chapter(self, chapter: int):
        """
        设置目标章节
        
        Args:
            chapter: 目标章节号
        """
        self.target_chapter = chapter
        self.logger.info(f"设置目标章节为: {chapter}")
    
    def get_target_chapter(self) -> int:
        """获取当前目标章节"""
        return self.target_chapter
    
    def add_chapter_template(self, chapter: int, template_path: str):
        """
        添加章节图像模板（用于回退）
        
        Args:
            chapter: 章节号
            template_path: 模板图像路径
        """
        self.chapter_templates[chapter] = template_path
        self.logger.debug(f"添加章节 {chapter} 模板: {template_path}")
    






    def scroll_and_find_chapter(self, screenshot: np.ndarray = None, max_attempts: int = 5) -> Optional[Tuple[int, int]]:
        """
        滚动查找目标章节（带数字顺序逻辑）

        Args:
            screenshot: 屏幕截图，如果为None则自动截图
            max_attempts: 最大滚动尝试次数

        Returns:
            (x, y) 点击坐标，如果未找到则返回None
        """
        if not ocr_utils.is_available():
            self.logger.warning("OCR不可用，无法进行滚动查找")
            return None

        self.logger.info(f"🔄 开始滚动查找目标章节: 第{self.target_chapter}章")

        # 滚动前先点击yao模板，确保章节列表处于正确状态
        if not self.click_yao_template_before_scroll():
            self.logger.warning("⚠️ 点击yao模板失败，继续尝试滚动查找")
        else:
            self.logger.info("✅ 成功点击yao模板，准备开始滚动")

        # 获取初始截图
        if screenshot is None:
            if self.base_bot:
                screenshot = self.base_bot.take_screenshot()
            else:
                self.logger.error("无法获取截图，base_bot为None")
                return None

        # 定义滚动区域（章节列表区域）
        scroll_region = self.get_chapter_scroll_region(screenshot)
        if not scroll_region:
            self.logger.error("无法确定滚动区域")
            return None

        scroll_x, scroll_y, scroll_w, scroll_h = scroll_region
        scroll_center_x = scroll_x + scroll_w // 2
        scroll_center_y = scroll_y + scroll_h // 2

        # 记录已识别的章节，用于判断滚动方向
        all_found_chapters = set()
        last_chapters = set()

        for attempt in range(max_attempts):
            self.logger.info(f"🔄 滚动尝试 {attempt + 1}/{max_attempts}")

            # 尝试在当前区域查找目标章节
            position = self.find_chapter_with_ocr_fallback(screenshot)
            if position:
                self.logger.info(f"✅ 滚动查找成功，找到目标章节 {self.target_chapter}")
                return position

            # 识别当前可见的所有章节
            current_chapters = self.get_visible_chapters(screenshot)
            self.logger.info(f"📋 当前可见章节: {sorted(current_chapters)}")

            # 更新所有已发现的章节
            all_found_chapters.update(current_chapters)

            # 如果没有识别到任何章节，随机滚动
            if not current_chapters:
                self.logger.warning("未识别到任何章节，尝试向下滚动")
                self.scroll_down(scroll_center_x, scroll_center_y)
            else:
                # 根据数字顺序逻辑决定滚动方向
                scroll_direction = self.determine_scroll_direction(current_chapters, self.target_chapter)

                if scroll_direction == "down":
                    self.logger.info(f"📉 目标章节 {self.target_chapter} 在当前区域下方，向下滚动")
                    self.scroll_down(scroll_center_x, scroll_center_y)
                elif scroll_direction == "up":
                    self.logger.info(f"📈 目标章节 {self.target_chapter} 在当前区域上方，向上滚动")
                    self.scroll_up(scroll_center_x, scroll_center_y)
                else:
                    self.logger.warning("无法确定滚动方向，尝试向下滚动")
                    self.scroll_down(scroll_center_x, scroll_center_y)

            # 检查是否已经滚动到底部或顶部（章节列表没有变化）
            if current_chapters == last_chapters and current_chapters:
                self.logger.warning("章节列表未发生变化，可能已到达边界")
                # 尝试反方向滚动一次
                if attempt < max_attempts - 1:
                    self.logger.info("🔄 尝试反方向滚动")
                    if scroll_direction == "down":
                        self.scroll_up(scroll_center_x, scroll_center_y)
                    else:
                        self.scroll_down(scroll_center_x, scroll_center_y)

            last_chapters = current_chapters.copy()

            # 等待滚动完成并获取新截图
            import time
            time.sleep(0.5)  # 等待滚动动画完成

            if self.base_bot:
                screenshot = self.base_bot.take_screenshot()
            else:
                break

        self.logger.warning(f"❌ 滚动查找失败，已尝试 {max_attempts} 次")
        self.logger.info(f"📊 总共发现的章节: {sorted(all_found_chapters)}")
        return None

    def get_chapter_scroll_region(self, screenshot: np.ndarray) -> Optional[Tuple[int, int, int, int]]:
        """
        获取章节列表的滚动区域

        Args:
            screenshot: 屏幕截图

        Returns:
            (x, y, width, height) 滚动区域坐标，如果无法确定则返回None
        """
        height, width = screenshot.shape[:2]

        # 指定的滚动区域: (1065,490,1269,889) 宽高(204,399)
        scroll_x = 1065
        scroll_y = 490
        scroll_w = 204
        scroll_h = 399

        # 确保坐标在屏幕范围内
        scroll_x = max(0, min(scroll_x, width - scroll_w))
        scroll_y = max(0, min(scroll_y, height - scroll_h))
        scroll_w = min(scroll_w, width - scroll_x)
        scroll_h = min(scroll_h, height - scroll_y)

        self.logger.debug(f"📍 滚动区域: ({scroll_x}, {scroll_y}, {scroll_w}, {scroll_h})")
        return (scroll_x, scroll_y, scroll_w, scroll_h)

    def get_visible_chapters(self, screenshot: np.ndarray) -> set:
        """
        获取当前可见的所有章节号

        Args:
            screenshot: 屏幕截图

        Returns:
            当前可见的章节号集合
        """
        try:
            # 获取章节区域（OCR识别区域，与滚动区域不同）
            height, width = screenshot.shape[:2]
            target_x1, target_y1 = 1047, 128
            target_x2, target_y2 = 1257, 657

            # 确保坐标在屏幕范围内
            target_x1 = max(0, min(target_x1, width))
            target_y1 = max(0, min(target_y1, height))
            target_x2 = max(0, min(target_x2, width))
            target_y2 = max(0, min(target_y2, height))

            # 提取章节区域
            target_region = screenshot[target_y1:target_y2, target_x1:target_x2]

            # 使用增强OCR识别所有章节
            chapters_info = ocr_utils.recognize_enhanced(target_region, None)

            # 提取章节号
            visible_chapters = set()
            for chapter_num, bbox in chapters_info:
                if 1 <= chapter_num <= 50:  # 合理的章节范围
                    visible_chapters.add(chapter_num)

            return visible_chapters

        except Exception as e:
            self.logger.error(f"获取可见章节失败: {e}")
            return set()

    def determine_scroll_direction(self, current_chapters: set, target_chapter: int) -> str:
        """
        根据数字顺序逻辑确定滚动方向

        Args:
            current_chapters: 当前可见的章节号集合
            target_chapter: 目标章节号

        Returns:
            "up", "down", 或 "unknown"
        """
        if not current_chapters:
            return "unknown"

        min_chapter = min(current_chapters)
        max_chapter = max(current_chapters)

        self.logger.debug(f"🔍 当前章节范围: {min_chapter}-{max_chapter}, 目标: {target_chapter}")

        if target_chapter < min_chapter:
            # 目标章节在当前可见范围上方
            return "up"
        elif target_chapter > max_chapter:
            # 目标章节在当前可见范围下方
            return "down"
        else:
            # 目标章节应该在当前可见范围内，但没找到
            # 可能是OCR识别问题，尝试小幅滚动
            return "down"

    def scroll_down(self, x: int, y: int, scroll_amount: int = 3):
        """
        向下滚动

        Args:
            x: 滚动位置x坐标
            y: 滚动位置y坐标
            scroll_amount: 滚动量
        """
        if self.base_bot:
            self.logger.debug(f"📉 向下滚动 {scroll_amount} 次，位置: ({x}, {y})")
            for _ in range(scroll_amount):
                self.base_bot.scroll(x, y, -1)  # 负值向下滚动
                import time
                time.sleep(0.1)  # 短暂延迟

    def scroll_up(self, x: int, y: int, scroll_amount: int = 3):
        """
        向上滚动

        Args:
            x: 滚动位置x坐标
            y: 滚动位置y坐标
            scroll_amount: 滚动量
        """
        if self.base_bot:
            self.logger.debug(f"📈 向上滚动 {scroll_amount} 次，位置: ({x}, {y})")
            for _ in range(scroll_amount):
                self.base_bot.scroll(x, y, 1)  # 正值向上滚动
                import time
                time.sleep(0.1)  # 短暂延迟

    def click_yao_template_before_scroll(self) -> bool:
        """
        在滚动前点击yao模板图像，确保章节列表处于正确状态

        Returns:
            bool: 是否成功点击yao模板
        """
        if not self.base_bot:
            self.logger.error("base_bot为None，无法点击yao模板")
            return False

        try:
            self.logger.info("🎯 查找并点击yao模板...")

            # 方法1: 使用BaseBot的模板查找功能
            if hasattr(self.base_bot, 'find_template'):
                position = self.base_bot.find_template('yao')
                if position:
                    self.logger.info(f"✅ 找到yao模板，位置: {position}")
                    # 点击模板
                    click_success = self.base_bot.click(position[0], position[1])
                    if click_success:
                        self.logger.info("✅ 成功点击yao模板")
                        import time
                        time.sleep(0.5)  # 等待界面响应
                        return True
                    else:
                        self.logger.warning("⚠️ 点击yao模板失败")
                        return False

            # 方法2: 使用模板管理器查找
            if hasattr(self.base_bot, 'template_manager'):
                template_manager = self.base_bot.template_manager
                if hasattr(template_manager, 'find_template'):
                    screenshot = self.base_bot.take_screenshot()
                    if screenshot is not None:
                        position = template_manager.find_template(screenshot, 'yao')
                        if position:
                            self.logger.info(f"✅ 模板管理器找到yao模板，位置: {position}")
                            click_success = self.base_bot.click(position[0], position[1])
                            if click_success:
                                self.logger.info("✅ 成功点击yao模板")
                                import time
                                time.sleep(0.5)  # 等待界面响应
                                return True
                            else:
                                self.logger.warning("⚠️ 点击yao模板失败")
                                return False

            # 方法3: 直接使用模板文件路径
            yao_template_path = "templates/yao.png"  # 假设模板文件路径
            if os.path.exists(yao_template_path):
                screenshot = self.base_bot.take_screenshot()
                if screenshot is not None:
                    template_img = cv2.imread(yao_template_path)
                    if template_img is not None:
                        # 使用OpenCV模板匹配
                        result = cv2.matchTemplate(screenshot, template_img, cv2.TM_CCOEFF_NORMED)
                        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)

                        if max_val > 0.7:  # 匹配阈值
                            # 计算点击位置（模板中心）
                            h, w = template_img.shape[:2]
                            click_x = max_loc[0] + w // 2
                            click_y = max_loc[1] + h // 2

                            self.logger.info(f"✅ OpenCV找到yao模板，位置: ({click_x}, {click_y}), 匹配度: {max_val:.3f}")
                            click_success = self.base_bot.click(click_x, click_y)
                            if click_success:
                                self.logger.info("✅ 成功点击yao模板")
                                import time
                                time.sleep(0.5)  # 等待界面响应
                                return True
                            else:
                                self.logger.warning("⚠️ 点击yao模板失败")
                                return False
                        else:
                            self.logger.warning(f"⚠️ yao模板匹配度过低: {max_val:.3f}")
                    else:
                        self.logger.error(f"❌ 无法加载yao模板图像: {yao_template_path}")
            else:
                self.logger.warning(f"⚠️ yao模板文件不存在: {yao_template_path}")

            self.logger.warning("❌ 所有方法都无法找到yao模板")
            return False

        except Exception as e:
            self.logger.error(f"❌ 点击yao模板时出错: {str(e)}")
            return False

    def find_chapter_with_ocr_fallback(self, screenshot: np.ndarray = None) -> Optional[Tuple[int, int]]:
        """
        OCR识别章节（作为备选方案，成功率较低）

        Args:
            screenshot: 屏幕截图，如果为None则自动截图

        Returns:
            (x, y) 点击坐标，如果未找到则返回None
        """
        if not ocr_utils.is_available():
            self.logger.warning("OCR不可用，无法使用OCR识别章节")
            return None

        # 获取截图
        if screenshot is None:
            if self.base_bot:
                screenshot = self.base_bot.take_screenshot()
            else:
                self.logger.error("无法获取截图，base_bot为None")
                return None

        try:
            # 获取屏幕尺寸
            height, width = screenshot.shape[:2]

            # 使用指定的精确区域进行OCR识别
            # 区域坐标: (1047,128,1257,657) 宽高(210,529) - 原来的正确区域
            target_x1, target_y1 = 1047, 128
            target_x2, target_y2 = 1257, 657

            # 确保坐标在屏幕范围内
            target_x1 = max(0, min(target_x1, width))
            target_y1 = max(0, min(target_y1, height))
            target_x2 = max(0, min(target_x2, width))
            target_y2 = max(0, min(target_y2, height))

            # 提取指定区域
            target_region = screenshot[target_y1:target_y2, target_x1:target_x2]
            region_width = target_x2 - target_x1
            region_height = target_y2 - target_y1

            self.logger.info(f"OCR备选方案：在指定区域进行识别，区域坐标: ({target_x1},{target_y1},{target_x2},{target_y2}), 宽高: ({region_width},{region_height})")

            # 保存运行时调试截图
            try:
                import os
                import time
                debug_dir = "debug_screenshots"
                if not os.path.exists(debug_dir):
                    os.makedirs(debug_dir)

                timestamp = int(time.time())
                debug_path = os.path.join(debug_dir, f"runtime_ocr_{timestamp}_{target_x1}_{target_y1}_{region_width}x{region_height}.png")
                cv2.imwrite(debug_path, target_region)
                self.logger.info(f"🖼️ 已保存运行时OCR截图: {debug_path}")

                # 立即测试这个区域的OCR识别
                self.logger.info(f"🧪 测试运行时截图的OCR识别...")
                test_results = ocr_utils.reader.readtext(target_region, detail=1)
                self.logger.info(f"🧪 运行时OCR结果: {len(test_results)} 个文本")
                for i, (bbox, text, confidence) in enumerate(test_results):
                    self.logger.info(f"🧪   {i+1}. '{text}' (置信度: {confidence:.3f})")
                    if '章' in text or '第' in text or '笫' in text:
                        self.logger.info(f"🧪   ⭐ 可能的章节文本!")

            except Exception as e:
                self.logger.warning(f"保存运行时调试截图失败: {e}")

            # 使用增强OCR识别（多引擎+多预处理）
            self.logger.info(f"🚀 启动增强OCR识别，目标章节: 第{self.target_chapter}章")
            chapters_info = ocr_utils.recognize_enhanced(target_region, self.target_chapter)

            # 查找目标章节并调整坐标
            for chapter_num, bbox in chapters_info:
                if chapter_num == self.target_chapter:
                    x1, y1, x2, y2 = bbox
                    # 调整坐标到全屏坐标系
                    click_x = target_x1 + (x1 + x2) // 2
                    click_y = target_y1 + (y1 + y2) // 2
                    self.logger.info(f"OCR备选方案找到目标章节 {self.target_chapter}，点击坐标: ({click_x}, {click_y})")
                    return (click_x, click_y)

            self.logger.warning("OCR备选方案未识别到目标章节")
            return None

        except Exception as e:
            self.logger.error(f"OCR备选方案识别章节时出错: {str(e)}")
            return None
    
    def find_chapter_with_template(self, screenshot: np.ndarray = None) -> Optional[Tuple[int, int]]:
        """
        使用图像模板匹配查找目标章节
        
        Args:
            screenshot: 屏幕截图，如果为None则自动截图
            
        Returns:
            (x, y) 点击坐标，如果未找到则返回None
        """
        if self.target_chapter not in self.chapter_templates:
            self.logger.warning(f"没有章节 {self.target_chapter} 的图像模板")
            return None
        
        template_path = self.chapter_templates[self.target_chapter]
        
        # 检查模板文件是否存在
        if not os.path.exists(template_path):
            self.logger.error(f"章节模板文件不存在: {template_path}")
            return None
        
        # 获取截图
        if screenshot is None:
            if self.base_bot:
                screenshot = self.base_bot.take_screenshot()
            else:
                self.logger.error("无法获取截图，base_bot为None")
                return None
        
        try:
            # 使用BaseBot的模板匹配功能
            if self.base_bot and hasattr(self.base_bot, 'find_template'):
                # 如果BaseBot已加载该模板
                template_name = f'chapter_{self.target_chapter}'
                position = self.base_bot.find_template(template_name)
                if position:
                    self.logger.info(f"模板匹配找到章节 {self.target_chapter}，位置: {position}")
                    return position
            
            # 直接使用ImageUtils进行模板匹配
            template_img = cv2.imread(template_path)
            if template_img is None:
                self.logger.error(f"无法加载模板图像: {template_path}")
                return None
            
            position = ImageUtils.find_template(screenshot, template_img, threshold=0.7)
            if position:
                self.logger.info(f"模板匹配找到章节 {self.target_chapter}，位置: {position}")
                return position
            else:
                self.logger.warning(f"模板匹配未找到章节 {self.target_chapter}")
                return None
                
        except Exception as e:
            self.logger.error(f"模板匹配时出错: {str(e)}")
            return None
    
    def find_target_chapter(self, screenshot: np.ndarray = None, use_ocr_first: bool = True, max_attempts: int = 3) -> Optional[Tuple[int, int]]:
        """
        查找目标章节，优先使用OCR，失败时回退到图像模板，三次失败后进行滚动查找

        Args:
            screenshot: 屏幕截图，如果为None则自动截图
            use_ocr_first: 是否优先使用OCR
            max_attempts: 最大尝试次数，超过后进行滚动查找

        Returns:
            (x, y) 点击坐标，如果未找到则返回None
        """
        self.logger.info(f"开始查找目标章节 {self.target_chapter}")

        # 记录尝试次数
        attempt_count = 0

        for attempt in range(max_attempts):
            attempt_count += 1
            self.logger.info(f"🔍 第 {attempt_count} 次尝试查找章节 {self.target_chapter}")

            # 方法1: 使用OCR识别（原始图像+错误修正方案）
            if use_ocr_first and ocr_utils.is_available():
                self.logger.info("🚀 尝试OCR识别（原始图像+错误修正）")
                position = self.find_chapter_with_ocr_fallback(screenshot)
                if position:
                    self.logger.info(f"✅ 第 {attempt_count} 次尝试成功找到章节")
                    return position
                self.logger.debug("OCR识别失败，尝试模板匹配")
            else:
                self.logger.warning("OCR不可用，无法识别章节")

            # 方法2: 回退到图像模板匹配
            self.logger.debug("使用图像模板匹配查找章节")
            position = self.find_chapter_with_template(screenshot)
            if position:
                self.logger.info(f"✅ 第 {attempt_count} 次尝试通过模板匹配成功找到章节")
                return position

            self.logger.warning(f"❌ 第 {attempt_count} 次尝试失败")

            # 如果不是最后一次尝试，等待一下再重试
            if attempt < max_attempts - 1:
                import time
                time.sleep(0.5)
                # 重新获取截图
                if self.base_bot:
                    screenshot = self.base_bot.take_screenshot()

        # 三次常规尝试都失败后，启动滚动查找
        self.logger.warning(f"⚠️ 常规方法 {max_attempts} 次尝试都失败，启动滚动查找")

        # 方法3: 滚动查找（带数字顺序逻辑）
        if ocr_utils.is_available():
            self.logger.info("🔄 启动滚动查找模式")
            position = self.scroll_and_find_chapter(screenshot, max_attempts=5)
            if position:
                self.logger.info("✅ 滚动查找成功找到目标章节")
                return position
            else:
                self.logger.error("❌ 滚动查找也失败了")
        else:
            self.logger.error("OCR不可用，无法进行滚动查找")

        self.logger.error(f"❌ 所有方法都未能找到章节 {self.target_chapter}")
        return None

    def find_target_chapter_no_scroll(self, screenshot: np.ndarray = None, use_ocr_first: bool = True, max_attempts: int = 1) -> Optional[Tuple[int, int]]:
        """
        查找目标章节，但不进行滚动查找，只在当前界面查找

        Args:
            screenshot: 屏幕截图，如果为None则自动截图
            use_ocr_first: 是否优先使用OCR
            max_attempts: 最大尝试次数，默认1次

        Returns:
            (x, y) 点击坐标，如果未找到则返回None
        """
        self.logger.info(f"开始在当前界面查找目标章节 {self.target_chapter}（不滚动）")

        for attempt in range(max_attempts):
            self.logger.info(f"🔍 第 {attempt + 1} 次尝试在当前界面查找章节 {self.target_chapter}")

            # 方法1: 使用OCR识别
            if use_ocr_first and ocr_utils.is_available():
                self.logger.info("🚀 尝试OCR识别（当前界面）")
                position = self.find_chapter_with_ocr_fallback(screenshot)
                if position:
                    self.logger.info(f"✅ OCR在当前界面找到章节 {self.target_chapter}")
                    return position
                self.logger.debug("OCR识别失败，尝试模板匹配")
            else:
                self.logger.warning("OCR不可用，无法识别章节")

            # 方法2: 图像模板匹配
            self.logger.debug("使用图像模板匹配查找章节（当前界面）")
            position = self.find_chapter_with_template(screenshot)
            if position:
                self.logger.info(f"✅ 模板匹配在当前界面找到章节 {self.target_chapter}")
                return position

            self.logger.warning(f"❌ 第 {attempt + 1} 次尝试在当前界面未找到章节")

            # 如果不是最后一次尝试，等待一下再重试
            if attempt < max_attempts - 1:
                import time
                time.sleep(0.5)
                # 重新获取截图
                if self.base_bot:
                    screenshot = self.base_bot.take_screenshot()

        self.logger.info(f"❌ 当前界面未找到章节 {self.target_chapter}，需要进入章节选择界面")
        return None

    def click_target_chapter(self, screenshot: np.ndarray = None, use_ocr_first: bool = True) -> bool:
        """
        查找并点击目标章节
        
        Args:
            screenshot: 屏幕截图，如果为None则自动截图
            use_ocr_first: 是否优先使用OCR
            
        Returns:
            是否成功点击
        """
        if not self.base_bot:
            self.logger.error("无法点击，base_bot为None")
            return False
        
        # 查找目标章节
        position = self.find_target_chapter(screenshot, use_ocr_first)
        if not position:
            return False
        
        # 点击章节
        x, y = position
        try:
            self.base_bot.click(x, y)
            self.logger.info(f"成功点击章节 {self.target_chapter}，坐标: ({x}, {y})")
            return True
        except Exception as e:
            self.logger.error(f"点击章节时出错: {str(e)}")
            return False
    
    def get_available_chapters(self, screenshot: np.ndarray = None) -> List[int]:
        """
        获取当前界面所有可用的章节号
        
        Args:
            screenshot: 屏幕截图，如果为None则自动截图
            
        Returns:
            章节号列表
        """
        if not ocr_utils.is_available():
            # 如果OCR不可用，返回已配置的模板章节
            return list(self.chapter_templates.keys())
        
        # 获取截图
        if screenshot is None:
            if self.base_bot:
                screenshot = self.base_bot.take_screenshot()
            else:
                self.logger.error("无法获取截图")
                return []
        
        try:
            # 使用OCR识别所有章节
            chapters_info = ocr_utils.recognize_chapter_numbers(screenshot)
            chapters = [info[0] for info in chapters_info]
            
            self.logger.info(f"检测到可用章节: {chapters}")
            return sorted(chapters)
            
        except Exception as e:
            self.logger.error(f"获取可用章节时出错: {str(e)}")
            return []