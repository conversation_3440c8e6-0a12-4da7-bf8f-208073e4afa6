import cv2
import numpy as np
import os
import logging
from typing import Tuple, Dict, Optional, List, Any, Union
import concurrent.futures

class AdaptiveTemplateManager:
    """自适应模板管理器
    
    用于处理不同分辨率下的模板匹配问题，通过动态调整模板尺寸和计算相对坐标
    解决在不同设备和分辨率下的图像识别问题。
    """
    
    def __init__(self, base_resolution: Tuple[int, int] = (1920, 1080)):
        """初始化自适应模板管理器
        
        Args:
            base_resolution: 基准分辨率，默认为1920x1080
        """
        self.base_resolution = base_resolution
        self.templates = {}  # 模板字典
        self.rel_click_points = {}  # 相对点击位置字典
        self.logger = logging.getLogger('阴阳师辅助工具.模板系统')
        self.logger.info(f"初始化自适应模板管理器 - 基准分辨率: {base_resolution[0]}x{base_resolution[1]}")
    
    def load_template(self, template_name: str, template_path: str, rel_click_point: Tuple[float, float] = (0.5, 0.5)) -> bool:
        """加载模板图像
        
        Args:
            template_name: 模板名称
            template_path: 模板文件路径
            rel_click_point: 相对点击位置（范围0.0-1.0），默认为中心点(0.5, 0.5)
            
        Returns:
            bool: 是否加载成功
        """
        try:
            if not os.path.exists(template_path):
                self.logger.error(f"模板文件不存在: {template_path}")
                return False
                
            template = cv2.imread(template_path)
            if template is None:
                self.logger.error(f"无法读取模板文件: {template_path}")
                return False
                
            self.templates[template_name] = template
            self.rel_click_points[template_name] = rel_click_point
            
            self.logger.info(f"加载模板: {template_name}, 尺寸: {template.shape[1]}x{template.shape[0]}, 点击位置: {rel_click_point}")
            return True
        except Exception as e:
            self.logger.error(f"加载模板失败: {str(e)}")
            return False
    
    def resize_template(self, template: np.ndarray, target_resolution: Tuple[int, int]) -> np.ndarray:
        """根据目标分辨率调整模板大小
        
        Args:
            template: 原始模板图像
            target_resolution: 目标分辨率
            
        Returns:
            np.ndarray: 调整后的模板图像
        """
        # 计算缩放比例
        scale_x = target_resolution[0] / self.base_resolution[0]
        scale_y = target_resolution[1] / self.base_resolution[1]
        
        # 对于不同宽高比的屏幕，使用最小缩放比例以避免变形
        scale = min(scale_x, scale_y)
        
        # 计算调整后的尺寸
        new_width = int(template.shape[1] * scale)
        new_height = int(template.shape[0] * scale)
        
        self.logger.debug(f"调整模板尺寸: 原始[{template.shape[1]}x{template.shape[0]}] -> 新[{new_width}x{new_height}], 缩放比例: {scale:.3f}")
        
        # 调整模板大小
        if scale != 1.0:
            resized_template = cv2.resize(template, (new_width, new_height), interpolation=cv2.INTER_AREA)
            return resized_template
        return template
    
    def get_target_resolution(self, image: np.ndarray) -> Tuple[int, int]:
        """获取目标图像的分辨率
        
        Args:
            image: 目标图像
            
        Returns:
            tuple: 目标分辨率 (width, height)
        """
        height, width = image.shape[:2]
        return (width, height)
    
    def get_absolute_position(self, window_rect: Tuple[int, int, int, int], rel_pos: Tuple[float, float]) -> Tuple[int, int]:
        """将相对坐标转换为窗口内的绝对像素坐标
        
        Args:
            window_rect: 窗口矩形 (left, top, right, bottom)
            rel_pos: 相对位置 (x, y)，范围为0.0-1.0
            
        Returns:
            tuple: 绝对像素坐标 (x, y)
        """
        left, top, right, bottom = window_rect
        width = right - left
        height = bottom - top
        
        abs_x = left + int(width * rel_pos[0])
        abs_y = top + int(height * rel_pos[1])
        
        self.logger.debug(f"相对坐标转换: 相对[{rel_pos[0]:.2f},{rel_pos[1]:.2f}] -> 绝对[{abs_x},{abs_y}]")
        
        return (abs_x, abs_y)
    
    def get_relative_position(self, window_rect: Tuple[int, int, int, int], abs_pos: Tuple[int, int]) -> Tuple[float, float]:
        """将窗口内的绝对像素坐标转换为相对坐标
        
        Args:
            window_rect: 窗口矩形 (left, top, right, bottom)
            abs_pos: 绝对坐标 (x, y)
            
        Returns:
            tuple: 相对坐标 (x, y)，范围为0.0-1.0
        """
        left, top, right, bottom = window_rect
        width = right - left
        height = bottom - top
        
        # 防止除0错误
        if width == 0 or height == 0:
            self.logger.error("窗口尺寸错误，无法计算相对坐标")
            return (0.5, 0.5)
        
        rel_x = (abs_pos[0] - left) / width
        rel_y = (abs_pos[1] - top) / height
        
        self.logger.debug(f"绝对坐标转换: 绝对[{abs_pos[0]},{abs_pos[1]}] -> 相对[{rel_x:.2f},{rel_y:.2f}]")
        
        return (rel_x, rel_y)
    
    def find_template(self, image: np.ndarray, template_name: str, 
                    threshold: float = 0.8) -> Optional[Dict[str, Any]]:
        """在图像中查找模板
        
        Args:
            image: 目标图像
            template_name: 模板名称
            threshold: 匹配阈值，默认0.8
            
        Returns:
            dict: 匹配结果，包含位置、中心点和置信度，如果未找到则返回None
        """
        if template_name not in self.templates:
            self.logger.warning(f"未找到模板: {template_name}")
            return None
            
        # 获取目标分辨率
        target_resolution = self.get_target_resolution(image)
        
        # 调整模板大小
        template = self.templates[template_name]
        resized_template = self.resize_template(template, target_resolution)
        
        # 获取相对点击位置
        rel_click_point = self.rel_click_points.get(template_name, (0.5, 0.5))
        
        self.logger.debug(f"查找模板[{template_name}]: 目标尺寸[{target_resolution[0]}x{target_resolution[1]}], 模板尺寸[{resized_template.shape[1]}x{resized_template.shape[0]}]")
        
        try:
            # 模板匹配
            result = cv2.matchTemplate(image, resized_template, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            if max_val >= threshold:
                # 获取尺寸
                h, w = resized_template.shape[:2]
                top_left = max_loc
                
                # 使用相对点击位置计算点击坐标
                click_x = int(top_left[0] + w * rel_click_point[0])
                click_y = int(top_left[1] + h * rel_click_point[1])
                
                match_info = {
                    'position': top_left,
                    'center': (click_x, click_y),  # 使用相对点击位置而非简单中心点
                    'confidence': max_val,
                    'size': (w, h)
                }
                
                self.logger.debug(f"找到模板[{template_name}]: 位置[{top_left[0]},{top_left[1]}], 点击位置[{click_x},{click_y}], 匹配度[{max_val:.3f}]")
                return match_info
            else:
                self.logger.debug(f"未找到模板[{template_name}]: 最佳匹配度[{max_val:.3f}] < 阈值[{threshold}]")
                return None
        except Exception as e:
            self.logger.error(f"模板匹配失败[{template_name}]: {str(e)}")
            return None
    
    def find_all_templates(self, image: np.ndarray, template_name: str,
                         threshold: float = 0.8, max_results: int = 10,
                         nms_threshold: float = 0.3) -> List[Dict[str, Any]]:
        """查找图像中所有匹配的模板位置
        
        Args:
            image: 目标图像
            template_name: 模板名称
            threshold: 匹配阈值，默认0.8
            max_results: 最大结果数
            nms_threshold: 非极大值抑制阈值，避免重复检测
            
        Returns:
            list: 匹配结果列表，每个结果包含位置、中心点和置信度
        """
        if template_name not in self.templates:
            self.logger.warning(f"未找到模板: {template_name}")
            return []
            
        # 获取目标分辨率
        target_resolution = self.get_target_resolution(image)
        
        # 调整模板大小
        template = self.templates[template_name]
        resized_template = self.resize_template(template, target_resolution)
        
        # 获取相对点击位置
        rel_click_point = self.rel_click_points.get(template_name, (0.5, 0.5))
        
        self.logger.debug(f"查找所有模板[{template_name}]: 目标尺寸[{target_resolution[0]}x{target_resolution[1]}], 模板尺寸[{resized_template.shape[1]}x{resized_template.shape[0]}]")
        
        try:
            # 模板匹配
            h, w = resized_template.shape[:2]
            result = cv2.matchTemplate(image, resized_template, cv2.TM_CCOEFF_NORMED)
            
            # 查找所有匹配位置
            locations = np.where(result >= threshold)
            
            match_list = []
            for pt in zip(*locations[::-1]):  # 反转坐标，从(y,x)变为(x,y)
                # 计算点击位置
                click_x = int(pt[0] + w * rel_click_point[0])
                click_y = int(pt[1] + h * rel_click_point[1])
                
                match_info = {
                    'position': pt,
                    'center': (click_x, click_y),
                    'confidence': result[pt[1], pt[0]],
                    'size': (w, h)
                }
                match_list.append(match_info)
            
            # 按照置信度排序
            match_list = sorted(match_list, key=lambda x: x['confidence'], reverse=True)
            
            # 应用非极大值抑制 (NMS)
            result_list = []
            while match_list and len(result_list) < max_results:
                best_match = match_list.pop(0)
                result_list.append(best_match)
                
                # 移除重叠的匹配
                match_list = [
                    match for match in match_list
                    if self._calculate_iou(best_match, match) < nms_threshold
                ]
            
            self.logger.info(f"查找所有模板[{template_name}]: 找到{len(result_list)}个匹配")
            for i, match in enumerate(result_list):
                self.logger.debug(f"匹配[{i+1}]: 位置[{match['position'][0]},{match['position'][1]}], 点击位置[{match['center'][0]},{match['center'][1]}], 匹配度[{match['confidence']:.3f}]")
            
            return result_list
        except Exception as e:
            self.logger.error(f"查找所有模板匹配失败[{template_name}]: {str(e)}")
            return []
    
    def _calculate_iou(self, box1: Dict[str, Any], box2: Dict[str, Any]) -> float:
        """计算两个矩形框的交并比 (IOU)
        
        Args:
            box1: 第一个矩形框
            box2: 第二个矩形框
            
        Returns:
            float: 交并比, 范围为0.0-1.0
        """
        x1_1, y1_1 = box1['position']
        x2_1, y2_1 = x1_1 + box1['size'][0], y1_1 + box1['size'][1]
        
        x1_2, y1_2 = box2['position']
        x2_2, y2_2 = x1_2 + box2['size'][0], y1_2 + box2['size'][1]
        
        # 计算交集区域
        x1_i = max(x1_1, x1_2)
        y1_i = max(y1_1, y1_2)
        x2_i = min(x2_1, x2_2)
        y2_i = min(y2_1, y2_2)
        
        if x1_i >= x2_i or y1_i >= y2_i:
            return 0.0  # 没有交集
            
        # 计算交集面积
        intersection = (x2_i - x1_i) * (y2_i - y1_i)
        
        # 计算两个矩形的面积
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
        
        # 计算并集面积
        union = area1 + area2 - intersection
        
        return intersection / union 
    
    def find_multiple_templates(self, image: np.ndarray, template_names: List[str], 
                             threshold: float = 0.8) -> Dict[str, Dict[str, Any]]:
        """查找多个模板（串行方式）
        
        Args:
            image: 目标图像
            template_names: 模板名称列表
            threshold: 匹配阈值，默认0.8
            
        Returns:
            dict: {模板名称: 匹配结果} - 每个模板的匹配结果
        """
        results = {}
        
        for template_name in template_names:
            result = self.find_template(image, template_name, threshold)
            if result:
                results[template_name] = result
        
        return results
        
    def find_multiple_templates_parallel(self, image: np.ndarray, template_names: List[str], 
                                      threshold: float = 0.8, max_workers: int = None) -> Dict[str, Dict[str, Any]]:
        """并行查找多个模板
        
        Args:
            image: 目标图像
            template_names: 模板名称列表
            threshold: 匹配阈值，默认0.8
            max_workers: 最大工作线程数，默认为None(由系统决定)
            
        Returns:
            dict: {模板名称: 匹配结果} - 每个模板的匹配结果
        """
        results = {}
        
        def process_template(template_name):
            result = self.find_template(image, template_name, threshold)
            return template_name, result
        
        # 筛选可用的模板
        valid_templates = [name for name in template_names if name in self.templates]
        
        if not valid_templates:
            self.logger.warning("没有可用的模板进行并行查找")
            return {}
            
        # 获取目标分辨率（预先计算，避免多线程中重复计算）
        target_resolution = self.get_target_resolution(image)
        self.logger.info(f"并行查找模板: 目标分辨率[{target_resolution[0]}x{target_resolution[1]}], 模板数量[{len(valid_templates)}]")
        
        # 使用线程池并行处理
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_name = {
                executor.submit(process_template, name): name 
                for name in valid_templates
            }
            
            # 收集结果
            for future in concurrent.futures.as_completed(future_to_name):
                name, result = future.result()
                if result:
                    results[name] = result
        
        self.logger.info(f"并行查找完成: 查找{len(valid_templates)}个模板, 找到{len(results)}个")
        return results 