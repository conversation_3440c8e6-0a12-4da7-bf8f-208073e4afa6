# 章节选择功能使用说明

## 功能位置

在探索功能的用户界面中，您可以在**探索设置**组中找到新增的章节选择功能，包含以下控件：

### 1. 目标章节下拉框
- 位置：探索设置组顶部
- 功能：选择您想要探索的章节（1-30章）
- 默认：第28章
- 使用：点击下拉框，选择您想要的章节

### 2. 检测可用章节按钮
- 功能：自动检测当前游戏界面中所有可用的章节
- 使用：
  1. 先在游戏中进入章节选择界面
  2. 点击"检测可用章节"按钮
  3. 系统会在可用章节后添加"✓"标记

### 3. 优先使用OCR识别复选框
- 功能：启用文字识别技术自动识别章节号
- 默认：已勾选
- 说明：取消勾选将只使用图像模板匹配（目前只支持28章）

## 使用步骤

### 方法一：直接选择章节（推荐）
1. 在"目标章节"下拉框中选择您想要的章节
2. 确保"优先使用OCR识别"已勾选
3. 启动探索功能
4. 系统会自动识别并选择您设置的章节

### 方法二：先检测再选择
1. 在游戏中手动进入章节选择界面
2. 点击"检测可用章节"按钮
3. 查看日志中显示的可用章节
4. 在下拉框中选择有"✓"标记的章节
5. 启动探索功能

## 注意事项

### OCR识别要求
- 需要安装EasyOCR库：`pip install easyocr`
- 游戏界面需要清晰可见
- 章节数字需要清楚显示

### 回退机制
- 如果OCR识别失败，系统会自动回退到图像模板匹配
- 目前只有第28章有图像模板
- 建议为常用章节添加图像模板

### 故障排除

**OCR识别失败：**
- 检查是否正确安装了easyocr
- 确保游戏界面在章节选择页面
- 尝试取消勾选"优先使用OCR识别"

**章节选择无效：**
- 确保目标章节确实存在于当前界面
- 使用"检测可用章节"功能验证

**性能问题：**
- 首次使用OCR会下载模型文件，需要网络连接
- 后续使用速度会明显提升

## 日志信息

系统会在日志中显示以下信息：
- `目标章节已设置为: 第X章` - 章节设置成功
- `检测到可用章节: 第X章、第Y章...` - 检测结果
- `OCR找到目标章节 X，点击坐标: (x, y)` - OCR识别成功
- `模板匹配找到章节 X，位置: (x, y)` - 图像模板匹配成功

通过查看日志可以了解章节选择的详细过程和结果。