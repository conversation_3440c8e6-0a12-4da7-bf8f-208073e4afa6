import os
import time
import random
import math
import logging
import win32gui
import win32api
import win32con
import numpy as np
from typing import Tuple, List, Dict, Optional, Callable, Union

class AntiDetection:
    """防检测工具类
    
    提供各种防检测技术，包括：
    - 人类点击模拟
    - 随机坐标偏移
    - 随机延迟
    - 平滑鼠标移动
    - 贝塞尔曲线轨迹
    """
    
    def __init__(self, hwnd=None):
        """初始化防检测工具
        
        Args:
            hwnd: 窗口句柄，用于坐标转换
        """
        self.hwnd = hwnd
        self.window_rect = None
        
        # 防检测设置
        self.use_random_click = True       # 启用随机点击位置
        self.click_radius = 5              # 点击半径范围（像素）
        
        self.use_random_delay = True       # 启用随机延迟
        self.min_delay = 0.1               # 最小延迟时间（秒）
        self.max_delay = 0.5               # 最大延迟时间（秒）
        
        self.use_human_like_move = True    # 启用人类移动模拟
        self.use_breaks = True             # 启用定期休息
        self.break_interval = 1800         # 休息间隔（秒）
        self.break_duration = 60           # 休息时长（秒）
        
        self.press_duration_min = 0.03     # 最短按压时间
        self.press_duration_max = 0.12     # 最长按压时间
        
        self.move_duration_min = 0.2       # 最短移动时间
        self.move_duration_max = 0.5       # 最长移动时间
        
        # 断点参数
        self.last_break_time = time.time()
        self.continuous_actions = 0
        
        # 如果提供了窗口句柄，获取窗口区域
        if self.hwnd:
            self.get_window_rect()
    
    def set_hwnd(self, hwnd):
        """设置窗口句柄
        
        Args:
            hwnd: 窗口句柄
            
        Returns:
            bool: 是否设置成功
        """
        if hwnd and win32gui.IsWindow(hwnd):
            self.hwnd = hwnd
            self.get_window_rect()
            logging.info(f"设置窗口句柄: {hwnd}")
            return True
        logging.error(f"无效的窗口句柄: {hwnd}")
        return False
    
    def get_window_rect(self):
        """获取窗口矩形区域
        
        Returns:
            tuple: 窗口矩形区域 (left, top, right, bottom)
        """
        if not self.hwnd:
            logging.warning("未设置窗口句柄，无法获取窗口区域")
            return None
            
        try:
            self.window_rect = win32gui.GetWindowRect(self.hwnd)
            left, top, right, bottom = self.window_rect
            width = right - left
            height = bottom - top
            logging.info(f"获取窗口区域: 位置[{left},{top}], 尺寸[{width}x{height}]")
            return self.window_rect
        except Exception as e:
            logging.error(f"获取窗口区域失败: {str(e)}")
            return None
    
    def click(self, x: int, y: int, delay: float = None, is_screen_coord: bool = False) -> bool:
        """点击指定位置，支持随机化点击位置和平滑移动
        
        Args:
            x: 横坐标
            y: 纵坐标
            delay: 点击后等待时间，为None则使用随机值
            is_screen_coord: 是否已经是屏幕坐标，True表示直接使用，False需要转换
            
        Returns:
            bool: 是否点击成功
        """
        if not self.hwnd and not is_screen_coord:
            logging.error("未设置窗口句柄，无法点击")
            return False
            
        final_x, final_y = x, y
        
        # 如果不是屏幕坐标，需要转换
        if not is_screen_coord and self.hwnd:
            try:
                # 转换为屏幕坐标
                final_x, final_y = win32gui.ClientToScreen(self.hwnd, (x, y))
                logging.debug(f"坐标转换: 客户区[{x},{y}] -> 屏幕[{final_x},{final_y}]")
            except Exception as e:
                logging.error(f"坐标转换失败: {str(e)}")
                return False
        
        if self.use_random_click:
            # 在半径范围内随机化点击位置
            angle = random.uniform(0, 2 * math.pi)
            distance = random.uniform(0, self.click_radius)
            rand_x = int(final_x + distance * math.cos(angle))
            rand_y = int(final_y + distance * math.sin(angle))
            final_x, final_y = rand_x, rand_y
            logging.debug(f"随机化点击位置: 原始[{x},{y}] -> 随机化[{final_x},{final_y}]")
        
        logging.info(f"点击位置: [{final_x},{final_y}]")
        
        try:
            # 直接设置鼠标位置，而不是平滑移动
            win32api.SetCursorPos((final_x, final_y))
            
            # 减少点击前的短暂停顿
            time.sleep(random.uniform(0.02, 0.08))
            
            # 点击
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)
            time.sleep(random.uniform(self.press_duration_min, self.press_duration_max))  # 随机化按下时间
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)
            
            # 记录连续操作次数
            self.continuous_actions += 1
            
            # 添加随机延迟
            if delay is None and self.use_random_delay:
                delay = random.uniform(self.min_delay, self.max_delay)
                logging.debug(f"随机延迟: {delay:.2f}秒")
            elif delay is not None:
                logging.debug(f"使用指定延迟: {delay:.2f}秒")
            else:
                delay = 0.1  # 默认延迟
            
            time.sleep(delay)
            return True
        except Exception as e:
            logging.error(f"点击操作失败: {str(e)}")
            return False
    
    def human_click(self, x: int, y: int, delay: float = None, is_screen_coord: bool = False) -> bool:
        """模拟人类点击行为，使用平滑移动路径
        
        Args:
            x: 目标x坐标
            y: 目标y坐标
            delay: 点击后延迟时间，如果为None则使用随机延迟
            is_screen_coord: 是否已经是屏幕坐标
            
        Returns:
            bool: 是否成功点击
        """
        if not self.hwnd and not is_screen_coord:
            logging.error("未设置窗口句柄，无法点击")
            return False
            
        final_x, final_y = x, y
        
        # 如果不是屏幕坐标，需要转换
        if not is_screen_coord and self.hwnd:
            try:
                # 转换为屏幕坐标
                final_x, final_y = win32gui.ClientToScreen(self.hwnd, (x, y))
                logging.debug(f"坐标转换: 客户区[{x},{y}] -> 屏幕[{final_x},{final_y}]")
            except Exception as e:
                logging.error(f"坐标转换失败: {str(e)}")
                return False
        
        try:
            # 获取当前鼠标位置
            current_pos = win32gui.GetCursorPos()
            
            # 生成人类般的移动路径
            if self.use_human_like_move:
                # 使用贝塞尔曲线生成平滑路径
                # 速度随机化
                move_duration = random.uniform(self.move_duration_min, self.move_duration_max)
                
                # 实现平滑移动
                self.smooth_move(current_pos[0], current_pos[1], final_x, final_y, duration=move_duration)
                
                # 短暂随机停顿后点击
                time.sleep(random.uniform(0.02, 0.08))
            else:
                # 如果不使用人类行为模拟，直接设置鼠标位置
                win32api.SetCursorPos((final_x, final_y))
            
            # 执行点击
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)
            # 随机按压时间
            time.sleep(random.uniform(self.press_duration_min, self.press_duration_max))
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)
            
            # 记录连续操作次数
            self.continuous_actions += 1
            
            # 使用随机延迟或指定延迟
            if delay is None and self.use_random_delay:
                delay = random.uniform(self.min_delay, self.max_delay)
                logging.debug(f"随机延迟: {delay:.2f}秒")
            elif delay is not None:
                logging.debug(f"使用指定延迟: {delay:.2f}秒")
            else:
                delay = 0.1  # 默认延迟
            
            time.sleep(delay)
            return True
        except Exception as e:
            logging.error(f"人类模拟点击失败: {e}")
            return False
    
    def right_click(self, x: int, y: int, delay: float = None, is_screen_coord: bool = False) -> bool:
        """执行鼠标右键点击
        
        Args:
            x: X坐标
            y: Y坐标
            delay: 点击后延迟时间，为None则使用随机值
            is_screen_coord: 是否已经是屏幕坐标
            
        Returns:
            bool: 是否成功点击
        """
        if not self.hwnd and not is_screen_coord:
            logging.error("未设置窗口句柄，无法点击")
            return False
            
        final_x, final_y = x, y
        
        # 如果不是屏幕坐标，需要转换
        if not is_screen_coord and self.hwnd:
            try:
                # 转换为屏幕坐标
                final_x, final_y = win32gui.ClientToScreen(self.hwnd, (x, y))
                logging.debug(f"坐标转换: 客户区[{x},{y}] -> 屏幕[{final_x},{final_y}]")
            except Exception as e:
                logging.error(f"坐标转换失败: {str(e)}")
                return False
        
        try:
            # 直接设置鼠标位置
            win32api.SetCursorPos((final_x, final_y))
            
            # 减少点击前的短暂停顿
            time.sleep(random.uniform(0.02, 0.08))
            
            # 右键点击
            win32api.mouse_event(win32con.MOUSEEVENTF_RIGHTDOWN, 0, 0, 0, 0)
            time.sleep(random.uniform(self.press_duration_min, self.press_duration_max))  # 随机化按下时间
            win32api.mouse_event(win32con.MOUSEEVENTF_RIGHTUP, 0, 0, 0, 0)
            
            # 记录连续操作次数
            self.continuous_actions += 1
            
            # 添加随机延迟
            if delay is None and self.use_random_delay:
                delay = random.uniform(self.min_delay, self.max_delay)
                logging.debug(f"随机延迟: {delay:.2f}秒")
            elif delay is not None:
                logging.debug(f"使用指定延迟: {delay:.2f}秒")
            else:
                delay = 0.1  # 默认延迟
            
            time.sleep(delay)
            return True
        except Exception as e:
            logging.error(f"右键点击操作失败: {str(e)}")
            return False
    
    def double_click(self, x: int, y: int, delay: float = None, is_screen_coord: bool = False) -> bool:
        """执行鼠标双击
        
        Args:
            x: X坐标
            y: Y坐标
            delay: 点击后延迟时间，为None则使用随机值
            is_screen_coord: 是否已经是屏幕坐标
            
        Returns:
            bool: 是否成功双击
        """
        try:
            # 第一次点击
            result1 = self.click(x, y, 0.05, is_screen_coord)
            
            # 第二次点击
            result2 = self.click(x, y, 0, is_screen_coord)
            
            # 延迟
            if delay is None and self.use_random_delay:
                delay = random.uniform(self.min_delay, self.max_delay)
                logging.debug(f"随机延迟: {delay:.2f}秒")
            elif delay is not None:
                logging.debug(f"使用指定延迟: {delay:.2f}秒")
            else:
                delay = 0.1  # 默认延迟
            
            time.sleep(delay)
            return result1 and result2
        except Exception as e:
            logging.error(f"双击失败: {str(e)}")
            return False
    
    def drag(self, x1: int, y1: int, x2: int, y2: int, duration: float = 0.5, 
             delay: float = None, is_screen_coord: bool = False) -> bool:
        """拖拽操作
        
        Args:
            x1: 起始X坐标
            y1: 起始Y坐标
            x2: 目标X坐标
            y2: 目标Y坐标
            duration: 拖拽持续时间(秒)
            delay: 拖拽后延迟时间(秒)，为None则使用随机值
            is_screen_coord: 是否已经是屏幕坐标
            
        Returns:
            bool: 是否成功拖拽
        """
        if not self.hwnd and not is_screen_coord:
            logging.error("未设置窗口句柄，无法拖拽")
            return False
            
        start_x, start_y = x1, y1
        end_x, end_y = x2, y2
        
        # 如果不是屏幕坐标，需要转换
        if not is_screen_coord and self.hwnd:
            try:
                # 转换为屏幕坐标
                start_x, start_y = win32gui.ClientToScreen(self.hwnd, (x1, y1))
                end_x, end_y = win32gui.ClientToScreen(self.hwnd, (x2, y2))
                logging.debug(f"坐标转换: 客户区[{x1},{y1}]->[{x2},{y2}] -> 屏幕[{start_x},{start_y}]->[{end_x},{end_y}]")
            except Exception as e:
                logging.error(f"坐标转换失败: {str(e)}")
                return False
        
        try:
            # 移动到起点
            win32api.SetCursorPos((start_x, start_y))
            time.sleep(random.uniform(0.02, 0.08))
            
            # 按下鼠标
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)
            
            # 使用贝塞尔曲线生成平滑轨迹
            path = self._generate_human_path(start_x, start_y, end_x, end_y)
            
            # 移动持续时间
            if duration < 0.1:
                duration = random.uniform(0.3, 0.7)  # 使用随机持续时间
                
            # 计算每步间隔时间
            step_delay = duration / len(path)
            
            # 执行移动
            for point_x, point_y in path:
                win32api.SetCursorPos((int(point_x), int(point_y)))
                time.sleep(step_delay)
            
            # 确保到达终点
            win32api.SetCursorPos((end_x, end_y))
            
            # 松开鼠标
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)
            
            # 记录连续操作次数
            self.continuous_actions += 1
            
            # 延迟
            if delay is None and self.use_random_delay:
                delay = random.uniform(self.min_delay, self.max_delay)
                logging.debug(f"随机延迟: {delay:.2f}秒")
            elif delay is not None:
                logging.debug(f"使用指定延迟: {delay:.2f}秒")
            else:
                delay = 0.1  # 默认延迟
            
            time.sleep(delay)
            return True
        except Exception as e:
            logging.error(f"拖拽失败: {str(e)}")
            # 确保释放鼠标
            try:
                win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)
            except:
                pass
            return False
    
    def _bernstein(self, i: int, n: int, t: float) -> float:
        """计算贝塞尔曲线的伯恩斯坦多项式值
        
        Args:
            i: 控制点索引
            n: 控制点数量
            t: 参数值(0-1)
            
        Returns:
            float: 伯恩斯坦多项式值
        """
        return math.comb(n, i) * (t ** i) * ((1 - t) ** (n - i))
    
    def smooth_move(self, start_x: int, start_y: int, end_x: int, end_y: int, duration: float = 0.3) -> None:
        """使用贝塞尔曲线平滑移动鼠标
        
        Args:
            start_x: 起始X坐标
            start_y: 起始Y坐标
            end_x: 目标X坐标
            end_y: 目标Y坐标
            duration: 移动持续时间(秒)
        """
        # 生成人类般的移动路径
        path = self._generate_human_path(start_x, start_y, end_x, end_y)
        
        # 计算每步间隔时间
        step_delay = duration / len(path)
        
        # 执行移动
        for point_x, point_y in path:
            win32api.SetCursorPos((int(point_x), int(point_y)))
            time.sleep(step_delay)
    
    def _generate_human_path(self, x1: int, y1: int, x2: int, y2: int, control_points: int = 2) -> List[Tuple[float, float]]:
        """生成模拟人类移动的贝塞尔曲线路径
        
        Args:
            x1: 起始X坐标
            y1: 起始Y坐标
            x2: 目标X坐标
            y2: 目标Y坐标
            control_points: 额外控制点数量
            
        Returns:
            List[Tuple[float, float]]: 路径点列表
        """
        # 起点和终点
        points = [(x1, y1), (x2, y2)]
        
        # 计算两点间距离
        distance = math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)
        
        # 根据距离确定路径点数量
        point_count = max(int(distance / 50) * 10, 10)
        
        # 生成控制点
        control_points_list = []
        for i in range(control_points):
            # 沿线路随机偏移
            dev_x = random.uniform(-0.3, 0.3) * distance
            dev_y = random.uniform(-0.3, 0.3) * distance
            
            # 计算当前点在路径上的位置(0.0-1.0)
            t = (i + 1) / (control_points + 1)
            
            # 在起点和终点之间的位置
            x = x1 + t * (x2 - x1) + dev_x
            y = y1 + t * (y2 - y1) + dev_y
            
            control_points_list.append((x, y))
        
        # 组合所有点（起点 + 控制点 + 终点）
        all_points = [points[0]] + control_points_list + [points[1]]
        n = len(all_points) - 1
        
        # 生成贝塞尔曲线
        result = []
        for i in range(point_count):
            t = i / (point_count - 1)
            x, y = 0, 0
            for j in range(n + 1):
                bern = self._bernstein(j, n, t)
                x += all_points[j][0] * bern
                y += all_points[j][1] * bern
            result.append((x, y))
        
        return result
    
    def random_delay(self, min_delay: float = None, max_delay: float = None) -> float:
        """生成随机延迟并等待
        
        Args:
            min_delay: 最小延迟时间，如果为None则使用实例默认值
            max_delay: 最大延迟时间，如果为None则使用实例默认值
            
        Returns:
            float: 实际延迟时间
        """
        if min_delay is None:
            min_delay = self.min_delay
        if max_delay is None:
            max_delay = self.max_delay
            
        delay = random.uniform(min_delay, max_delay)
        time.sleep(delay)
        return delay
    
    def check_if_break_needed(self) -> bool:
        """检查是否需要休息
        
        Returns:
            bool: 是否需要休息
        """
        if not self.use_breaks:
            return False
            
        current_time = time.time()
        time_since_last_break = current_time - self.last_break_time
        
        # 两种情况需要休息：时间达到或连续操作过多
        if time_since_last_break > self.break_interval:
            logging.info(f"已运行{time_since_last_break:.1f}秒，达到休息间隔")
            return True
            
        return False
    
    def take_break(self, callback: Callable = None) -> None:
        """执行休息
        
        Args:
            callback: 回调函数，用于通知UI
        """
        if not self.use_breaks:
            return
            
        logging.info(f"开始休息{self.break_duration}秒")
        if callback:
            callback(f"自动休息{self.break_duration}秒...")
            
        # 随机化真实休息时间
        actual_break = self.break_duration * random.uniform(0.8, 1.2)
        time.sleep(actual_break)
        
        self.last_break_time = time.time()
        self.continuous_actions = 0
        
        logging.info(f"休息结束，继续运行")
        if callback:
            callback("休息结束，继续运行...")
    
    def get_random_click_offset(self, width: int, height: int, percentage: float = 0.015) -> Tuple[int, int]:
        """计算随机点击偏移量
        
        Args:
            width: 窗口宽度
            height: 窗口高度
            percentage: 偏移比例，默认为窗口尺寸的1.5%
            
        Returns:
            Tuple[int, int]: (offset_x, offset_y) 偏移量
        """
        # 计算基于窗口尺寸的偏移范围
        offset_range = min(int(width * percentage), int(height * percentage))
        # 确保至少有几个像素的偏移
        offset_range = max(3, offset_range)
        
        # 生成随机偏移
        offset_x = random.randint(-offset_range, offset_range)
        offset_y = random.randint(-offset_range, offset_range)
        
        return (offset_x, offset_y)
    
    def get_random_rect_position(self, min_rel_x: float, min_rel_y: float, 
                                max_rel_x: float, max_rel_y: float,
                                width: int, height: int) -> Tuple[int, int]:
        """在相对矩形区域内生成随机位置
        
        Args:
            min_rel_x: 左边界相对位置 (0.0-1.0)
            min_rel_y: 上边界相对位置 (0.0-1.0)
            max_rel_x: 右边界相对位置 (0.0-1.0)
            max_rel_y: 下边界相对位置 (0.0-1.0)
            width: 窗口宽度
            height: 窗口高度
            
        Returns:
            Tuple[int, int]: (x, y) 随机位置坐标
        """
        # 计算实际像素区域
        min_x = int(min_rel_x * width)
        min_y = int(min_rel_y * height)
        max_x = int(max_rel_x * width)
        max_y = int(max_rel_y * height)
        
        # 生成随机坐标
        rand_x = random.randint(min_x, max_x)
        rand_y = random.randint(min_y, max_y)
        
        return (rand_x, rand_y) 