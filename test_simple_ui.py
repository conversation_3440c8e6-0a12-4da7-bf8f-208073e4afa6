#!/usr/bin/env python3
"""测试简单界面 - 确保不透明"""

import sys
from PyQt5.QtWidgets import (QApplication, QWidget, QVBoxLayout, QHBoxLayout,
                             QLabel, QPushButton, QGroupBox)
from PyQt5.QtCore import Qt

class SimpleTestWindow(QWidget):
    """简单测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
    
    def setup_ui(self):
        """设置界面"""
        self.setWindowTitle("🎯 界面透明度测试")
        self.setFixedSize(400, 300)
        
        # 强制设置为不透明
        self.setAttribute(Qt.WA_TranslucentBackground, False)
        self.setAttribute(Qt.WA_NoSystemBackground, False)
        self.setWindowOpacity(1.0)
        
        # 设置背景色
        self.setStyleSheet("""
            QWidget {
                background-color: #ffffff;
                font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #007bff;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: #f8f9fa;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #007bff;
                background-color: #ffffff;
            }
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QLabel {
                color: #333333;
                padding: 5px;
                font-size: 13px;
            }
        """)
        
        layout = QVBoxLayout()
        
        # 标题
        title = QLabel("🎯 界面透明度测试")
        title.setStyleSheet("""
            font-size: 20px; 
            font-weight: bold; 
            color: #007bff; 
            margin: 15px;
            padding: 15px;
            background-color: #e7f3ff;
            border-radius: 8px;
            border: 2px solid #007bff;
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 说明
        info = QLabel("如果您能清楚看到这些文字和背景色，\n说明界面是完全不透明的。")
        info.setStyleSheet("""
            color: #333333; 
            margin: 10px;
            padding: 15px;
            background-color: #fff3cd;
            border-radius: 6px;
            border: 1px solid #ffeaa7;
            font-size: 14px;
            line-height: 1.5;
        """)
        info.setAlignment(Qt.AlignCenter)
        info.setWordWrap(True)
        layout.addWidget(info)
        
        # 测试组
        test_group = QGroupBox("测试区域")
        test_layout = QVBoxLayout()
        
        # 测试按钮
        test_btn = QPushButton("🔍 点击测试")
        test_btn.clicked.connect(self.test_click)
        test_layout.addWidget(test_btn)
        
        # 状态标签
        self.status_label = QLabel("✅ 界面正常显示")
        self.status_label.setStyleSheet("""
            color: #155724; 
            padding: 10px;
            background-color: #d4edda;
            border-radius: 6px;
            border: 1px solid #c3e6cb;
            font-size: 13px;
            font-weight: bold;
        """)
        self.status_label.setAlignment(Qt.AlignCenter)
        test_layout.addWidget(self.status_label)
        
        test_group.setLayout(test_layout)
        layout.addWidget(test_group)
        
        # 关闭按钮
        close_btn = QPushButton("❌ 关闭测试")
        close_btn.setStyleSheet("""
            background-color: #dc3545;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 14px;
        """)
        close_btn.clicked.connect(self.close)
        layout.addWidget(close_btn)
        
        self.setLayout(layout)
    
    def test_click(self):
        """测试点击"""
        self.status_label.setText("🎉 按钮点击正常！界面完全不透明！")
        self.status_label.setStyleSheet("""
            color: #0c5460; 
            padding: 10px;
            background-color: #bee5eb;
            border-radius: 6px;
            border: 1px solid #86cfda;
            font-size: 13px;
            font-weight: bold;
        """)

def main():
    """主函数"""
    print("🎯 启动界面透明度测试...")
    
    app = QApplication(sys.argv)
    
    window = SimpleTestWindow()
    window.show()
    
    print("✅ 测试窗口已显示！")
    print("🔍 请检查：")
    print("   1. 窗口是否完全不透明")
    print("   2. 背景色是否正常显示")
    print("   3. 文字是否清晰可见")
    print("   4. 按钮是否可以正常点击")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
