# 📝 日志系统使用指南

## 🎯 日志位置说明

### 📍 **GUI界面日志**
- **位置**：结界突破GUI界面右侧的"运行日志"面板
- **特点**：实时显示，自动滚动到最新内容
- **内容**：所有运行过程中的日志信息
- **操作**：支持清空日志按钮

### 📁 **文件日志**
程序会自动在以下位置创建日志文件：

```
logs/
├── main.log                    # 主日志文件（所有模块）
├── error.log                   # 错误日志文件（仅错误信息）
├── realm_raid.log             # 结界突破专用日志
└── realm_raid_YYYYMMDD_HHMMSS.log  # 会话日志（每次运行）
```

#### 📄 **日志文件说明**

1. **main.log**
   - 包含所有模块的日志信息
   - 自动轮转（最大10MB，保留5个备份）
   - 格式：`时间 - 模块名 - 级别 - 消息`

2. **error.log**
   - 仅包含ERROR级别的日志
   - 自动轮转（最大5MB，保留3个备份）
   - 用于快速定位错误问题

3. **realm_raid.log**
   - 结界突破模块专用日志
   - 包含详细的突破流程信息
   - 自动轮转（最大10MB，保留5个备份）

4. **会话日志**
   - 每次启动结界突破时创建
   - 文件名包含时间戳，便于区分
   - 记录单次会话的完整过程

## 🔍 **日志查看方法**

### 方法1：GUI界面查看
- 在结界突破界面右侧直接查看实时日志
- 支持清空日志功能
- 自动滚动到最新内容

### 方法2：日志查看器工具
```bash
# 启动专用日志查看器
python tools/log_viewer.py
```

**日志查看器功能：**
- 📁 浏览所有日志文件
- 🔍 搜索关键词
- 🔄 自动刷新
- 📊 显示文件大小和修改时间
- 🗑️ 删除旧日志文件

### 方法3：直接打开文件
使用任何文本编辑器打开logs目录下的日志文件：
- 记事本
- VS Code
- Notepad++
- 等

## 📊 **日志级别说明**

### 🔵 **INFO（信息）**
- 正常的操作流程信息
- 例如：开始突破、选择目标、战斗结果等

### 🟡 **WARNING（警告）**
- 可能的问题，但不影响继续运行
- 例如：模板未找到、突破券不足等

### 🔴 **ERROR（错误）**
- 严重错误，可能导致功能异常
- 例如：连接失败、截图失败、异常退出等

### 🔍 **DEBUG（调试）**
- 详细的调试信息
- 例如：坐标计算、模板匹配详情等

## 📋 **典型日志示例**

### ✅ **正常运行日志**
```
2024-01-20 14:30:15 - INFO - 结界突破机器人初始化完成
2024-01-20 14:30:16 - INFO - 开始结界突破流程
2024-01-20 14:30:18 - INFO - 成功进入结界突破界面
2024-01-20 14:30:20 - INFO - 找到 6 个可用结界
2024-01-20 14:30:21 - INFO - 选择目标结界: 索引2 (第0行第2列)
2024-01-20 14:30:23 - INFO - 成功开始挑战
2024-01-20 14:30:25 - INFO - 开始战斗
2024-01-20 14:30:45 - INFO - 战斗结果: victory
2024-01-20 14:30:46 - INFO - 战斗胜利！
2024-01-20 14:30:47 - INFO - 完成第 1 次突破
```

### ⚠️ **警告日志示例**
```
2024-01-20 14:35:20 - WARNING - 突破券不足，等待恢复...
2024-01-20 14:35:25 - WARNING - 找不到挑战按钮
2024-01-20 14:35:30 - WARNING - 没有找到可用的结界
```

### ❌ **错误日志示例**
```
2024-01-20 14:40:15 - ERROR - 无法导航到结界突破界面
2024-01-20 14:40:16 - ERROR - 找不到阴阳寮按钮
2024-01-20 14:40:20 - ERROR - 执行单次突破失败: 连接超时
2024-01-20 14:40:21 - ERROR - 突破线程异常: 窗口句柄无效
```

## 🛠️ **日志配置**

### 修改日志级别
在 `main_gui.py` 中修改：
```python
setup_logging(
    log_level=logging.DEBUG,  # 改为DEBUG查看更多信息
    enable_file_log=True,
    log_dir="logs"
)
```

### 禁用文件日志
```python
setup_logging(
    log_level=logging.INFO,
    enable_file_log=False,  # 禁用文件日志
    log_dir="logs"
)
```

### 自定义日志目录
```python
setup_logging(
    log_level=logging.INFO,
    enable_file_log=True,
    log_dir="my_logs"  # 自定义目录
)
```

## 🔧 **日志维护**

### 自动清理
日志文件会自动轮转，无需手动清理：
- 当文件超过设定大小时自动创建新文件
- 保留指定数量的备份文件
- 自动删除过旧的备份

### 手动清理
如需手动清理：
1. 使用日志查看器的删除功能
2. 直接删除logs目录下的文件
3. 运行清理脚本（如需要可创建）

### 备份重要日志
对于重要的运行记录，建议：
1. 复制到其他位置备份
2. 压缩存档长期保存
3. 导出关键统计信息

## 🚨 **故障排除**

### 日志文件无法创建
- 检查logs目录是否存在
- 检查目录写入权限
- 确认磁盘空间充足

### 日志内容乱码
- 确认文件编码为UTF-8
- 使用支持UTF-8的编辑器打开
- 检查系统区域设置

### 日志文件过大
- 检查轮转配置是否正确
- 手动删除过大的日志文件
- 调整日志级别减少输出

## 📞 **技术支持**

如果遇到日志相关问题：
1. 查看error.log获取错误信息
2. 检查日志配置是否正确
3. 确认文件权限和磁盘空间
4. 使用日志查看器工具分析

---

**提示**：合理使用日志功能可以帮助快速定位问题，提高脚本的稳定性和可维护性。
