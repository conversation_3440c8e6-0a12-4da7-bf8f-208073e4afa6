# 🎮 阴阳师自动化工具 v2.0 使用指南

## 🚀 **快速开始**

### 启动程序
```bash
# 进入程序目录
cd D:\探索脚本

# 启动新版本界面
python main_gui_v2.py
```

### 界面概览
程序启动后，您将看到全新的左侧标签页界面：

```
┌─────────────────────────────────────────────────────────────────┐
│ 阴阳师自动化工具 v2.0                                            │
├─────────────────────────────────────────────────────────────────┤
│ ┌─────────────────┐ ┌─────────────────────────────────────────┐ │
│ │ 🔧 通用设置      │ │                                         │ │
│ │ 🗺️ 探索副本      │ │         欢迎使用阴阳师自动化工具         │ │
│ │ 🏰 结界突破      │ │                                         │ │
│ │ 📝 运行日志      │ │            功能说明和使用指南            │ │
│ │                 │ │                                         │ │
│ │   [设置面板]     │ │                                         │ │
│ └─────────────────┘ └─────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│ 当前窗口: 未选择 | 当前页面: 🔧 通用设置                        │
└─────────────────────────────────────────────────────────────────┘
```

## 📋 **使用步骤**

### 第一步：通用设置
1. **点击左侧 "🔧 通用设置" 标签页**
2. **选择游戏窗口**：
   - 方法1：在"窗口标题"输入框输入"MuMu"或"阴阳师"，点击"查找窗口"
   - 方法2：直接输入窗口句柄数字，点击"使用句柄"
   - 方法3：拖拽十字准星到游戏窗口上
3. **配置全局设置**（可选）：
   - 操作模式：建议启用"后台模式运行"
   - 日志级别：推荐使用"INFO"级别
   - 其他设置根据需要调整
4. **点击"保存设置"**

### 第二步：选择功能
根据需要点击对应的功能标签页：

#### 🗺️ **探索副本**
1. **点击 "🗺️ 探索副本" 标签页**
2. **配置探索参数**：
   - 探索类型：选择章节（推荐第二十八章）
   - 探索次数：设置想要探索的次数
   - 体力设置：勾选"使用体力药"，设置"保留体力"
3. **高级设置**（可选）：
   - 勾选"自动战斗"和"快速战斗"
   - 调整操作延迟（默认1秒）
   - 启用"自动错误恢复"和"错误时截图"
4. **开始探索**：
   - 点击"测试连接"验证设置
   - 点击"开始探索"启动功能

#### 🏰 **结界突破**
1. **点击 "🏰 结界突破" 标签页**
2. **配置突破参数**：
   - 突破次数：设置想要突破的次数
   - 选择策略：推荐"智能网格"
   - 失败处理：勾选"失败后重试"和"跳过冷却结界"
3. **高级设置**（可选）：
   - 勾选"自动战斗"
   - 设置战斗超时时间（默认180秒）
   - 调整随机延迟范围
   - 配置定时休息（可选）
4. **开始突破**：
   - 点击"测试连接"验证设置
   - 点击"开始突破"启动功能

### 第三步：监控运行
1. **点击 "📝 运行日志" 标签页**
2. **查看实时日志**：
   - 所有操作都会实时显示在日志中
   - 可以看到详细的执行步骤和结果
   - 错误信息会用红色标记显示
3. **日志管理**：
   - 使用搜索功能查找特定信息
   - 可以清空当前日志
   - 通过菜单栏打开日志查看器

## 🎯 **功能特性**

### ✨ **新界面优势**
- **统一窗口管理**：一次选择，所有功能共享
- **集中设置面板**：所有配置都在左侧标签页
- **实时状态显示**：右侧区域显示详细说明和状态
- **智能内容切换**：根据选中标签显示对应内容

### 🔧 **通用设置功能**
- **窗口管理**：多种方式选择游戏窗口
- **操作模式**：后台模式减少干扰
- **日志系统**：完整的操作记录和错误追踪
- **性能优化**：截图质量和OCR加速选项
- **安全机制**：操作延迟和错误恢复

### 🗺️ **探索副本功能**
- **章节选择**：支持第一章到第二十八章
- **智能体力管理**：自动使用体力药，保留指定体力
- **战斗模式**：支持自动战斗和快速战斗
- **错误处理**：自动恢复机制和错误截图

### 🏰 **结界突破功能**
- **智能选择**：多种结界选择策略
- **3x3网格**：完整的网格扫描和选择
- **失败处理**：支持失败重试和冷却跳过
- **定时休息**：避免长时间连续操作

## 📝 **日志系统**

### 日志级别说明
- **🔍 DEBUG**：详细调试信息（坐标、策略选择等）
- **ℹ️ INFO**：一般信息（主要操作步骤和结果）
- **⚠️ WARNING**：警告信息（模板未找到、超时等）
- **❌ ERROR**：错误信息（严重问题和异常）

### 日志文件位置
```
logs/
├── main.log                    # 完整日志
├── error.log                   # 仅错误日志
├── realm_raid.log             # 结界突破专用日志
└── explore_YYYYMMDD_HHMMSS.log # 会话日志
```

### 查看日志
- **实时日志**：在"📝 运行日志"标签页查看
- **历史日志**：菜单栏 → 工具 → 日志查看器
- **文件日志**：直接打开logs目录下的文件

## ⚙️ **高级功能**

### 配置管理
- **导入配置**：菜单栏 → 文件 → 导入配置
- **导出配置**：菜单栏 → 文件 → 导出配置
- **重置设置**：在全局设置中点击"重置默认"

### 错误处理
- **自动恢复**：程序会自动尝试恢复错误状态
- **错误截图**：出错时自动保存截图到screenshots目录
- **详细日志**：所有错误都有完整的堆栈跟踪信息

### 性能优化
- **后台模式**：减少对前台窗口的干扰
- **GPU加速**：如果有GPU可以加速OCR识别
- **截图质量**：可以调整截图质量平衡性能和准确性

## 🚨 **常见问题**

### Q: 程序启动失败
A: 检查依赖项是否完整安装：
```bash
pip install opencv-python numpy easyocr pywin32 PyQt5
```

### Q: 找不到游戏窗口
A: 确保：
1. 阴阳师游戏已启动
2. 窗口标题包含"阴阳师"或"MuMu"等关键词
3. 尝试使用拖拽选择工具

### Q: 连接测试失败
A: 检查：
1. 游戏窗口是否被其他窗口遮挡
2. 游戏是否在前台显示
3. 尝试重新选择窗口

### Q: 功能运行异常
A: 查看：
1. "📝 运行日志"中的错误信息
2. logs/error.log文件
3. 确保游戏界面在正确的页面

## 🔄 **版本对比**

### v1.0 vs v2.0
| 功能 | v1.0 | v2.0 |
|------|------|------|
| 界面布局 | 分散的功能窗口 | 统一的标签页界面 |
| 窗口管理 | 每个功能独立选择 | 全局统一管理 |
| 日志系统 | 分散在各功能中 | 集中统一显示 |
| 设置管理 | 各功能独立设置 | 全局设置 + 功能设置 |
| 用户体验 | 需要重复操作 | 一次设置，处处使用 |

## 🎉 **总结**

阴阳师自动化工具 v2.0 通过全新的左侧标签页界面设计，大大简化了用户的操作流程：

1. **🔧 一次设置**：在通用设置中选择窗口和配置全局参数
2. **🎯 专注功能**：在功能标签页中专注于核心参数配置
3. **📝 实时监控**：在日志标签页中查看详细的运行状态
4. **🚀 高效使用**：减少重复操作，提升使用效率

现在就启动 `python main_gui_v2.py` 体验全新的界面吧！🎮
