#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""详细的启动调试工具"""

import sys
import os
import logging
import traceback
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def setup_debug_logging():
    """设置调试日志"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('debug_startup.log', encoding='utf-8')
        ]
    )

def test_step_by_step():
    """逐步测试启动过程"""
    print("🔧 开始逐步调试启动过程...")
    
    try:
        # 步骤1: 测试PyQt5基础导入
        print("📦 步骤1: 导入PyQt5基础模块...")
        from PyQt5.QtWidgets import QApplication, QWidget, QLabel, QVBoxLayout
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QIcon
        print("✅ PyQt5基础模块导入成功")
        
        # 步骤2: 创建最小应用
        print("🖥️ 步骤2: 创建最小QApplication...")
        app = QApplication(sys.argv)
        print("✅ QApplication创建成功")
        
        # 步骤3: 创建测试窗口
        print("🏠 步骤3: 创建测试窗口...")
        test_window = QWidget()
        test_window.setWindowTitle("调试测试窗口")
        test_window.setGeometry(100, 100, 300, 200)
        
        layout = QVBoxLayout()
        label = QLabel("✅ 测试窗口正常显示")
        layout.addWidget(label)
        test_window.setLayout(layout)
        
        test_window.show()
        print("✅ 测试窗口显示成功")
        
        # 短暂显示后关闭
        app.processEvents()
        time.sleep(1)
        test_window.close()
        app.quit()
        print("✅ 测试窗口关闭成功")
        
        # 步骤4: 测试核心模块导入
        print("📚 步骤4: 导入核心模块...")
        from core.logging_config import setup_logging
        print("✅ 日志配置模块导入成功")
        
        # 步骤5: 测试主窗口模块导入
        print("🏗️ 步骤5: 导入主窗口模块...")
        from core.main_window import MainWindow
        print("✅ 主窗口模块导入成功")
        
        # 步骤6: 测试结界突破模块导入
        print("🏰 步骤6: 导入结界突破模块...")
        from features.realm_raid.realm_raid_gui_simple import RealmRaidGUI
        print("✅ 结界突破模块导入成功")
        
        # 步骤7: 测试交互式配置模块导入
        print("🖱️ 步骤7: 导入交互式配置模块...")
        from features.realm_raid.interactive_grid_config import InteractiveGridConfigDialog
        print("✅ 交互式配置模块导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 在步骤中发生错误: {e}")
        print(f"详细错误: {traceback.format_exc()}")
        return False

def test_main_window_creation():
    """测试主窗口创建"""
    print("🏗️ 测试主窗口创建过程...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from core.main_window import MainWindow
        from core.logging_config import setup_logging
        
        # 设置日志
        setup_logging(log_level=logging.DEBUG, enable_file_log=True, log_dir="logs")
        
        # 创建应用
        app = QApplication(sys.argv)
        app.setApplicationName("调试测试")
        
        print("🏠 创建主窗口实例...")
        main_window = MainWindow()
        print("✅ 主窗口实例创建成功")
        
        print("🖥️ 尝试显示主窗口...")
        main_window.show()
        print("✅ 主窗口显示命令执行成功")
        
        # 处理事件
        app.processEvents()
        print("✅ 事件处理成功")
        
        # 短暂等待
        time.sleep(2)
        
        # 关闭
        main_window.close()
        app.quit()
        print("✅ 主窗口测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 主窗口创建失败: {e}")
        print(f"详细错误: {traceback.format_exc()}")
        return False

def test_realm_raid_gui():
    """测试结界突破GUI"""
    print("🏰 测试结界突破GUI...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from features.realm_raid.realm_raid_gui_simple import RealmRaidGUI
        
        app = QApplication(sys.argv)
        
        print("🏗️ 创建结界突破GUI...")
        realm_gui = RealmRaidGUI()
        print("✅ 结界突破GUI创建成功")
        
        print("🖥️ 显示结界突破GUI...")
        realm_gui.show()
        print("✅ 结界突破GUI显示成功")
        
        app.processEvents()
        time.sleep(1)
        
        realm_gui.close()
        app.quit()
        print("✅ 结界突破GUI测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 结界突破GUI测试失败: {e}")
        print(f"详细错误: {traceback.format_exc()}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🔧 详细启动调试工具")
    print("=" * 60)
    
    setup_debug_logging()
    
    # 逐步测试
    if not test_step_by_step():
        print("❌ 基础测试失败")
        return 1
    
    print("\n" + "=" * 40)
    print("🏗️ 开始主窗口测试...")
    
    # 测试主窗口
    if not test_main_window_creation():
        print("❌ 主窗口测试失败")
        return 1
    
    print("\n" + "=" * 40)
    print("🏰 开始结界突破GUI测试...")
    
    # 测试结界突破GUI
    if not test_realm_raid_gui():
        print("❌ 结界突破GUI测试失败")
        return 1
    
    print("\n" + "=" * 60)
    print("✅ 所有测试通过！")
    print("💡 建议检查:")
    print("  1. 显卡驱动是否最新")
    print("  2. PyQt5版本是否兼容")
    print("  3. 是否有其他程序占用资源")
    print("=" * 60)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
