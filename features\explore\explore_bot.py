import os
import time
import random
import logging
import math
import numpy as np
import win32gui
import win32con
import win32api
from typing import Tuple, List, Dict, Optional, Callable
from core.base_bot import BaseBot
from core.image_utils import ImageUtils
from core.config_manager import config_manager
from core.chapter_selector import ChapterSelector
from features.explore.explore_config import CHAPTER_TEMPLATES, DEFAULT_SETTINGS

class ExploreBot(BaseBot):
    """阴阳师探索副本自动化类
    
    自动探索副本，打怪，开箱子，挑战Boss
    """
    
    def __init__(self, hwnd=None, base_resolution: Tuple[int, int] = (1920, 1080)):
        """初始化探索机器人
        
        Args:
            hwnd: 游戏窗口句柄
            base_resolution: 基准分辨率，默认为1920x1080
        """
        # 如果提供了窗口句柄，尝试获取子窗口
        if hwnd:
            hwnd = self._get_client_window(hwnd)
            
        super().__init__(hwnd, base_resolution)
        
        # 从配置中获取操作模式设置
        operation_mode = config_manager.get("operation_mode", "background")
        self.enable_background_mode(operation_mode == "background")
        logging.info(f"探索机器人已设置为{'后台' if operation_mode == 'background' else '前台'}模式")
        
        # 章节设置
        self.target_chapter = DEFAULT_SETTINGS.get('target_chapter', 28)
        self.use_ocr_first = DEFAULT_SETTINGS.get('use_ocr_first', True)
        logging.info(f"目标章节设置为: {self.target_chapter}")
        
        # 初始化章节选择器
        self.chapter_selector = ChapterSelector(self)
        self.chapter_selector.set_target_chapter(self.target_chapter)
        
        # 为章节选择器添加已知的模板
        for chapter, template_path in CHAPTER_TEMPLATES.items():
            self.chapter_selector.add_chapter_template(chapter, template_path)
        
        # 探索专用设置
        self.auto_restart = True  # 自动重新开始探索
        self.auto_use_ap = True   # 自动使用体力
        self.move_forward = True  # 自动前进
        self.no_monster_threshold = 30  # 没有怪物时，前进多少次后重新开始
        self.enable_movement = False  # 是否启用移动功能
        self.movement_delay_count = 0  # 延迟移动计数器
        self.movement_delay_threshold = 5  # 延迟移动阈值，连续5次没有可交互元素才开始移动
        
        # 探索统计数据
        self.stats = {
            'battles': 0,      # 战斗次数
            'monsters': 0,     # 怪物数量
            'treasures': 0,    # 宝箱数量
            'bosses': 0,       # Boss数量
            'restarts': 0,     # 重启次数
            'victories': 0,    # 胜利次数
            'rewards': 0,      # 奖励次数
        }
        
        # 探索模板图像路径和点击位置
        self.template_paths = {
            # 模板名称: (文件路径, 相对点击位置)
            'tansuo': ('templates/explore/tansuo.png', (0.5, 0.5)),         # 探索图标
            'explore_icon': ('templates/explore/explore_icon.png', (0.5, 0.5)), # 进入副本
            'yao': ('templates/explore/yao.png', (0.5, 0.5)),               # 探索界面元素
            # 'chapter_btn': ('templates/explore/28.png', (0.5, 0.5)),      # 章节选择 - 改为动态选择
            'challenge_btn': ('templates/explore/challenge_btn.png', (0.5, 0.5)), # 挑战按钮
            'victory': ('templates/explore/victory.png', (0.5, 0.5)),       # 胜利画面
            'reward': ('templates/explore/reward.png', (0.5, 0.5)),         # 奖励画面
            'boss_btn': ('templates/explore/boss_btn.png', (0.5, 0.5)),     # 首领按钮
            'treasure_chest': ('templates/explore/treasure_chest.png', (0.5, 0.5)), # 宝箱图标
            'get_reward': ('templates/explore/get_reward.png', (0.5, 0.5)), # 获取奖励按钮
            'ready_btn': ('templates/explore/ready_btn.png', (0.5, 0.5)),   # 准备按钮
        }
        
        # 探索进度标记
        self.is_exploring = False  # 是否已进入探索状态
        
        # 加载模板
        self.load_templates()
        
        # 为特定模板设置自定义点击区域
        # 为reward.png和victory.png设置在(572,351)到(766,444)范围内随机点击
        self.set_custom_click_area('reward', 572, 351, 766, 444)
        self.set_custom_click_area('victory', 572, 351, 766, 444)
        logging.info("已设置reward和victory模板的自定义点击区域: (572,351)-(766,444)")

    def apply_settings(self, settings):
        """应用用户设置

        Args:
            settings: 用户设置字典
        """
        try:
            logging.info(f"📝 应用探索设置: {settings}")

            # 探索类型设置
            if 'explore_type' in settings:
                explore_type = settings['explore_type']
                # 提取章节数字
                if '第' in explore_type and '章' in explore_type:
                    chapter_str = explore_type.replace('第', '').replace('章', '')
                    # 处理中文数字
                    chinese_numbers = {
                        '一': 1, '二': 2, '三': 3, '四': 4, '五': 5,
                        '六': 6, '七': 7, '八': 8, '九': 9, '十': 10,
                        '十一': 11, '十二': 12, '十三': 13, '十四': 14, '十五': 15,
                        '十六': 16, '十七': 17, '十八': 18, '十九': 19, '二十': 20,
                        '二十一': 21, '二十二': 22, '二十三': 23, '二十四': 24, '二十五': 25,
                        '二十六': 26, '二十七': 27, '二十八': 28
                    }

                    try:
                        # 先尝试直接转换数字
                        chapter_num = int(chapter_str)
                    except ValueError:
                        # 如果失败，尝试中文数字转换
                        chapter_num = chinese_numbers.get(chapter_str)
                        if chapter_num is None:
                            logging.warning(f"⚠️ 无法解析章节数字: {explore_type}")
                            return

                    self.target_chapter = chapter_num
                    self.chapter_selector.set_target_chapter(chapter_num)
                    logging.info(f"🎯 设置目标章节: {chapter_num}")

            # 探索次数设置
            if 'explore_count' in settings:
                self.max_runs = settings['explore_count']
                logging.info(f"🔢 设置探索次数: {self.max_runs}")

            # 体力设置
            if 'use_stamina' in settings:
                self.auto_use_ap = settings['use_stamina']
                logging.info(f"⚡ 自动使用体力药: {self.auto_use_ap}")

            if 'reserve_stamina' in settings:
                self.reserve_ap = settings.get('reserve_stamina', 0)
                logging.info(f"💾 保留体力: {self.reserve_ap}")

            # 战斗设置
            if 'auto_battle' in settings:
                self.auto_battle = settings['auto_battle']
                logging.info(f"⚔️ 自动战斗: {self.auto_battle}")

            if 'quick_battle' in settings:
                self.quick_battle = settings.get('quick_battle', False)
                logging.info(f"⚡ 快速战斗: {self.quick_battle}")

            # 操作延迟设置
            if 'operation_delay' in settings:
                delay_ms = settings['operation_delay']
                self.operation_delay = delay_ms / 1000.0  # 转换为秒
                logging.info(f"⏱️ 操作延迟: {self.operation_delay}秒")

            # 错误处理设置
            if 'auto_recovery' in settings:
                self.auto_recovery = settings['auto_recovery']
                logging.info(f"🔄 自动错误恢复: {self.auto_recovery}")

            if 'screenshot_on_error' in settings:
                self.screenshot_on_error = settings['screenshot_on_error']
                logging.info(f"📸 错误时截图: {self.screenshot_on_error}")

            logging.info("✅ 探索设置应用完成")

        except Exception as e:
            logging.error(f"❌ 应用探索设置失败: {e}")
            import traceback
            logging.error(f"错误详情: {traceback.format_exc()}")

    def _get_client_window(self, hwnd):
        """获取游戏客户区窗口句柄
        
        Args:
            hwnd: 父窗口句柄
            
        Returns:
            int: 客户区窗口句柄（第一个子窗口）
        """
        try:
            # 获取第一个子窗口
            child_hwnd = win32gui.GetWindow(hwnd, win32con.GW_CHILD)
            if child_hwnd:
                window_text = win32gui.GetWindowText(child_hwnd)
                class_name = win32gui.GetClassName(child_hwnd)
                logging.info(f"使用第一个子窗口: 句柄={child_hwnd}, 类名={class_name}, 文本={window_text}")
                return child_hwnd
            else:
                logging.warning("未找到子窗口，使用父窗口")
                return hwnd
        except Exception as e:
            logging.error(f"获取子窗口失败: {str(e)}")
            return hwnd
        
    def set_hwnd(self, hwnd):
        """设置窗口句柄
        
        Args:
            hwnd: 窗口句柄
            
        Returns:
            bool: 是否设置成功
        """
        if hwnd:
            # 获取客户区窗口句柄
            client_hwnd = self._get_client_window(hwnd)
            return super().set_hwnd(client_hwnd)
        return False
        
    def set_target_chapter(self, chapter: int):
        """设置目标章节
        
        Args:
            chapter: 目标章节号
        """
        self.target_chapter = chapter
        if self.chapter_selector:
            self.chapter_selector.set_target_chapter(chapter)
        logging.info(f"目标章节已更改为: {chapter}")
    
    def get_target_chapter(self) -> int:
        """获取当前目标章节"""
        return self.target_chapter
    
    def get_available_chapters(self) -> List[int]:
        """获取当前界面可用的章节列表"""
        if self.chapter_selector:
            return self.chapter_selector.get_available_chapters()
        return []
        
    def load_templates(self):
        """加载探索需要的所有模板图像"""
        for name, (path, rel_click_point) in self.template_paths.items():
            if os.path.exists(path):
                self.load_template(name, path, rel_click_point)
            else:
                logging.warning(f"模板文件不存在: {path}")
                
    def _safe_click_template(self, template_name: str, results: Dict, callback=None, message: str = None) -> bool:
        """安全点击模板位置
        
        Args:
            template_name: 模板名称
            results: 模板查找结果
            callback: 回调函数
            message: 回调消息
            
        Returns:
            bool: 是否成功点击
        """
        if template_name in results:
            pos = results[template_name]
            x, y = pos
            
            # 获取当前窗口大小
            if not self.window_rect:
                self.get_window_rect()
                
            if self.window_rect:
                # 检查是否有自定义点击区域
                if template_name in self.custom_click_areas:
                    # 使用自定义点击区域
                    x1, y1, x2, y2 = self.custom_click_areas[template_name]
                    # 在自定义区域内随机选择坐标
                    rand_x = random.randint(x1, x2)
                    rand_y = random.randint(y1, y2)
                    logging.info(f"点击模板 {template_name}，识别位置: {pos}，使用自定义点击区域: ({rand_x}, {rand_y})")
                    # 使用随机延迟
                    delay = random.uniform(0.1, 0.4)
                    self.click(rand_x, rand_y, delay=delay)
                elif template_name == 'get_reward':
                    # 特殊处理get_reward模板，在指定区域内随机点击
                    left, top, right, bottom = self.window_rect
                    width = right - left
                    height = bottom - top
                    
                    # 使用相对坐标（基于1920x1080分辨率换算）
                    min_rel_x, min_rel_y = 0.171, 0.419  # 对应原来的328,453
                    max_rel_x, max_rel_y = 0.328, 0.537  # 对应原来的630,580
                    
                    # 计算实际像素位置
                    rand_x = random.randint(int(min_rel_x * width), int(max_rel_x * width))
                    rand_y = random.randint(int(min_rel_y * height), int(max_rel_y * height))
                    
                    logging.info(f"点击模板 {template_name}，识别位置: {pos}，使用随机点击位置: ({rand_x}, {rand_y})")
                    # 使用随机延迟
                    delay = random.uniform(0.1, 0.4)
                    self.click(rand_x, rand_y, delay=delay)
                else:
                    # 使用新的高斯自适应随机点击方法
                    rand_x, rand_y = self.adaptive_gaussian_click(template_name, (x, y))
                    
                    logging.info(f"点击模板 {template_name}，识别位置: {pos}，使用高斯自适应点击: ({rand_x}, {rand_y})")
                    # 使用随机延迟
                    delay = random.uniform(0.1, 0.35)
                    self.click(rand_x, rand_y, delay=delay)
            else:
                # 如果无法获取窗口大小，则使用原始坐标点击，但仍添加随机延迟
                logging.warning(f"无法获取窗口大小，使用原始坐标点击")
                delay = random.uniform(0.1, 0.3)
                self.click(x, y, delay=delay)
            
            if callback and message:
                callback(message)
                
            return True
        return False
        
    def start(self, callback: Callable = None) -> None:
        """开始探索
        
        Args:
            callback: 回调函数，用于通知UI
        """
        self.running = True
        self.last_break_time = time.time()  # 记录启动时间
        no_monster_count = 0
        self.is_exploring = False  # 重置探索状态
        self.enable_movement = False  # 初始状态不启用移动
        self.movement_delay_count = 0  # 重置延迟移动计数器
        
        # 确保使用的是当前配置的操作模式
        operation_mode = config_manager.get("operation_mode", "background")
        self.enable_background_mode(operation_mode == "background")
        logging.info(f"探索使用{'后台' if operation_mode == 'background' else '前台'}操作模式")
        
        # 获取当前窗口分辨率并记录
        current_resolution = self.get_current_resolution()
        logging.info(f"当前窗口分辨率: {current_resolution[0]}x{current_resolution[1]}")
        
        if callback:
            callback("开始探索副本")
            
        while self.running:
            try:
                # 检查是否需要休息
                if self.check_if_break_needed():
                    self.take_break(callback)
                    
                # 检查操作模式是否与配置一致
                operation_mode = config_manager.get("operation_mode", "background")
                expected_background = (operation_mode == "background")
                current_background = self.is_background_mode_enabled()

                if current_background != expected_background:
                    self.enable_background_mode(expected_background)
                    mode_text = "后台" if expected_background else "前台"
                    logging.info(f"🔄 同步操作模式为: {mode_text}模式")
                    
                # 获取游戏画面截图
                screenshot = self.take_screenshot()
                if screenshot is None:
                    logging.error("截图失败，尝试更新窗口信息并重试")
                    self.get_window_rect()  # 更新窗口信息
                    self.background_operation.update_window_info()  # 更新后台操作窗口信息
                    time.sleep(0.5)
                    continue
                    
                # 使用并行查找一次性检查所有可能的模板
                template_names = [
                    'tansuo', 'yao', 'explore_icon',                     # 导航相关模板（移除chapter_btn）
                    'challenge_btn', 'boss_btn',                         # 选择按钮
                    'ready_btn', 'victory', 'reward',                    # 战斗相关
                    'treasure_chest', 'get_reward'                       # 奖励相关
                ]
                
                # 并行查找多个模板
                results = self.find_multiple_templates_parallel(template_names)
                
                if results:
                    logging.info(f"并行查找到元素: {list(results.keys())}")
                    # 记录找到的每个元素的坐标，用于调试
                    for name, pos in results.items():
                        logging.info(f"元素 {name} 位置: {pos}")
                
                # 控制移动功能的开启与关闭
                if 'explore_icon' in results:
                    self.enable_movement = True
                    logging.info("识别到explore_icon，启用移动功能")
                elif 'challenge_btn' in results or 'boss_btn' in results:
                    self.enable_movement = False
                    self.movement_delay_count = 0  # 重置延迟计数器
                    logging.info("识别到challenge_btn或boss_btn，关闭移动功能")
                elif 'reward' in results:
                    self.enable_movement = True
                    logging.info("识别到reward，启用移动功能")
                elif 'treasure_chest' in results:
                    self.enable_movement = False
                    self.movement_delay_count = 0  # 重置延迟计数器
                    logging.info("识别到treasure_chest，关闭移动功能")
                
                # 检测是否回到章节选择界面（识别到yao.png）
                if 'yao' in results:
                    if self.is_exploring:
                        logging.info("识别到yao界面元素，重置探索状态")
                        self.is_exploring = False  # 重置探索状态
                        no_monster_count = 0
                        self.movement_delay_count = 0  # 重置延迟计数器
                        
                # 基于优先级处理元素（不使用状态机）
                clicked = False
                
                # 探索中的元素处理（最高优先级）
                if self.is_exploring:
                    # 战斗准备按钮（最高优先级）
                    if self._safe_click_template('ready_btn', results, callback, "检测到战斗准备，点击准备"):
                        self.stats['battles'] += 1
                        clicked = True
                        self.movement_delay_count = 0  # 重置延迟计数器
                        time.sleep(0.5)
                        continue
                        
                    # 胜利画面
                    elif self._safe_click_template('victory', results, callback, "检测到战斗胜利"):
                        self.stats['victories'] += 1
                        clicked = True
                        self.movement_delay_count = 0  # 重置延迟计数器
                        time.sleep(0.5)
                        continue
                        
                    # 奖励画面
                    elif self._safe_click_template('reward', results, callback, "检测到奖励画面"):
                        self.stats['rewards'] += 1
                        clicked = True
                        self.movement_delay_count = 0  # 重置延迟计数器
                        time.sleep(0.5)
                        continue
                        
                    # 宝箱
                    elif self._safe_click_template('treasure_chest', results, callback, "检测到宝箱"):
                        self.stats['treasures'] += 1
                        clicked = True
                        self.movement_delay_count = 0  # 重置延迟计数器
                        time.sleep(0.5)
                        continue
                        
                    # 获取奖励按钮
                    elif self._safe_click_template('get_reward', results, callback, "检测到获取奖励按钮"):
                        clicked = True
                        self.movement_delay_count = 0  # 重置延迟计数器
                        time.sleep(0.5)
                        continue
                        
                # 如果在探索中但没有找到任何可点击元素，判断是否需要移动
                if self.is_exploring and not clicked:
                    no_monster_count += 1
                    
                    # 判断是否启用移动并且达到延迟阈值
                    if self.enable_movement:
                        self.movement_delay_count += 1
                        logging.info(f"未找到交互元素，延迟计数: {self.movement_delay_count}/{self.movement_delay_threshold}")
                        
                        if self.movement_delay_count >= self.movement_delay_threshold:
                            logging.info(f"未找到交互元素，移动计数: ({no_monster_count}/{self.no_monster_threshold})")
                            
                            if no_monster_count >= self.no_monster_threshold:
                                # 移除重置探索状态的功能，保留移动
                                logging.info(f"已达到{self.no_monster_threshold}次未发现怪物，继续移动")
                                # 向前移动
                                self._move_forward(callback)
                                if callback:
                                    callback(f"已达到{self.no_monster_threshold}次未发现怪物，继续移动")
                            else:
                                # 向前移动
                                self._move_forward(callback)
                        else:
                            logging.info(f"延迟移动中({self.movement_delay_count}/{self.movement_delay_threshold})，等待更多次数后才开始移动")
                    else:
                        logging.info("移动功能已禁用，不执行移动")
                        
                # 导航元素处理（按照进入探索的流程顺序，确保能一步步进入探索）
                
                # 如果已在探索中但发现挑战/首领按钮，可能需要点击这些按钮
                if self.is_exploring and ('challenge_btn' in results or 'boss_btn' in results):
                    # 检测挑战按钮
                    if 'challenge_btn' in results:
                        pos = results['challenge_btn']
                        x, y = pos
                        logging.info(f"探索中发现挑战按钮，点击位置: {pos}")
                        self.click(x, y)
                        if callback:
                            callback("点击挑战按钮，开始探索")
                        clicked = True
                        time.sleep(1.5)
                        continue
                        
                    # 检测首领
                    elif 'boss_btn' in results:
                        pos = results['boss_btn']
                        x, y = pos
                        logging.info(f"探索中发现首领，点击位置: {pos}")
                        self.click(x, y)
                        if callback:
                            callback("发现首领，开始挑战")
                        self.stats['bosses'] += 1
                        clicked = True
                        time.sleep(1.5)
                        continue
                        
                # 如果不在探索状态，按顺序尝试进入探索
                if not self.is_exploring and not clicked:
                    # 确认进入副本
                    if 'explore_icon' in results:
                        pos = results['explore_icon']
                        x, y = pos
                        logging.info(f"发现进入副本按钮，点击位置: {pos}")
                        self.click(x, y)
                        if callback:
                            callback("确认进入副本")
                        clicked = True
                        time.sleep(1)
                        continue
                        
                    # 智能章节选择：先检查章节是否存在，再决定是否点击yao
                    elif 'yao' in results:
                        # 首先尝试在当前界面查找目标章节（不进行滚动）
                        logging.info("发现yao模板，先检查当前界面是否有目标章节")
                        chapter_position = self.chapter_selector.find_target_chapter_no_scroll(use_ocr_first=self.use_ocr_first)

                        if chapter_position:
                            # 当前界面就有目标章节，直接点击
                            x, y = chapter_position
                            logging.info(f"当前界面发现目标章节{self.target_chapter}，直接点击，位置: {chapter_position}")
                            self.click(x, y)
                            if callback:
                                callback(f"发现并选择章节{self.target_chapter}")
                            clicked = True
                            time.sleep(1)
                            continue
                        else:
                            # 当前界面没有目标章节，点击yao进入章节选择界面
                            pos = results['yao']
                            x, y = pos
                            logging.info(f"当前界面无目标章节，点击yao进入章节选择界面，位置: {pos}")
                            self.click(x, y)
                            if callback:
                                callback("点击yao，进入章节选择")
                            clicked = True
                            time.sleep(1.5)  # 等待界面加载

                            # 点击yao后尝试选择目标章节（包含滚动查找）
                            if self.chapter_selector.click_target_chapter(use_ocr_first=self.use_ocr_first):
                                if callback:
                                    callback(f"选择章节{self.target_chapter}")
                                time.sleep(1)
                            else:
                                logging.warning("点击yao后仍无法找到目标章节，将在下次循环重试")
                            continue
                        
                    # 点击探索图标
                    elif 'tansuo' in results:
                        pos = results['tansuo']
                        x, y = pos
                        logging.info(f"发现探索图标，点击位置: {pos}")
                        self.click(x, y)
                        if callback:
                            callback("点击探索图标，进入探索界面")
                        clicked = True
                        time.sleep(1)
                        continue
                        
                # 挑战或首领按钮处理（如果不在探索中，这些按钮意味着我们可以开始探索了）
                if not self.is_exploring and not clicked:
                    # 检测挑战按钮
                    if 'challenge_btn' in results:
                        pos = results['challenge_btn']
                        x, y = pos
                        logging.info(f"发现挑战按钮，点击位置: {pos}")
                        self.click(x, y)
                        if callback:
                            callback("点击挑战按钮，开始探索")
                        self.is_exploring = True  # 标记已进入探索状态
                        clicked = True
                        time.sleep(1.5)
                        continue
                        
                    # 检测首领
                    elif 'boss_btn' in results:
                        pos = results['boss_btn']
                        x, y = pos
                        logging.info(f"发现首领，点击位置: {pos}")
                        self.click(x, y)
                        if callback:
                            callback("发现首领，开始挑战")
                        self.is_exploring = True  # 标记已进入探索状态
                        self.stats['bosses'] += 1
                        clicked = True
                        time.sleep(1.5)
                        continue
                        
                # 等待一段时间
                self.wait()
                
            except Exception as e:
                logging.error(f"运行错误: {str(e)}")
                if callback:
                    callback(f"发生错误: {str(e)}")
                # 继续运行
                time.sleep(2)
                
    def _move_forward(self, callback=None):
        """向前移动 - 适用于横板游戏（向右移动）"""
        # 获取当前窗口分辨率
        window_width, window_height = self.get_current_resolution()
        
        # 点击位置计算
        # 水平位置：屏幕右侧约70%-80%处
        x_percent = 0.75  # 屏幕75%位置（靠右）
        x_variance = 0.03  # 水平方向小幅度随机偏移
        x_percent_with_variance = x_percent + random.uniform(-x_variance, x_variance)
        x = int(window_width * x_percent_with_variance)
        
        # 垂直位置：屏幕中部稍微偏下
        y_percent = 0.8  # 屏幕60%位置
        y_variance = 0.05  # 垂直方向随机偏移
        y_percent_with_variance = y_percent + random.uniform(-y_variance, y_variance)
        y = int(window_height * y_percent_with_variance)
        
        logging.info(f"横板游戏向前移动，点击位置: ({x}, {y})，水平位置比例: {x_percent_with_variance:.2f}，垂直位置比例: {y_percent_with_variance:.2f}")
        if callback:
            callback("向右移动")
            
        # 添加随机延迟，使移动更自然
        delay = random.uniform(0.1, 0.3)
        
        # 使用通用点击方法，支持后台模式
        self.click(x, y, delay=delay)
        
    def get_random_screen_position(self, rel_x: float, rel_y: float, variance: float = 0.05) -> Tuple[int, int]:
        """获取屏幕上的随机位置
        
        Args:
            rel_x: 相对X坐标（0.0-1.0）
            rel_y: 相对Y坐标（0.0-1.0）
            variance: 随机偏移量
            
        Returns:
            tuple: 像素位置 (x, y)
        """
        if not self.window_rect:
            self.get_window_rect()
            
        if not self.window_rect:
            return (0, 0)
            
        left, top, right, bottom = self.window_rect
        width = right - left
        height = bottom - top
        
        # 添加随机偏移
        rel_x += random.uniform(-variance, variance)
        rel_y += random.uniform(-variance, variance)
        
        # 限制在0.0-1.0范围内
        rel_x = max(0.0, min(1.0, rel_x))
        rel_y = max(0.0, min(1.0, rel_y))
        
        # 计算像素位置
        x = int(left + rel_x * width)
        y = int(top + rel_y * height)
        
        return (x, y)

    def human_click(self, x: int, y: int, delay: float = None) -> bool:
        """模拟人类点击行为，支持后台窗口操作
        
        Args:
            x: 目标x坐标
            y: 目标y坐标
            delay: 点击后延迟时间，如果为None则使用随机延迟
            
        Returns:
            bool: 是否成功点击
        """
        try:
            if self.hwnd:
                # 转换为窗口客户区相对坐标
                if self.window_rect:
                    left, top, right, bottom = self.window_rect
                    client_x = x - left
                    client_y = y - top
                else:
                    # 如果获取不到窗口位置，直接使用原始坐标
                    client_x = x
                    client_y = y
                
                # 使用PostMessage直接向窗口发送鼠标消息，支持后台操作
                # 计算lParam: 低16位是x坐标，高16位是y坐标
                lParam = win32api.MAKELONG(client_x, client_y)
                
                # 发送鼠标左键按下消息
                win32gui.PostMessage(self.hwnd, win32con.WM_LBUTTONDOWN, win32con.MK_LBUTTON, lParam)
                # 随机按压时间
                time.sleep(random.uniform(0.03, 0.12))
                # 发送鼠标左键松开消息
                win32gui.PostMessage(self.hwnd, win32con.WM_LBUTTONUP, 0, lParam)
                
                logging.info(f"使用PostMessage点击窗口位置: ({client_x}, {client_y})")
            else:
                # 如果没有窗口句柄，回退到原来的物理鼠标点击方法
                logging.warning("没有有效的窗口句柄，使用物理鼠标点击")
                
                # 获取当前鼠标位置
                current_pos = win32gui.GetCursorPos()
                
                # 生成人类般的移动路径
                if self.use_human_like_move:
                    # 使用贝塞尔曲线生成平滑路径
                    # 速度随机化
                    move_duration = random.uniform(0.2, 0.5)
                    
                    # 使用基类的smooth_move方法实现平滑移动
                    self.smooth_move(x, y, duration=move_duration)
                    
                    # 短暂随机停顿后点击
                    time.sleep(random.uniform(0.02, 0.08))
                else:
                    # 如果不使用人类行为模拟，直接设置鼠标位置
                    win32api.SetCursorPos((x, y))
                
                # 执行点击
                win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)
                # 随机按压时间
                time.sleep(random.uniform(0.03, 0.12))
                win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)
            
            # 使用随机延迟或指定延迟
            if delay is None:
                delay = random.uniform(0.1, 0.3)
            
            time.sleep(delay)
            return True
        except Exception as e:
            logging.error(f"点击操作失败: {e}")
            return False 