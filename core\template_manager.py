import cv2
import numpy as np
import os
import logging
import time
from typing import Tuple, Dict, Optional, List, Any, Union
import concurrent.futures

class TemplateManager:
    """常规模板管理器
    
    提供基于OpenCV的模板匹配功能
    """
    
    def __init__(self, base_resolution: Tuple[int, int] = (1920, 1080)):
        """初始化模板管理器
        
        Args:
            base_resolution: 基准分辨率, 默认1920x1080
        """
        self.templates = {}  # 保存所有模板图像
        self.rel_click_points = {}  # 保存相对点击位置
        self.base_resolution = base_resolution
        self.logger = logging.getLogger('阴阳师辅助工具.模板')
        self.template_cache = {}  # 模板匹配结果缓存
        self.cache_validity_time = 2.0  # 缓存有效期(秒)
        self.max_cache_size = 20  # 最大缓存条目数
    
    def load_template(self, template_name: str, template_path: str, rel_click_point: Tuple[float, float] = (0.5, 0.5)) -> bool:
        """加载模板图像
        
        Args:
            template_name: 模板名称
            template_path: 模板文件路径
            rel_click_point: 相对点击位置（范围0.0-1.0），默认为中心点(0.5, 0.5)
            
        Returns:
            bool: 是否加载成功
        """
        try:
            if not os.path.exists(template_path):
                self.logger.error(f"模板文件不存在: {template_path}")
                return False
                
            template = cv2.imread(template_path)
            if template is None:
                self.logger.error(f"无法读取模板文件: {template_path}")
                return False
                
            self.templates[template_name] = template
            self.rel_click_points[template_name] = rel_click_point
            
            # 清除该模板的缓存
            self._clear_template_cache(template_name)
            
            self.logger.info(f"加载模板: {template_name}, 尺寸: {template.shape[1]}x{template.shape[0]}, 点击位置: {rel_click_point}")
            return True
        except Exception as e:
            self.logger.error(f"加载模板失败: {str(e)}")
            return False
            
    def _clear_template_cache(self, template_name=None):
        """清除模板缓存
        
        Args:
            template_name: 要清除的模板名称，为None则清除所有
        """
        if template_name is None:
            self.template_cache.clear()
            self.logger.debug("清除所有模板缓存")
        elif template_name in self.template_cache:
            del self.template_cache[template_name]
            self.logger.debug(f"清除模板[{template_name}]缓存")
    
    def _manage_cache_size(self):
        """管理缓存大小，移除最旧的条目"""
        if len(self.template_cache) > self.max_cache_size:
            # 按时间戳排序
            sorted_cache = sorted(self.template_cache.items(), key=lambda x: x[1]['timestamp'])
            # 移除最旧的条目直到达到最大缓存容量
            while len(sorted_cache) > self.max_cache_size:
                oldest = sorted_cache.pop(0)
                del self.template_cache[oldest[0]]
                self.logger.debug(f"缓存满，移除最旧条目: {oldest[0]}")
            
    def _check_cache(self, image_hash, template_name, threshold):
        """检查缓存中是否有有效的匹配结果
        
        Args:
            image_hash: 图像哈希值
            template_name: 模板名称
            threshold: 匹配阈值
            
        Returns:
            Tuple或None: 如果缓存有效返回坐标点，否则返回None
        """
        cache_key = f"{image_hash}_{template_name}_{threshold}"
        
        if cache_key in self.template_cache:
            cache_entry = self.template_cache[cache_key]
            current_time = time.time()
            
            # 检查缓存是否过期
            if current_time - cache_entry['timestamp'] < self.cache_validity_time:
                self.logger.debug(f"使用缓存结果: {template_name}")
                return cache_entry['result']
            else:
                # 缓存已过期
                del self.template_cache[cache_key]
                
        return None
        
    def _update_cache(self, image_hash, template_name, threshold, result):
        """更新缓存
        
        Args:
            image_hash: 图像哈希值
            template_name: 模板名称
            threshold: 匹配阈值
            result: 匹配结果
        """
        cache_key = f"{image_hash}_{template_name}_{threshold}"
        
        # 添加新的缓存条目
        self.template_cache[cache_key] = {
            'timestamp': time.time(),
            'result': result
        }
        
        # 管理缓存大小
        self._manage_cache_size()
        
    def _compute_image_hash(self, image):
        """计算图像的简单哈希值
        
        Args:
            image: 图像数据
            
        Returns:
            str: 图像哈希值
        """
        # 使用图像尺寸和前几行像素的总和作为简单哈希
        try:
            h, w = image.shape[:2]
            sample_rows = min(5, h)  # 采样前5行或全部行（如果不足5行）
            pixel_sum = np.sum(image[:sample_rows, :, :])
            return f"{w}x{h}_{int(pixel_sum)}"
        except Exception as e:
            self.logger.error(f"计算图像哈希失败: {str(e)}")
            return str(hash(time.time()))  # 失败时返回随机哈希
            
    def find_template_in_image(self, image: np.ndarray, template_name: str, 
                              threshold: float = 0.7) -> Optional[Tuple[int, int]]:
        """在图像中查找模板
        
        Args:
            image: 待搜索图像
            template_name: 模板名称
            threshold: 匹配阈值
            
        Returns:
            tuple: 找到的中心点坐标 (x, y)，如果未找到则返回None
        """
        if template_name not in self.templates:
            self.logger.error(f"未找到模板: {template_name}")
            return None
            
        if image is None:
            self.logger.error("输入图像为空")
            return None
            
        template = self.templates[template_name]
        rel_click_point = self.rel_click_points.get(template_name, (0.5, 0.5))
        
        try:
            # 计算图像哈希并检查缓存
            image_hash = self._compute_image_hash(image)
            cached_result = self._check_cache(image_hash, template_name, threshold)
            if cached_result is not None:
                return cached_result
                
            # 获取模板尺寸
            h, w = template.shape[:2]
            
            # 模板匹配
            result = cv2.matchTemplate(image, template, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            if max_val >= threshold:
                # 计算相对点击位置
                click_x = int(max_loc[0] + w * rel_click_point[0])
                click_y = int(max_loc[1] + h * rel_click_point[1])
                
                position = (click_x, click_y)
                
                # 更新缓存
                self._update_cache(image_hash, template_name, threshold, position)
                
                self.logger.info(f"找到模板[{template_name}]: 位置[{click_x},{click_y}], 匹配度[{max_val:.3f}]")
                return position
            else:
                # 缓存未找到的结果
                self._update_cache(image_hash, template_name, threshold, None)
                
                self.logger.debug(f"未找到模板[{template_name}]: 最佳匹配度[{max_val:.3f}] < 阈值[{threshold}]")
                return None
        except Exception as e:
            self.logger.error(f"查找模板失败[{template_name}]: {str(e)}")
            return None
            
    def find_all_templates_in_image(self, image: np.ndarray, template_name: str, 
                                 threshold: float = 0.7, max_results: int = 5) -> List[Tuple[int, int]]:
        """查找图像中所有匹配的模板位置
        
        Args:
            image: 待搜索图像
            template_name: 模板名称
            threshold: 匹配阈值
            max_results: 最大结果数量
            
        Returns:
            list: 中心点坐标列表 [(x1, y1), (x2, y2), ...]
        """
        if template_name not in self.templates:
            self.logger.error(f"未找到模板: {template_name}")
            return []
            
        if image is None:
            self.logger.error("输入图像为空")
            return []
            
        template = self.templates[template_name]
        rel_click_point = self.rel_click_points.get(template_name, (0.5, 0.5))
        
        try:
            # 计算图像哈希并检查缓存
            image_hash = self._compute_image_hash(image)
            cache_key = f"{image_hash}_{template_name}_{threshold}_all_{max_results}"
            
            if cache_key in self.template_cache:
                cache_entry = self.template_cache[cache_key]
                current_time = time.time()
                
                # 检查缓存是否过期
                if current_time - cache_entry['timestamp'] < self.cache_validity_time:
                    self.logger.debug(f"使用缓存结果: {template_name} (所有匹配)")
                    return cache_entry['result']
                else:
                    # 缓存已过期
                    del self.template_cache[cache_key]
            
            # 获取模板尺寸
            h, w = template.shape[:2]
            
            # 模板匹配
            result = cv2.matchTemplate(image, template, cv2.TM_CCOEFF_NORMED)
            
            # 找到所有符合阈值的位置
            locations = np.where(result >= threshold)
            points = []
            
            # 转换为坐标列表
            for pt in zip(*locations[::-1]):  # 反转，从(y,x)变为(x,y)
                # 计算中心点
                click_x = int(pt[0] + w * rel_click_point[0])
                click_y = int(pt[1] + h * rel_click_point[1])
                
                # 匹配值
                match_val = result[pt[1], pt[0]]
                
                points.append((click_x, click_y, match_val))
            
            # 按匹配度排序并限制结果数量
            points.sort(key=lambda p: p[2], reverse=True)
            points = points[:max_results]
            
            # 只返回坐标
            result_points = [(p[0], p[1]) for p in points]
            
            # 更新缓存
            self.template_cache[cache_key] = {
                'timestamp': time.time(),
                'result': result_points
            }
            
            if result_points:
                self.logger.info(f"找到 {len(result_points)} 个模板[{template_name}]匹配")
            else:
                self.logger.debug(f"未找到模板[{template_name}]匹配")
                
            return result_points
        except Exception as e:
            self.logger.error(f"查找所有模板失败[{template_name}]: {str(e)}")
            return []
            
    def find_multiple_templates(self, image: np.ndarray, template_names: List[str], 
                              threshold: float = 0.7) -> Dict[str, Tuple[int, int]]:
        """查找图像中的多个模板（串行方式）
        
        Args:
            image: 待搜索图像
            template_names: 模板名称列表
            threshold: 匹配阈值
            
        Returns:
            dict: {模板名称: (x, y)} - 找到的中心点坐标
        """
        results = {}
        
        for template_name in template_names:
            position = self.find_template_in_image(image, template_name, threshold)
            if position:
                results[template_name] = position
                
        return results
        
    def find_multiple_templates_parallel(self, image: np.ndarray, template_names: List[str], 
                                       threshold: float = 0.7, max_workers: int = None) -> Dict[str, Tuple[int, int]]:
        """并行查找图像中的多个模板
        
        Args:
            image: 待搜索图像
            template_names: 模板名称列表
            threshold: 匹配阈值
            max_workers: 最大工作线程数，默认为None(由系统决定)
            
        Returns:
            dict: {模板名称: (x, y)} - 找到的中心点坐标
        """
        results = {}
        
        # 模板数量为0或1时，无需并行处理
        if not template_names:
            return results
            
        if len(template_names) == 1:
            position = self.find_template_in_image(image, template_names[0], threshold)
            if position:
                results[template_names[0]] = position
            return results
            
        # 保存原始图像，避免在多线程环境中共享
        image_copy = image.copy()
        
        # 使用线程池并行查找
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_template = {
                executor.submit(self.find_template_in_image, image_copy, name, threshold): name
                for name in template_names
            }
            
            # 收集结果
            for future in concurrent.futures.as_completed(future_to_template):
                template_name = future_to_template[future]
                try:
                    position = future.result()
                    if position:
                        results[template_name] = position
                except Exception as e:
                    self.logger.error(f"并行查找模板异常[{template_name}]: {str(e)}")
                    
        return results

    def match_template(self, template_name: str, screenshot: np.ndarray, threshold: float = None) -> Optional[Tuple[int, int]]:
        """在截图中查找模板
        
        Args:
            template_name: 模板名称
            screenshot: 截图图像
            threshold: 匹配阈值，如果为None则使用默认阈值
            
        Returns:
            如果找到模板，返回其中心点坐标；否则返回None
        """
        if template_name not in self.templates:
            logging.warning(f"模板 {template_name} 不存在")
            return None
        
        template = self.templates[template_name]
        if threshold is None:
            threshold = 0.7  # 使用默认阈值
        
        # 多尺度匹配
        scales = [0.8, 0.9, 1.0, 1.1, 1.2]
        best_match = None
        best_val = 0
        best_scale = 1.0
        
        for scale in scales:
            # 调整模板大小
            width = int(template.shape[1] * scale)
            height = int(template.shape[0] * scale)
            resized_template = cv2.resize(template, (width, height))
            
            # 模板匹配
            result = cv2.matchTemplate(screenshot, resized_template, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            if max_val > best_val:
                best_val = max_val
                best_match = max_loc
                best_scale = scale
            
        if best_val >= threshold:
            # 计算中心点
            template_width = int(template.shape[1] * best_scale)
            template_height = int(template.shape[0] * best_scale)
            center_x = best_match[0] + template_width // 2
            center_y = best_match[1] + template_height // 2
            return (center_x, center_y)
        
        return None 