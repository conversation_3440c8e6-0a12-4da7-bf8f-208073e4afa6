#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""最小化测试 - 只测试交互式网格配置"""

import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_interactive_config_only():
    """只测试交互式网格配置"""
    try:
        print("🖱️ 测试交互式网格配置...")
        
        from PyQt5.QtWidgets import QApplication
        from features.realm_raid.interactive_grid_config import InteractiveGridConfigDialog
        
        app = QApplication(sys.argv)
        app.setApplicationName("交互式网格配置测试")
        
        # 默认配置
        default_config = {
            "grid_area": {
                "left": 0.12,
                "top": 0.20,
                "right": 0.88,
                "bottom": 0.80
            },
            "layout": {
                "rows": 3,
                "columns": 3,
                "item_spacing": {
                    "horizontal": 0.02,
                    "vertical": 0.03
                }
            },
            "item_size": {
                "width": 0.22,
                "height": 0.15
            }
        }
        
        print("✅ 创建交互式配置对话框...")
        dialog = InteractiveGridConfigDialog(None, default_config)
        
        print("🖥️ 显示对话框...")
        result = dialog.exec_()
        
        if result == dialog.Accepted:
            config = dialog.get_config()
            print("✅ 用户保存了配置")
            print(f"📊 网格区域: {config['grid_area']}")
            print(f"📐 结界尺寸: {config['item_size']}")
        else:
            print("❌ 用户取消了配置")
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ 交互式配置测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def test_realm_raid_gui_only():
    """只测试结界突破GUI"""
    try:
        print("🏰 测试结界突破GUI...")
        
        from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
        from features.realm_raid.realm_raid_gui_simple import RealmRaidGUI
        
        app = QApplication(sys.argv)
        app.setApplicationName("结界突破GUI测试")
        
        # 创建主窗口
        main_window = QMainWindow()
        main_window.setWindowTitle("结界突破GUI测试")
        main_window.setGeometry(100, 100, 800, 600)
        
        # 创建结界突破GUI
        print("✅ 创建结界突破GUI...")
        realm_gui = RealmRaidGUI()
        
        # 设置为中央组件
        main_window.setCentralWidget(realm_gui)
        
        print("🖥️ 显示主窗口...")
        main_window.show()
        
        print("🔄 启动事件循环...")
        exit_code = app.exec_()
        
        print(f"✅ 结界突破GUI测试完成，退出代码: {exit_code}")
        return True
        
    except Exception as e:
        print(f"❌ 结界突破GUI测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("🧪 最小化功能测试")
    print("=" * 50)
    
    choice = input("选择测试项目:\n1. 交互式网格配置\n2. 结界突破GUI\n请输入 (1/2): ").strip()
    
    if choice == "1":
        print("\n🖱️ 开始交互式网格配置测试...")
        success = test_interactive_config_only()
    elif choice == "2":
        print("\n🏰 开始结界突破GUI测试...")
        success = test_realm_raid_gui_only()
    else:
        print("❌ 无效选择")
        return 1
    
    if success:
        print("✅ 测试成功完成！")
        return 0
    else:
        print("❌ 测试失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
