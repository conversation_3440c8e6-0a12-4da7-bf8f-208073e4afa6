# 3×3网格自定义配置指南

## 🎯 **功能概述**

现在您可以完全自定义结界突破的3×3网格配置，包括：
- 网格区域位置和大小
- 网格行列数量
- 结界项尺寸
- 间距设置

## 🔧 **如何使用**

### 1. **打开配置界面**
1. 启动程序：`python main_gui_v2.py`
2. 切换到"🏰 结界突破"标签页
3. 点击"网格配置"按钮

### 2. **配置参数说明**

#### **📐 网格区域 (相对坐标 0.0-1.0)**
- **左边界**: 网格区域距离屏幕左边的比例 (默认: 0.12 = 12%)
- **上边界**: 网格区域距离屏幕上边的比例 (默认: 0.20 = 20%)
- **右边界**: 网格区域距离屏幕右边的比例 (默认: 0.88 = 88%)
- **下边界**: 网格区域距离屏幕下边的比例 (默认: 0.80 = 80%)

#### **🗂️ 网格布局**
- **行数**: 网格的行数 (默认: 3行)
- **列数**: 网格的列数 (默认: 3列)
- **水平间距**: 结界项之间的水平间距 (默认: 0.02 = 2%)
- **垂直间距**: 结界项之间的垂直间距 (默认: 0.03 = 3%)

#### **📏 结界项尺寸**
- **宽度**: 单个结界项的宽度 (默认: 0.22 = 22%)
- **高度**: 单个结界项的高度 (默认: 0.15 = 15%)

### 3. **实时预览**
配置界面提供实时预览功能：
- 调整任何参数都会立即在预览区域显示效果
- 红色方块代表结界位置
- 数字显示结界索引 (0-8)

## 🎨 **常用配置示例**

### **标准3×3配置 (默认)**
```json
{
  "grid_area": {
    "left": 0.12,
    "top": 0.20,
    "right": 0.88,
    "bottom": 0.80
  },
  "layout": {
    "rows": 3,
    "columns": 3,
    "item_spacing": {
      "horizontal": 0.02,
      "vertical": 0.03
    }
  },
  "item_size": {
    "width": 0.22,
    "height": 0.15
  }
}
```

### **紧凑型配置**
```json
{
  "grid_area": {
    "left": 0.15,
    "top": 0.25,
    "right": 0.85,
    "bottom": 0.75
  },
  "layout": {
    "rows": 3,
    "columns": 3,
    "item_spacing": {
      "horizontal": 0.01,
      "vertical": 0.02
    }
  },
  "item_size": {
    "width": 0.20,
    "height": 0.12
  }
}
```

### **宽屏适配配置**
```json
{
  "grid_area": {
    "left": 0.10,
    "top": 0.15,
    "right": 0.90,
    "bottom": 0.85
  },
  "layout": {
    "rows": 3,
    "columns": 3,
    "item_spacing": {
      "horizontal": 0.03,
      "vertical": 0.04
    }
  },
  "item_size": {
    "width": 0.25,
    "height": 0.18
  }
}
```

## 🔧 **高级功能**

### **支持非3×3网格**
您可以配置其他网格布局：
- **2×2网格**: 4个结界位置
- **4×4网格**: 16个结界位置
- **3×4网格**: 12个结界位置
- **任意组合**: 最大支持5×5

### **多分辨率适配**
- 使用相对坐标系统，自动适配不同分辨率
- 1920×1080、1366×768、2560×1440等都能正确工作

### **配置保存**
- 配置自动保存到 `custom_grid_config.json`
- 同时更新 `realm_raid_config.py` 文件
- 重启程序后自动加载新配置

## 📋 **配置步骤**

### **第1步: 确定网格区域**
1. 启动游戏，进入结界突破界面
2. 观察结界列表的位置
3. 调整"网格区域"参数，使其覆盖结界列表

### **第2步: 调整网格布局**
1. 根据实际结界数量设置行列数
2. 调整间距，确保不重叠
3. 观察预览效果

### **第3步: 优化结界项尺寸**
1. 调整宽度和高度
2. 确保能完全覆盖结界图标
3. 避免过大导致误点

### **第4步: 测试验证**
1. 保存配置
2. 重启程序
3. 运行结界突破功能测试

## ⚠️ **注意事项**

### **配置原则**
- **网格区域**: 应该完全覆盖结界列表区域
- **结界项尺寸**: 应该能完全覆盖单个结界图标
- **间距设置**: 避免相邻结界重叠
- **边界检查**: 确保所有结界都在屏幕范围内

### **常见问题**
1. **点击位置不准确**
   - 调整网格区域位置
   - 优化结界项尺寸

2. **结界重叠**
   - 增加间距设置
   - 减小结界项尺寸

3. **超出屏幕范围**
   - 调整网格区域边界
   - 减少行列数量

## 🎯 **最佳实践**

### **配置流程**
1. **先截图**: 截取结界突破界面，分析布局
2. **粗调整**: 设置大致的网格区域
3. **细优化**: 精确调整尺寸和间距
4. **实际测试**: 运行功能验证效果

### **测试方法**
1. 启用调试模式
2. 观察点击位置是否准确
3. 检查所有结界是否能正确识别
4. 验证选择策略是否正常工作

## 🎊 **总结**

### ✅ **自定义网格的优势**
- **完美适配**: 适配任何分辨率和界面布局
- **灵活配置**: 支持各种网格布局
- **实时预览**: 所见即所得的配置体验
- **持久保存**: 配置永久保存，无需重复设置

### 🚀 **开始自定义**
现在您可以根据自己的游戏界面，完全自定义3×3网格配置，获得最佳的自动化体验！

**点击"网格配置"按钮，开始个性化定制吧！** 🎯
