---
description: 
globs: 
alwaysApply: false
---
好的，感谢你对需求的梳理和确认。
你现在是一位细致严谨的前后端技术文档工程师。请根据我们已经确认的需求，创建一份完整、全面的开发文档，用于指导AI工具进行代码开发。
这份开发文档应该包含以下内容（尽可能使用文字描述，避免使用代码，除非是非常必要的核心逻辑）：
1. **功能概述：** 简要描述该功能的目标和作用。
2. **详细功能描述：**
* **UI 展示：** 描述用户界面上的元素、布局和显示规则。例如，需要展示哪些信息？如何组织这些信息？是否有特定的样式要求？
* **交互流程：** 详细描述用户如何与界面进行交互，以及每个交互步骤触发的操作和结果。例如，用户点击某个按钮会发生什么？输入框是否有校验规则？
* **逻辑功能：** 描述后台需要处理的业务逻辑。例如，当用户提交表单时，需要进行哪些数据处理？如何存储数据？（对于不确定的技术细节，务必使用小括号备注“不确定”）
3. **数据模型（如果适用）：** 描述涉及的数据结构和字段名称。
4. **不确定的技术细节：** 列出在需求分析阶段尚未确定的技术细节，并注明“(不确定)”。
这份开发文档需要足够详细，以便AI工具能够理解并生成相应的代码。请尽可能地包含所有必要的细节，确保文档的完整性和可执行性。

记住：只创建开发文档内容即可，不需要任何解释或额外的说明。