import cv2
import numpy as np
import os
import logging
from typing import Tuple, List, Dict, Optional, Union
import concurrent.futures

class ImageUtils:
    """图像处理工具类
    
    提供图像识别、匹配和处理的方法
    """
    
    @staticmethod
    def load_template(template_path: str) -> Optional[np.ndarray]:
        """加载模板图像
        
        Args:
            template_path: 模板图像文件路径
            
        Returns:
            np.ndarray: 模板图像数据，如果加载失败则返回None
        """
        try:
            if not os.path.exists(template_path):
                logging.error(f"模板文件不存在: {template_path}")
                return None
                
            template = cv2.imread(template_path)
            if template is None:
                logging.error(f"无法读取模板文件: {template_path}")
                return None
                
            return template
        except Exception as e:
            logging.error(f"加载模板失败: {str(e)}")
            return None
    
    @staticmethod
    def match_template(image: np.ndarray, template: np.ndarray, threshold: float = 0.8) -> Optional[Tuple[int, int, float]]:
        """模板匹配
        
        Args:
            image: 待匹配图像
            template: 模板图像
            threshold: 匹配阈值
            
        Returns:
            tuple: (x, y, 匹配度) - 找到的位置和匹配度，未找到返回None
        """
        try:
            result = cv2.matchTemplate(image, template, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            if max_val >= threshold:
                # 获取模板尺寸
                h, w = template.shape[:2]
                # 返回模板中心位置和匹配度
                return (max_loc[0] + w // 2, max_loc[1] + h // 2, max_val)
            else:
                return None
        except Exception as e:
            logging.error(f"模板匹配失败: {str(e)}")
            return None
    
    @staticmethod
    def match_multiple_templates(image: np.ndarray, templates: Dict[str, np.ndarray], threshold: float = 0.8) -> Dict[str, Tuple[int, int, float]]:
        """匹配多个模板
        
        Args:
            image: 待匹配图像
            templates: 模板字典 {名称: 图像数据}
            threshold: 匹配阈值
            
        Returns:
            dict: {名称: (x, y, 匹配度)} - 找到的位置和匹配度
        """
        results = {}
        
        for name, template in templates.items():
            result = ImageUtils.match_template(image, template, threshold)
            if result:
                results[name] = result
        
        return results
    
    @staticmethod
    def match_multiple_templates_parallel(image: np.ndarray, templates: Dict[str, np.ndarray], threshold: float = 0.8, max_workers: int = None) -> Dict[str, Tuple[int, int, float]]:
        """并行匹配多个模板
        
        Args:
            image: 待匹配图像
            templates: 模板字典 {名称: 图像数据}
            threshold: 匹配阈值
            max_workers: 最大工作线程数，默认为None(由系统决定)
            
        Returns:
            dict: {名称: (x, y, 匹配度)} - 找到的位置和匹配度
        """
        results = {}
        
        def process_template(name_template_pair):
            name, template = name_template_pair
            result = ImageUtils.match_template(image, template, threshold)
            return name, result
        
        # 使用线程池并行处理
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_name = {
                executor.submit(process_template, (name, template)): name 
                for name, template in templates.items()
            }
            
            # 收集结果
            for future in concurrent.futures.as_completed(future_to_name):
                name, result = future.result()
                if result:
                    results[name] = result
        
        return results
    
    @staticmethod
    def find_all_matches(image: np.ndarray, template: np.ndarray, threshold: float = 0.8, max_results: int = 10) -> List[Tuple[int, int, float]]:
        """找出所有匹配位置
        
        Args:
            image: 待匹配图像
            template: 模板图像
            threshold: 匹配阈值
            max_results: 最大结果数量
            
        Returns:
            list: [(x, y, 匹配度), ...] - 所有匹配位置和匹配度
        """
        try:
            result = cv2.matchTemplate(image, template, cv2.TM_CCOEFF_NORMED)
            h, w = template.shape[:2]
            
            # 找出所有超过阈值的位置
            locations = np.where(result >= threshold)
            
            # 转换为坐标列表
            matches = []
            for pt in zip(*locations[::-1]):  # 反转以获得 (x, y) 而不是 (y, x)
                x, y = pt[0] + w // 2, pt[1] + h // 2
                max_val = result[pt[1], pt[0]]
                matches.append((x, y, float(max_val)))
            
            # 按匹配度排序
            matches.sort(key=lambda x: x[2], reverse=True)
            
            # 返回前N个结果
            return matches[:max_results]
        except Exception as e:
            logging.error(f"查找所有匹配失败: {str(e)}")
            return []
    
    @staticmethod
    def find_all_matches_parallel(image: np.ndarray, templates: Dict[str, np.ndarray], threshold: float = 0.8, max_results: int = 10, max_workers: int = None) -> Dict[str, List[Tuple[int, int, float]]]:
        """并行查找多个模板的所有匹配位置
        
        Args:
            image: 待匹配图像
            templates: 模板字典 {名称: 图像数据}
            threshold: 匹配阈值
            max_results: 每个模板的最大结果数量
            max_workers: 最大工作线程数，默认为None(由系统决定)
            
        Returns:
            dict: {名称: [(x1, y1, 匹配度1), (x2, y2, 匹配度2), ...]} - 所有找到的位置和匹配度
        """
        results = {}
        
        def process_template(name_template_pair):
            name, template = name_template_pair
            matches = ImageUtils.find_all_matches(image, template, threshold, max_results)
            return name, matches
        
        # 使用线程池并行处理
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_name = {
                executor.submit(process_template, (name, template)): name 
                for name, template in templates.items()
            }
            
            # 收集结果
            for future in concurrent.futures.as_completed(future_to_name):
                name, matches = future.result()
                if matches:
                    results[name] = matches
        
        return results
    
    @staticmethod
    def crop_image(image: np.ndarray, x: int, y: int, width: int, height: int) -> np.ndarray:
        """裁剪图像
        
        Args:
            image: 原始图像
            x: 左上角X坐标
            y: 左上角Y坐标
            width: 宽度
            height: 高度
            
        Returns:
            np.ndarray: 裁剪后的图像
        """
        try:
            h, w = image.shape[:2]
            
            # 确保裁剪区域在图像内
            x = max(0, min(x, w))
            y = max(0, min(y, h))
            width = max(0, min(width, w - x))
            height = max(0, min(height, h - y))
            
            return image[y:y+height, x:x+width]
        except Exception as e:
            logging.error(f"裁剪图像失败: {str(e)}")
            return image
    
    @staticmethod
    def draw_rectangle(image: np.ndarray, x: int, y: int, width: int, height: int, color: Tuple[int, int, int] = (0, 255, 0), thickness: int = 2) -> np.ndarray:
        """在图像上绘制矩形
        
        Args:
            image: 原始图像
            x: 左上角X坐标
            y: 左上角Y坐标
            width: 宽度
            height: 高度
            color: 颜色 (B, G, R)
            thickness: 线条粗细
            
        Returns:
            np.ndarray: 绘制后的图像
        """
        try:
            result = image.copy()
            cv2.rectangle(result, (x, y), (x + width, y + height), color, thickness)
            return result
        except Exception as e:
            logging.error(f"绘制矩形失败: {str(e)}")
            return image
    
    @staticmethod
    def save_image(image: np.ndarray, file_path: str) -> bool:
        """保存图像
        
        Args:
            image: 图像数据
            file_path: 保存路径
            
        Returns:
            bool: 是否保存成功
        """
        try:
            # 确保目录存在
            directory = os.path.dirname(file_path)
            if directory and not os.path.exists(directory):
                os.makedirs(directory)
                
            cv2.imwrite(file_path, image)
            return True
        except Exception as e:
            logging.error(f"保存图像失败: {str(e)}")
            return False 