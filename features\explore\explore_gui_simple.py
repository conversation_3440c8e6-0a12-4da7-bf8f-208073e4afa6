"""探索副本GUI界面 - 简化版"""

import logging
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QCheckBox, QGroupBox,
                            QSpinBox, QComboBox, QMessageBox,
                            QSlider, QRadioButton, QButtonGroup)
from PyQt5.QtCore import QThread, pyqtSignal
import traceback

from features.explore.explore_bot import ExploreBot


class ExploreThread(QThread):
    """后台运行探索脚本的线程"""
    
    update_signal = pyqtSignal(str)  # 更新日志信号
    stats_signal = pyqtSignal(dict)  # 更新统计数据信号
    
    def __init__(self, bot, parent=None):
        super().__init__(parent)
        self.bot = bot
        self.running = False
    
    def run(self):
        """运行线程"""
        self.running = True
        try:
            logging.info("🧵 探索后台线程开始运行")
            self.update_signal.emit("🚀 开始探索...")
            self.bot.start(callback=self.update_signal.emit)
            logging.info("✅ 探索流程正常结束")
        except Exception as e:
            error_msg = f"探索线程发生错误: {str(e)}"
            logging.error(f"❌ {error_msg}")
            logging.error(f"错误详情: {traceback.format_exc()}")
            self.update_signal.emit(f"❌ 发生错误: {str(e)}")
        finally:
            self.running = False
            logging.info("🧵 探索后台线程结束")
            self.update_signal.emit("🛑 探索停止")
    
    def stop(self):
        """停止线程"""
        logging.info("🛑 用户请求停止探索线程")
        self.running = False
        if self.bot:
            self.bot.running = False
        self.update_signal.emit("🛑 停止探索")


class ExploreGUI(QWidget):
    """探索功能的GUI组件 - 简化版"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.explore_bot = None
        self.explore_thread = None
        self.is_running = False
        self.setup_ui()
        logging.info("🎮 探索GUI组件初始化完成")
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # 探索设置组
        settings_group = QGroupBox("探索设置")
        settings_layout = QVBoxLayout()
        settings_group.setLayout(settings_layout)
        
        # 探索类型选择
        type_layout = QHBoxLayout()
        type_layout.addWidget(QLabel("探索类型:"))
        self.explore_type_combo = QComboBox()

        # 设置固定宽度和强制样式确保显示完整
        self.explore_type_combo.setFixedWidth(800)
        self.explore_type_combo.setStyleSheet("""
            QComboBox {
                width: 800px !important;
                min-width: 800px !important;
                max-width: 800px !important;
                border: 3px solid red !important;
                background-color: yellow !important;
                font-size: 16px !important;
                padding: 8px !important;
            }
            QComboBox QAbstractItemView {
                width: 800px !important;
                min-width: 800px !important;
                font-size: 16px !important;
                border: 2px solid blue !important;
                background-color: lightyellow !important;
            }
        """)

        # 使用数字显示避免中文显示问题
        for i in range(1, 29):
            self.explore_type_combo.addItem(f"第{i}章", i)

        self.explore_type_combo.setCurrentText("第28章")
        print(f"🔧 DEBUG: 探索类型下拉框宽度已设置为800px，应该有红色边框和黄色背景")

        type_layout.addWidget(self.explore_type_combo)
        type_layout.addStretch()
        settings_layout.addLayout(type_layout)
        
        # 探索次数设置
        count_layout = QHBoxLayout()
        count_layout.addWidget(QLabel("探索次数:"))
        self.explore_count_spin = QSpinBox()
        self.explore_count_spin.setRange(1, 1000)
        self.explore_count_spin.setValue(100)
        count_layout.addWidget(self.explore_count_spin)
        count_layout.addStretch()
        settings_layout.addLayout(count_layout)
        
        # 体力设置
        stamina_layout = QHBoxLayout()
        self.use_stamina_check = QCheckBox("使用体力药")
        stamina_layout.addWidget(self.use_stamina_check)
        
        stamina_layout.addWidget(QLabel("保留体力:"))
        self.reserve_stamina_spin = QSpinBox()
        self.reserve_stamina_spin.setRange(0, 999)
        self.reserve_stamina_spin.setValue(0)
        stamina_layout.addWidget(self.reserve_stamina_spin)
        stamina_layout.addStretch()
        settings_layout.addLayout(stamina_layout)
        
        layout.addWidget(settings_group)
        
        # 高级设置组
        advanced_group = QGroupBox("高级设置")
        advanced_layout = QVBoxLayout()
        advanced_group.setLayout(advanced_layout)
        
        # 战斗设置
        battle_layout = QHBoxLayout()
        self.auto_battle_check = QCheckBox("自动战斗")
        self.auto_battle_check.setChecked(True)
        battle_layout.addWidget(self.auto_battle_check)
        
        self.quick_battle_check = QCheckBox("快速战斗")
        battle_layout.addWidget(self.quick_battle_check)
        battle_layout.addStretch()
        advanced_layout.addLayout(battle_layout)
        
        # 延迟设置
        delay_layout = QHBoxLayout()
        delay_layout.addWidget(QLabel("操作延迟:"))
        self.delay_slider = QSlider()
        self.delay_slider.setOrientation(1)  # 水平方向
        self.delay_slider.setRange(500, 3000)
        self.delay_slider.setValue(1000)
        self.delay_slider.valueChanged.connect(self.update_delay_label)
        delay_layout.addWidget(self.delay_slider)
        
        self.delay_label = QLabel("1.0秒")
        delay_layout.addWidget(self.delay_label)
        advanced_layout.addLayout(delay_layout)
        
        # 错误处理
        error_layout = QHBoxLayout()
        self.auto_recovery_check = QCheckBox("自动错误恢复")
        self.auto_recovery_check.setChecked(True)
        error_layout.addWidget(self.auto_recovery_check)
        
        self.screenshot_on_error_check = QCheckBox("错误时截图")
        self.screenshot_on_error_check.setChecked(True)
        error_layout.addWidget(self.screenshot_on_error_check)
        error_layout.addStretch()
        advanced_layout.addLayout(error_layout)
        
        layout.addWidget(advanced_group)
        
        # 控制按钮组
        control_group = QGroupBox("控制")
        control_layout = QHBoxLayout()
        control_group.setLayout(control_layout)
        
        self.start_button = QPushButton("开始探索")
        self.start_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        self.start_button.clicked.connect(self.start_explore)
        control_layout.addWidget(self.start_button)
        
        self.stop_button = QPushButton("停止探索")
        self.stop_button.setStyleSheet("QPushButton { background-color: #f44336; color: white; font-weight: bold; }")
        self.stop_button.setEnabled(False)
        self.stop_button.clicked.connect(self.stop_explore)
        control_layout.addWidget(self.stop_button)
        
        self.test_button = QPushButton("测试连接")
        self.test_button.clicked.connect(self.test_connection)
        control_layout.addWidget(self.test_button)
        
        layout.addWidget(control_group)
        
        # 状态显示
        status_group = QGroupBox("运行状态")
        status_layout = QVBoxLayout()
        status_group.setLayout(status_layout)
        
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("QLabel { font-size: 14px; font-weight: bold; }")
        status_layout.addWidget(self.status_label)
        
        layout.addWidget(status_group)
        
        layout.addStretch()
    
    def update_delay_label(self, value):
        """更新延迟标签"""
        self.delay_label.setText(f"{value/1000:.1f}秒")
    
    def get_main_window(self):
        """获取主窗口实例"""
        parent = self.parent()
        while parent:
            if hasattr(parent, 'get_current_hwnd'):
                return parent
            parent = parent.parent()
        return None
    
    def start_explore(self):
        """开始探索"""
        try:
            logging.info("🚀 用户点击开始探索按钮")
            
            # 获取主窗口的窗口句柄
            main_window = self.get_main_window()
            if not main_window:
                error_msg = "无法获取主窗口实例"
                logging.error(f"❌ {error_msg}")
                QMessageBox.critical(self, "错误", error_msg)
                return
            
            hwnd = main_window.get_current_hwnd()
            if not hwnd:
                error_msg = "请先在左侧选择游戏窗口"
                logging.error(f"❌ 启动失败: {error_msg}")
                QMessageBox.critical(self, "错误", error_msg)
                return
            
            logging.info(f"✅ 使用窗口句柄: {hwnd}")
            
            # 创建机器人实例
            logging.info("🤖 正在创建探索机器人实例...")
            self.explore_bot = ExploreBot(hwnd)
            
            # 应用用户设置
            settings = self.get_current_settings()
            logging.info(f"⚙️ 应用用户设置: {settings}")
            self.explore_bot.apply_settings(settings)
            
            # 更新界面状态
            self.is_running = True
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.status_label.setText("运行中...")
            logging.info("🔄 界面状态已更新")
            
            # 启动探索线程
            logging.info("🧵 启动探索后台线程...")
            self.explore_thread = ExploreThread(self.explore_bot)
            self.explore_thread.update_signal.connect(self.on_update)
            self.explore_thread.stats_signal.connect(self.on_stats_update)
            self.explore_thread.finished.connect(self.on_explore_finished)
            self.explore_thread.start()
            
            logging.info("✅ 探索启动成功")
            
        except Exception as e:
            error_msg = f"启动失败: {e}"
            logging.error(f"❌ {error_msg}")
            logging.error(f"错误详情: {traceback.format_exc()}")
            QMessageBox.critical(self, "错误", error_msg)
            self.reset_ui_state()
    
    def stop_explore(self):
        """停止探索"""
        try:
            logging.info("🛑 用户点击停止探索按钮")
            
            if self.explore_thread and self.explore_thread.isRunning():
                logging.info("⏹️ 正在停止探索线程...")
                self.explore_thread.stop()
                
                if self.explore_thread.wait(5000):
                    logging.info("✅ 探索线程已安全停止")
                else:
                    logging.warning("⚠️ 线程停止超时，强制终止")
            
            self.reset_ui_state()
            logging.info("🛑 探索已停止")
            
        except Exception as e:
            error_msg = f"停止探索失败: {e}"
            logging.error(f"❌ {error_msg}")
            logging.error(f"错误详情: {traceback.format_exc()}")
    
    def test_connection(self):
        """测试连接"""
        try:
            logging.info("🔧 用户点击测试连接按钮")
            
            main_window = self.get_main_window()
            if not main_window:
                error_msg = "无法获取主窗口实例"
                logging.error(f"❌ {error_msg}")
                QMessageBox.critical(self, "错误", error_msg)
                return
            
            hwnd = main_window.get_current_hwnd()
            if not hwnd:
                error_msg = "请先在左侧选择游戏窗口"
                logging.error(f"❌ 测试连接失败: {error_msg}")
                QMessageBox.critical(self, "错误", error_msg)
                return
            
            logging.info(f"🔍 开始测试窗口连接，句柄: {hwnd}")
            
            # 创建临时机器人实例进行测试
            logging.info("🤖 创建临时机器人实例进行测试...")
            test_bot = ExploreBot(hwnd)
            
            # 测试截图功能
            logging.info("📸 测试截图功能...")
            screenshot = test_bot.take_screenshot()
            
            if screenshot is not None:
                success_msg = "连接测试成功！可以正常截图。"
                logging.info(f"✅ {success_msg}")
                QMessageBox.information(self, "成功", success_msg)
            else:
                error_msg = "连接测试失败！无法截图。"
                logging.error(f"❌ {error_msg}")
                QMessageBox.critical(self, "失败", error_msg)
                
        except Exception as e:
            error_msg = f"测试连接失败: {e}"
            logging.error(f"❌ {error_msg}")
            logging.error(f"错误详情: {traceback.format_exc()}")
            QMessageBox.critical(self, "错误", error_msg)
    
    def get_current_settings(self):
        """获取当前设置"""
        return {
            'explore_type': self.explore_type_combo.currentText(),
            'explore_count': self.explore_count_spin.value(),
            'use_stamina': self.use_stamina_check.isChecked(),
            'reserve_stamina': self.reserve_stamina_spin.value(),
            'auto_battle': self.auto_battle_check.isChecked(),
            'quick_battle': self.quick_battle_check.isChecked(),
            'operation_delay': self.delay_slider.value(),
            'auto_recovery': self.auto_recovery_check.isChecked(),
            'screenshot_on_error': self.screenshot_on_error_check.isChecked()
        }
    
    def on_update(self, message):
        """更新回调"""
        # 这里可以发送信号到主窗口的日志组件
        logging.info(f"探索更新: {message}")
    
    def on_stats_update(self, stats):
        """统计更新回调"""
        # 这里可以更新状态显示
        pass
    
    def on_explore_finished(self):
        """探索结束回调"""
        logging.info("🏁 探索流程结束回调")
        self.reset_ui_state()
    
    def reset_ui_state(self):
        """重置界面状态"""
        logging.debug("🔄 重置探索GUI界面状态")
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.status_label.setText("就绪")
        self.is_running = False
