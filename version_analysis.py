#!/usr/bin/env python3
"""项目版本分析和分离"""

import os
import shutil
from datetime import datetime

def analyze_main_programs():
    """分析主程序文件"""
    print("🔍 分析主程序文件")
    print("=" * 40)
    
    main_files = {
        'main.py': '命令行版本',
        'main_gui.py': '旧版GUI (v1)',
        'main_gui_v2.py': '新版GUI (v2)'
    }
    
    for file, description in main_files.items():
        if os.path.exists(file):
            with open(file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            print(f"📁 {file} ({description})")
            print(f"   行数: {len(lines)}")
            
            # 查看导入的模块
            imports = []
            for line in lines[:20]:  # 只看前20行
                if 'import' in line and ('explore' in line or 'realm_raid' in line):
                    imports.append(line.strip())
            
            if imports:
                print(f"   关键导入:")
                for imp in imports:
                    print(f"     {imp}")
            print()

def analyze_feature_modules():
    """分析功能模块"""
    print("🔍 分析功能模块")
    print("=" * 40)
    
    features_path = 'features'
    if os.path.exists(features_path):
        for feature in os.listdir(features_path):
            feature_path = os.path.join(features_path, feature)
            if os.path.isdir(feature_path) and not feature.startswith('__'):
                print(f"📂 {feature}/")
                
                # 查看该功能模块的文件
                files = []
                for file in os.listdir(feature_path):
                    if file.endswith('.py') and not file.startswith('__'):
                        files.append(file)
                
                # 分类文件
                gui_files = [f for f in files if 'gui' in f]
                bot_files = [f for f in files if 'bot' in f]
                config_files = [f for f in files if 'config' in f]
                other_files = [f for f in files if f not in gui_files + bot_files + config_files]
                
                if gui_files:
                    print(f"   GUI文件: {', '.join(gui_files)}")
                if bot_files:
                    print(f"   Bot文件: {', '.join(bot_files)}")
                if config_files:
                    print(f"   配置文件: {', '.join(config_files)}")
                if other_files:
                    print(f"   其他文件: {', '.join(other_files)}")
                print()

def identify_version_files():
    """识别版本文件"""
    print("🔍 识别新旧版本文件")
    print("=" * 40)
    
    version_mapping = {
        'v1_files': {
            'description': '旧版本文件 (v1)',
            'files': [
                'main_gui.py',
                'features/explore/explore_gui.py',
                'features/realm_raid/realm_raid_gui.py'
            ]
        },
        'v2_files': {
            'description': '新版本文件 (v2)',
            'files': [
                'main_gui_v2.py',
                'core/main_window.py',
                'features/explore/explore_gui_simple.py',
                'features/realm_raid/realm_raid_gui_simple.py'
            ]
        },
        'common_files': {
            'description': '通用文件 (两个版本共用)',
            'files': [
                'main.py',
                'core/',
                'features/explore/explore_bot.py',
                'features/explore/explore_config.py',
                'features/realm_raid/realm_raid_bot.py',
                'templates/',
                'config.json'
            ]
        }
    }
    
    for version, info in version_mapping.items():
        print(f"📋 {info['description']}")
        for file_path in info['files']:
            exists = "✅" if os.path.exists(file_path) else "❌"
            print(f"   {exists} {file_path}")
        print()
    
    return version_mapping

def create_version_separation_plan():
    """创建版本分离计划"""
    print("📋 创建版本分离计划")
    print("=" * 40)
    
    separation_plan = {
        'v1_legacy': {
            'description': '旧版本 (Legacy)',
            'target_dir': 'legacy_v1',
            'files': [
                'main_gui.py',
                'features/explore/explore_gui.py',
                'features/realm_raid/realm_raid_gui.py'
            ]
        },
        'v2_current': {
            'description': '新版本 (Current)',
            'target_dir': '.',  # 保留在当前目录
            'files': [
                'main_gui_v2.py',
                'core/main_window.py',
                'features/explore/explore_gui_simple.py',
                'features/realm_raid/realm_raid_gui_simple.py'
            ]
        },
        'cleanup_files': {
            'description': '需要清理的文件',
            'files': [
                'final_width_test.py',
                'verify_correct_file_fix.py',
                'test_scripts_backup/',
                'debug_screenshots/',
                'cursor-memory-bank/'
            ]
        }
    }
    
    for category, info in separation_plan.items():
        print(f"📂 {info['description']}")
        if 'target_dir' in info:
            print(f"   目标目录: {info['target_dir']}")
        print(f"   文件列表:")
        for file_path in info['files']:
            exists = "✅" if os.path.exists(file_path) else "❌"
            print(f"     {exists} {file_path}")
        print()
    
    return separation_plan

def execute_separation(plan, dry_run=True):
    """执行版本分离"""
    print(f"🔧 {'模拟' if dry_run else '执行'}版本分离")
    print("=" * 40)
    
    if not dry_run:
        # 创建备份目录
        backup_dir = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        os.makedirs(backup_dir, exist_ok=True)
        print(f"📦 创建备份目录: {backup_dir}")
    
    # 处理旧版本文件
    legacy_info = plan['v1_legacy']
    legacy_dir = legacy_info['target_dir']
    
    print(f"📁 处理旧版本文件 -> {legacy_dir}/")
    
    if not dry_run:
        os.makedirs(legacy_dir, exist_ok=True)
        os.makedirs(f"{legacy_dir}/features/explore", exist_ok=True)
        os.makedirs(f"{legacy_dir}/features/realm_raid", exist_ok=True)
    
    for file_path in legacy_info['files']:
        if os.path.exists(file_path):
            target_path = os.path.join(legacy_dir, file_path)
            action = "移动" if not dry_run else "将移动"
            print(f"   {action}: {file_path} -> {target_path}")
            
            if not dry_run:
                # 创建目标目录
                os.makedirs(os.path.dirname(target_path), exist_ok=True)
                # 移动文件
                shutil.move(file_path, target_path)
    
    # 处理清理文件
    cleanup_info = plan['cleanup_files']
    print(f"🗑️ 清理不需要的文件")
    
    for file_path in cleanup_info['files']:
        if os.path.exists(file_path):
            action = "删除" if not dry_run else "将删除"
            print(f"   {action}: {file_path}")
            
            if not dry_run:
                if os.path.isdir(file_path):
                    shutil.rmtree(file_path)
                else:
                    os.remove(file_path)
    
    print(f"\n{'✅ 模拟完成' if dry_run else '✅ 分离完成'}")

def create_version_readme():
    """创建版本说明文件"""
    readme_content = """# 项目版本说明

## 当前版本结构

### 新版本 (v2) - 推荐使用
- **主程序**: `main_gui_v2.py`
- **架构**: 基于 `core/main_window.py` 的统一主窗口
- **探索GUI**: `features/explore/explore_gui_simple.py`
- **结界突破GUI**: `features/realm_raid/realm_raid_gui_simple.py`
- **特点**: 简洁界面、统一管理、更好的用户体验

### 旧版本 (v1) - 已移至 legacy_v1/
- **主程序**: `legacy_v1/main_gui.py`
- **探索GUI**: `legacy_v1/features/explore/explore_gui.py`
- **结界突破GUI**: `legacy_v1/features/realm_raid/realm_raid_gui.py`
- **特点**: 功能完整但界面复杂

### 命令行版本
- **主程序**: `main.py`
- **用途**: 命令行操作，适合自动化脚本

## 使用建议

1. **日常使用**: 运行 `main_gui_v2.py`
2. **命令行**: 运行 `main.py`
3. **旧版本**: 如需使用旧版本，运行 `legacy_v1/main_gui.py`

## 文件说明

### 核心文件 (两个版本共用)
- `core/` - 核心功能模块
- `features/*/explore_bot.py` - 探索机器人
- `features/*/explore_config.py` - 配置文件
- `templates/` - 模板图像
- `config.json` - 全局配置

### 版本特定文件
- v2版本使用 `*_simple.py` 文件
- v1版本使用完整功能的 `*.py` 文件
"""
    
    return readme_content

if __name__ == "__main__":
    print("🚀 项目版本分析和分离工具")
    print("=" * 50)
    
    # 分析主程序
    analyze_main_programs()
    
    # 分析功能模块
    analyze_feature_modules()
    
    # 识别版本文件
    version_mapping = identify_version_files()
    
    # 创建分离计划
    separation_plan = create_version_separation_plan()
    
    # 询问是否执行分离
    print("🤔 是否执行版本分离？")
    print("1. 模拟运行 (推荐，先看看会做什么)")
    print("2. 实际执行 (真正分离文件)")
    print("3. 只生成README")
    print("4. 退出")
    
    choice = input("请选择 (1-4): ").strip()
    
    if choice == "1":
        execute_separation(separation_plan, dry_run=True)
    elif choice == "2":
        confirm = input("确认要实际执行分离吗？(yes/no): ").strip().lower()
        if confirm == "yes":
            execute_separation(separation_plan, dry_run=False)
            
            # 创建README
            readme_content = create_version_readme()
            with open("VERSION_README.md", "w", encoding="utf-8") as f:
                f.write(readme_content)
            print("📝 已创建 VERSION_README.md")
        else:
            print("❌ 取消执行")
    elif choice == "3":
        readme_content = create_version_readme()
        with open("VERSION_README.md", "w", encoding="utf-8") as f:
            f.write(readme_content)
        print("📝 已创建 VERSION_README.md")
    else:
        print("👋 退出")
