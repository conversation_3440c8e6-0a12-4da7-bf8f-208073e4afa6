#!/usr/bin/env python3
"""测试增强版OCR效果"""

import cv2
import numpy as np
import logging
import os
import time
from core.ocr_utils import OCRUtils

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_enhanced_ocr():
    """测试增强版OCR"""
    print("🚀 测试增强版OCR效果")
    print("=" * 50)
    
    # 初始化OCR
    ocr = OCRUtils()
    
    if not ocr.is_available():
        print("❌ OCR不可用，请检查安装")
        return
    
    print(f"✅ OCR初始化成功")
    print(f"可用引擎: {list(ocr.engines.keys()) if hasattr(ocr, 'engines') else ['easyocr']}")
    
    # 查找测试图像
    test_images = []
    
    # 1. 查找debug截图
    debug_dir = "debug_screenshots"
    if os.path.exists(debug_dir):
        for file in os.listdir(debug_dir):
            if file.endswith('.png') and 'runtime_ocr' in file:
                test_images.append(os.path.join(debug_dir, file))
    
    if not test_images:
        print("⚠️ 没有找到测试图像")
        print("请先运行游戏并尝试章节选择，会自动生成测试图像")
        return
    
    # 选择最新的几个图像进行测试
    test_images.sort(key=lambda x: os.path.getmtime(x), reverse=True)
    test_images = test_images[:3]  # 只测试最新的3个
    
    print(f"📸 找到 {len(test_images)} 个测试图像")
    
    for i, image_path in enumerate(test_images, 1):
        print(f"\n🔍 测试图像 {i}: {os.path.basename(image_path)}")
        print("-" * 40)
        
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            print(f"❌ 无法读取图像: {image_path}")
            continue
        
        print(f"图像尺寸: {image.shape}")
        
        # 测试原有方法（如果可用）
        if hasattr(ocr, 'reader') and ocr.reader:
            print("\n📊 原有EasyOCR方法:")
            try:
                start_time = time.time()
                old_results = ocr.reader.readtext(image)
                old_time = time.time() - start_time
                
                print(f"  识别时间: {old_time:.2f}秒")
                print(f"  识别结果: {len(old_results)} 个文本")
                
                for j, (bbox, text, confidence) in enumerate(old_results):
                    if confidence > 0.5:  # 只显示高置信度结果
                        print(f"    {j+1}. '{text}' (置信度: {confidence:.3f})")
                        
            except Exception as e:
                print(f"  ❌ 原有方法失败: {e}")
        
        # 测试增强方法
        if hasattr(ocr, 'recognize_enhanced'):
            print("\n🚀 增强OCR方法:")
            try:
                start_time = time.time()
                enhanced_results = ocr.recognize_enhanced(image)
                enhanced_time = time.time() - start_time
                
                print(f"  识别时间: {enhanced_time:.2f}秒")
                print(f"  识别结果: {len(enhanced_results)} 个章节")
                
                for j, (chapter, bbox) in enumerate(enhanced_results):
                    print(f"    {j+1}. 第{chapter}章 位置: {bbox}")
                    
            except Exception as e:
                print(f"  ❌ 增强方法失败: {e}")
        
        # 测试原有章节识别方法
        print("\n📋 原有章节识别方法:")
        try:
            start_time = time.time()
            old_chapter_results = ocr.recognize_chapter_numbers(image)
            old_chapter_time = time.time() - start_time
            
            print(f"  识别时间: {old_chapter_time:.2f}秒")
            print(f"  识别结果: {len(old_chapter_results)} 个章节")
            
            for j, (chapter, bbox) in enumerate(old_chapter_results):
                print(f"    {j+1}. 第{chapter}章 位置: {bbox}")
                
        except Exception as e:
            print(f"  ❌ 原有章节识别失败: {e}")

def create_test_image():
    """创建测试图像"""
    print("\n🎨 创建测试图像")
    print("=" * 30)
    
    # 创建一个包含章节文字的测试图像
    image = np.ones((400, 300, 3), dtype=np.uint8) * 255  # 白色背景
    
    # 添加一些章节文字
    font = cv2.FONT_HERSHEY_SIMPLEX
    chapters = ["第二十一章", "第二十二章", "第二十三章", "第二十四章", "第二十五章"]
    
    for i, chapter in enumerate(chapters):
        y = 50 + i * 60
        cv2.putText(image, chapter, (50, y), font, 1, (0, 0, 0), 2)
    
    # 保存测试图像
    test_dir = "test_images"
    os.makedirs(test_dir, exist_ok=True)
    test_path = os.path.join(test_dir, "test_chapters.png")
    cv2.imwrite(test_path, image)
    
    print(f"✅ 测试图像已保存: {test_path}")
    
    # 测试这个图像
    ocr = OCRUtils()
    if ocr.is_available():
        print("\n🔍 测试创建的图像:")
        
        if hasattr(ocr, 'recognize_enhanced'):
            results = ocr.recognize_enhanced(image)
            print(f"增强OCR识别结果: {len(results)} 个章节")
            for chapter, bbox in results:
                print(f"  第{chapter}章 位置: {bbox}")
        
        old_results = ocr.recognize_chapter_numbers(image)
        print(f"原有OCR识别结果: {len(old_results)} 个章节")
        for chapter, bbox in old_results:
            print(f"  第{chapter}章 位置: {bbox}")

def compare_performance():
    """性能对比测试"""
    print("\n⚡ 性能对比测试")
    print("=" * 30)
    
    ocr = OCRUtils()
    if not ocr.is_available():
        print("❌ OCR不可用")
        return
    
    # 创建测试图像
    test_image = np.ones((500, 400, 3), dtype=np.uint8) * 240
    font = cv2.FONT_HERSHEY_SIMPLEX
    
    chapters = [
        "第十八章", "第十九章", "第二十章", 
        "第二十一章", "第二十二章", "第二十三章",
        "第二十四章", "第二十五章", "第二十六章"
    ]
    
    for i, chapter in enumerate(chapters):
        y = 40 + (i % 3) * 150
        x = 50 + (i // 3) * 120
        cv2.putText(test_image, chapter, (x, y), font, 0.7, (0, 0, 0), 2)
    
    print(f"测试图像尺寸: {test_image.shape}")
    print(f"包含章节: {len(chapters)} 个")
    
    # 多次测试取平均值
    test_rounds = 3
    
    # 测试原有方法
    if hasattr(ocr, 'recognize_chapter_numbers'):
        print(f"\n📊 原有方法 ({test_rounds} 次测试):")
        old_times = []
        old_results_count = []
        
        for i in range(test_rounds):
            start_time = time.time()
            results = ocr.recognize_chapter_numbers(test_image)
            elapsed = time.time() - start_time
            
            old_times.append(elapsed)
            old_results_count.append(len(results))
            print(f"  第{i+1}次: {elapsed:.3f}秒, {len(results)}个结果")
        
        avg_old_time = sum(old_times) / len(old_times)
        avg_old_count = sum(old_results_count) / len(old_results_count)
        print(f"  平均时间: {avg_old_time:.3f}秒")
        print(f"  平均识别数: {avg_old_count:.1f}个")
    
    # 测试增强方法
    if hasattr(ocr, 'recognize_enhanced'):
        print(f"\n🚀 增强方法 ({test_rounds} 次测试):")
        new_times = []
        new_results_count = []
        
        for i in range(test_rounds):
            start_time = time.time()
            results = ocr.recognize_enhanced(test_image)
            elapsed = time.time() - start_time
            
            new_times.append(elapsed)
            new_results_count.append(len(results))
            print(f"  第{i+1}次: {elapsed:.3f}秒, {len(results)}个结果")
        
        avg_new_time = sum(new_times) / len(new_times)
        avg_new_count = sum(new_results_count) / len(new_results_count)
        print(f"  平均时间: {avg_new_time:.3f}秒")
        print(f"  平均识别数: {avg_new_count:.1f}个")
        
        # 性能对比
        if 'avg_old_time' in locals():
            time_ratio = avg_new_time / avg_old_time
            count_ratio = avg_new_count / avg_old_count if avg_old_count > 0 else 0
            
            print(f"\n📈 性能对比:")
            print(f"  时间比例: {time_ratio:.2f}x ({'更快' if time_ratio < 1 else '更慢'})")
            print(f"  识别数比例: {count_ratio:.2f}x ({'更多' if count_ratio > 1 else '更少'})")

def main():
    """主函数"""
    print("🧪 增强版OCR测试套件")
    print("=" * 60)
    
    # 测试1: 基本功能测试
    test_enhanced_ocr()
    
    # 测试2: 创建测试图像
    create_test_image()
    
    # 测试3: 性能对比
    compare_performance()
    
    print(f"\n🎯 测试完成！")
    print("=" * 60)
    print("📋 总结:")
    print("• 如果增强OCR识别数量更多，说明改进有效")
    print("• 如果识别时间合理，说明性能可接受")
    print("• 建议在实际游戏中测试章节选择功能")

if __name__ == "__main__":
    main()
