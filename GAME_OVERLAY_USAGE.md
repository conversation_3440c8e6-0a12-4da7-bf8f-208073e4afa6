# 游戏界面覆盖调整工具使用指南

## 🎯 **功能概述**

这是您要的功能！直接在游戏界面上显示网格覆盖层，实时调整网格配置，所见即所得！

## 🚀 **启动方法**

### **方法一：从主界面启动 (推荐)**
1. 运行 `python main_gui_v2.py`
2. 切换到"🏰 结界突破"标签页
3. 点击"界面映射"按钮

### **方法二：直接启动**
```bash
python features/realm_raid/game_overlay_tool.py
```

## 🎮 **使用步骤**

### **第1步：连接游戏窗口**
1. **启动阴阳师游戏**
2. **进入结界突破界面**
3. **点击"🎮 连接游戏窗口"按钮**
4. **确认显示"✅ 已连接"状态**

### **第2步：显示覆盖层**
1. **点击"显示覆盖层"按钮**
2. **游戏界面上会出现透明覆盖层**
3. **显示网格区域和结界位置**

### **第3步：实时调整网格**
现在您可以直接在游戏界面上看到调整效果！

#### **调整网格区域**
- 拖拽"左、上、右、下"滑块
- 🟡 **黄色边框**实时显示网格区域
- 确保黄色边框完全覆盖结界列表

#### **调整网格布局**
- 设置行数和列数 (支持1-5)
- 调整水平和垂直间距
- 🔴 **红色矩形**显示结界位置

#### **调整结界项尺寸**
- 拖拽宽度和高度滑块
- 确保红色矩形完全覆盖每个结界图标

### **第4步：保存配置**
1. **调整满意后点击"💾 保存配置"**
2. **配置保存到 `custom_grid_config.json`**
3. **重启程序后自动生效**

## 🎨 **覆盖层视觉指示**

### **颜色含义**
- **🟡 黄色边框**: 网格区域边界
- **🔴 红色矩形**: 结界项点击区域 (半透明)
- **🟢 绿色圆点**: 结界项中心点
- **⚪ 白色数字**: 结界索引编号 (0-8)
- **⚫ 半透明背景**: 覆盖层背景

### **覆盖层信息显示**
```
🟡 黄色边框: 网格区域
🔴 红色矩形: 结界位置
🟢 绿色圆点: 中心点
⚪ 白色数字: 索引编号

按 ESC 键关闭覆盖层
```

## 🔧 **控制界面说明**

### **界面布局**
```
┌─────────────────────────────────────┐
│      游戏界面网格覆盖调整            │
├─────────────────────────────────────┤
│  🎮 游戏窗口连接                    │
│  [🎮 连接游戏窗口]                  │
│  ✅ 已连接: 阴阳师-网易游戏          │
│  [显示覆盖层] [隐藏覆盖层]          │
├─────────────────────────────────────┤
│  📐 网格配置 (实时调整)             │
│  ┌─ 网格区域 ─────────────────────┐ │
│  │ 左: [====]  12%                │ │
│  │ 上: [====]  20%                │ │
│  │ 右: [====]  88%                │ │
│  │ 下: [====]  80%                │ │
│  └─────────────────────────────────┘ │
│  ┌─ 网格布局 ─────────────────────┐ │
│  │ 行数: [3]  列数: [3]           │ │
│  │ 水平间距: [==] 2%              │ │
│  │ 垂直间距: [==] 3%              │ │
│  └─────────────────────────────────┘ │
│  ┌─ 结界项尺寸 ───────────────────┐ │
│  │ 宽度: [====] 22%               │ │
│  │ 高度: [====] 15%               │ │
│  └─────────────────────────────────┘ │
├─────────────────────────────────────┤
│  ⚙️ 操作                            │
│  [💾 保存配置]                      │
│  [🔄 重置默认]                      │
└─────────────────────────────────────┘
```

## 🎯 **实际使用效果**

### **游戏界面覆盖效果**
当您显示覆盖层后，游戏界面上会看到：

```
游戏界面
┌─────────────────────────────────────┐
│  阴阳师 - 结界突破界面               │
│                                     │
│    ┌─────────────────────────┐      │ ← 🟡 黄色网格区域边框
│    │ ┌───┐ ┌───┐ ┌───┐      │      │
│    │ │ 0 │ │ 1 │ │ 2 │      │      │ ← 🔴 红色结界矩形
│    │ └───┘ └───┘ └───┘      │      │   ⚪ 白色索引数字
│    │ ┌───┐ ┌───┐ ┌───┐      │      │   🟢 绿色中心点
│    │ │ 3 │ │ 4 │ │ 5 │      │      │
│    │ └───┘ └───┘ └───┘      │      │
│    │ ┌───┐ ┌───┐ ┌───┐      │      │
│    │ │ 6 │ │ 7 │ │ 8 │      │      │
│    │ └───┘ └───┘ └───┘      │      │
│    └─────────────────────────┘      │
│                                     │
│  🟡 黄色边框: 网格区域               │ ← 覆盖层信息显示
│  🔴 红色矩形: 结界位置               │
│  🟢 绿色圆点: 中心点                 │
│  ⚪ 白色数字: 索引编号               │
│                                     │
│  按 ESC 键关闭覆盖层                │
└─────────────────────────────────────┘
```

## 🔧 **调整技巧**

### **精确定位方法**
1. **先调整网格区域**
   - 拖拽滑块使黄色边框完全覆盖结界列表
   - 不要包含其他界面元素

2. **再调整结界项尺寸**
   - 红色矩形应该完全覆盖每个结界图标
   - 不要过大导致重叠

3. **最后优化间距**
   - 确保相邻结界不重叠
   - 保持合理的间距

### **实时调整优势**
- **立即看到效果**: 拖拽滑块立即在游戏界面显示
- **精确定位**: 直接对准游戏中的结界位置
- **避免猜测**: 不需要计算，直接看到覆盖效果
- **快速验证**: 调整后立即知道是否合适

## ⚠️ **注意事项**

### **使用要求**
- 游戏必须在运行状态
- 结界突破界面必须完全显示
- 不要在调整过程中最小化游戏窗口

### **覆盖层控制**
- **显示覆盖层**: 开始调整时点击
- **隐藏覆盖层**: 调整完成后点击
- **ESC键**: 快速关闭覆盖层
- **窗口置顶**: 控制窗口始终在最前面

### **性能说明**
- 覆盖层使用透明窗口技术
- 对游戏性能影响极小
- 调整完成后建议隐藏覆盖层

## 🚀 **常见问题解决**

### **❌ 找不到游戏窗口**
**解决方案**：
1. 确保阴阳师游戏正在运行
2. 游戏窗口标题包含"阴阳师"、"Onmyoji"或"网易"
3. 尝试重新连接游戏窗口

### **❌ 覆盖层显示异常**
**解决方案**：
1. 隐藏后重新显示覆盖层
2. 重新连接游戏窗口
3. 检查游戏窗口是否被遮挡

### **❌ 调整无效果**
**解决方案**：
1. 确保覆盖层正在显示
2. 检查滑块是否正常拖拽
3. 重启覆盖工具

## 🎊 **总结**

### ✅ **游戏界面覆盖的优势**
- **🎯 直观精确**: 直接在游戏界面上调整，完全所见即所得
- **⚡ 实时反馈**: 拖拽滑块立即在游戏中显示效果
- **🎮 完美适配**: 直接对准游戏中的实际结界位置
- **🔧 简单易用**: 无需复杂计算，直接可视化调整
- **💯 精确定位**: 避免所有猜测，确保100%准确

### 🚀 **开始使用**
这就是您想要的功能！直接在游戏界面上调整网格，实时看到效果！

**点击"界面映射"按钮，开始在游戏界面上直接调整网格吧！** 🎯
