#!/usr/bin/env python3
"""结界突破3×3网格计算详解和可视化"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle
import matplotlib.patches as patches

def explain_grid_calculation():
    """详细解释3×3网格计算逻辑"""
    print("🔍 结界突破3×3网格计算详解")
    print("=" * 60)
    
    # 模拟屏幕分辨率
    screen_width = 1920
    screen_height = 1080
    
    print(f"📺 假设屏幕分辨率: {screen_width} × {screen_height}")
    
    # 网格配置（来自配置文件）
    grid_config = {
        "grid_area": {
            "left": 0.12,      # 12%屏幕宽度
            "top": 0.20,       # 20%屏幕高度  
            "right": 0.88,     # 88%屏幕宽度
            "bottom": 0.80     # 80%屏幕高度
        },
        "layout": {
            "rows": 3,         # 3行
            "columns": 3,      # 3列
            "item_spacing": {
                "horizontal": 0.02,  # 2%屏幕宽度的水平间距
                "vertical": 0.03     # 3%屏幕高度的垂直间距
            }
        },
        "item_size": {
            "width": 0.22,     # 22%屏幕宽度
            "height": 0.15     # 15%屏幕高度
        }
    }
    
    print("\n📐 网格配置参数:")
    print(f"网格区域: 左{grid_config['grid_area']['left']*100:.0f}% 上{grid_config['grid_area']['top']*100:.0f}% 右{grid_config['grid_area']['right']*100:.0f}% 下{grid_config['grid_area']['bottom']*100:.0f}%")
    print(f"布局: {grid_config['layout']['rows']}行 × {grid_config['layout']['columns']}列")
    print(f"间距: 水平{grid_config['layout']['item_spacing']['horizontal']*100:.0f}% 垂直{grid_config['layout']['item_spacing']['vertical']*100:.0f}%")
    print(f"单项尺寸: 宽{grid_config['item_size']['width']*100:.0f}% 高{grid_config['item_size']['height']*100:.0f}%")
    
    # 第一步：计算网格区域的像素坐标
    print(f"\n🎯 第一步：计算网格区域像素坐标")
    grid_left = int(grid_config["grid_area"]["left"] * screen_width)
    grid_top = int(grid_config["grid_area"]["top"] * screen_height)
    grid_right = int(grid_config["grid_area"]["right"] * screen_width)
    grid_bottom = int(grid_config["grid_area"]["bottom"] * screen_height)
    
    print(f"网格区域像素坐标:")
    print(f"  左边界: {grid_config['grid_area']['left']} × {screen_width} = {grid_left}px")
    print(f"  上边界: {grid_config['grid_area']['top']} × {screen_height} = {grid_top}px")
    print(f"  右边界: {grid_config['grid_area']['right']} × {screen_width} = {grid_right}px")
    print(f"  下边界: {grid_config['grid_area']['bottom']} × {screen_height} = {grid_bottom}px")
    print(f"  网格区域尺寸: {grid_right - grid_left} × {grid_bottom - grid_top}px")
    
    # 第二步：计算单个结界项的像素尺寸
    print(f"\n📏 第二步：计算单个结界项像素尺寸")
    item_width = int(grid_config["item_size"]["width"] * screen_width)
    item_height = int(grid_config["item_size"]["height"] * screen_height)
    
    print(f"单个结界项尺寸:")
    print(f"  宽度: {grid_config['item_size']['width']} × {screen_width} = {item_width}px")
    print(f"  高度: {grid_config['item_size']['height']} × {screen_height} = {item_height}px")
    
    # 第三步：计算间距
    print(f"\n📐 第三步：计算间距")
    spacing_h = int(grid_config["layout"]["item_spacing"]["horizontal"] * screen_width)
    spacing_v = int(grid_config["layout"]["item_spacing"]["vertical"] * screen_height)
    
    print(f"间距:")
    print(f"  水平间距: {grid_config['layout']['item_spacing']['horizontal']} × {screen_width} = {spacing_h}px")
    print(f"  垂直间距: {grid_config['layout']['item_spacing']['vertical']} × {screen_height} = {spacing_v}px")
    
    # 第四步：计算3×3网格的所有位置
    print(f"\n🗂️ 第四步：计算3×3网格所有位置")
    positions = {}
    
    print(f"网格索引布局:")
    print(f"┌─────┬─────┬─────┐")
    print(f"│  0  │  1  │  2  │")
    print(f"├─────┼─────┼─────┤")
    print(f"│  3  │  4  │  5  │")
    print(f"├─────┼─────┼─────┤")
    print(f"│  6  │  7  │  8  │")
    print(f"└─────┴─────┴─────┘")
    
    print(f"\n📍 详细位置计算:")
    
    for row in range(3):
        for col in range(3):
            # 核心计算公式
            x = grid_left + col * (item_width + spacing_h)
            y = grid_top + row * (item_height + spacing_v)
            
            # 计算网格索引（0-8）
            grid_index = row * 3 + col
            
            # 计算中心点
            center_x = x + item_width // 2
            center_y = y + item_height // 2
            
            # 计算可点击区域（稍微缩小避免误点）
            click_left = x + 8
            click_top = y + 8
            click_right = x + item_width - 8
            click_bottom = y + item_height - 8
            
            positions[f"r{row}c{col}"] = {
                "index": grid_index,
                "row": row,
                "col": col,
                "rect": {
                    "left": x,
                    "top": y,
                    "right": x + item_width,
                    "bottom": y + item_height
                },
                "center": {
                    "x": center_x,
                    "y": center_y
                },
                "click_area": {
                    "left": click_left,
                    "top": click_top,
                    "right": click_right,
                    "bottom": click_bottom
                }
            }
            
            print(f"  索引{grid_index} (第{row}行第{col}列):")
            print(f"    计算: x = {grid_left} + {col} × ({item_width} + {spacing_h}) = {x}")
            print(f"    计算: y = {grid_top} + {row} × ({item_height} + {spacing_v}) = {y}")
            print(f"    矩形: ({x}, {y}) → ({x + item_width}, {y + item_height})")
            print(f"    中心: ({center_x}, {center_y})")
            print(f"    点击区域: ({click_left}, {click_top}) → ({click_right}, {click_bottom})")
            print()
    
    return positions, screen_width, screen_height, grid_config

def visualize_grid(positions, screen_width, screen_height, grid_config):
    """可视化3×3网格布局"""
    print("🎨 生成3×3网格可视化图")
    
    # 创建图形
    fig, ax = plt.subplots(1, 1, figsize=(12, 8))
    
    # 设置坐标轴
    ax.set_xlim(0, screen_width)
    ax.set_ylim(0, screen_height)
    ax.invert_yaxis()  # 反转Y轴，使(0,0)在左上角
    
    # 绘制屏幕边界
    screen_rect = Rectangle((0, 0), screen_width, screen_height, 
                           linewidth=2, edgecolor='black', facecolor='lightgray', alpha=0.3)
    ax.add_patch(screen_rect)
    
    # 绘制网格区域
    grid_left = int(grid_config["grid_area"]["left"] * screen_width)
    grid_top = int(grid_config["grid_area"]["top"] * screen_height)
    grid_width = int((grid_config["grid_area"]["right"] - grid_config["grid_area"]["left"]) * screen_width)
    grid_height = int((grid_config["grid_area"]["bottom"] - grid_config["grid_area"]["top"]) * screen_height)
    
    grid_area_rect = Rectangle((grid_left, grid_top), grid_width, grid_height,
                              linewidth=2, edgecolor='blue', facecolor='lightblue', alpha=0.2)
    ax.add_patch(grid_area_rect)
    
    # 颜色映射
    colors = ['red', 'orange', 'yellow', 'green', 'cyan', 'blue', 'purple', 'pink', 'brown']
    
    # 绘制每个结界位置
    for i, (key, pos) in enumerate(positions.items()):
        rect = pos["rect"]
        center = pos["center"]
        click_area = pos["click_area"]
        index = pos["index"]
        
        # 绘制结界矩形
        realm_rect = Rectangle((rect["left"], rect["top"]), 
                              rect["right"] - rect["left"], 
                              rect["bottom"] - rect["top"],
                              linewidth=2, edgecolor=colors[index], 
                              facecolor=colors[index], alpha=0.3)
        ax.add_patch(realm_rect)
        
        # 绘制点击区域
        click_rect = Rectangle((click_area["left"], click_area["top"]),
                              click_area["right"] - click_area["left"],
                              click_area["bottom"] - click_area["top"],
                              linewidth=1, edgecolor=colors[index], 
                              facecolor=colors[index], alpha=0.6)
        ax.add_patch(click_rect)
        
        # 标注索引和中心点
        ax.plot(center["x"], center["y"], 'ko', markersize=8)
        ax.text(center["x"], center["y"], str(index), 
               ha='center', va='center', fontsize=14, fontweight='bold', color='white')
        
        # 标注坐标
        ax.text(rect["left"], rect["top"] - 10, 
               f'({rect["left"]}, {rect["top"]})', 
               ha='left', va='bottom', fontsize=8, color=colors[index])
    
    # 添加标题和标签
    ax.set_title(f'结界突破3×3网格布局\n屏幕分辨率: {screen_width}×{screen_height}', fontsize=16, fontweight='bold')
    ax.set_xlabel('X坐标 (像素)', fontsize=12)
    ax.set_ylabel('Y坐标 (像素)', fontsize=12)
    
    # 添加图例
    legend_elements = [
        patches.Patch(color='lightgray', alpha=0.3, label='屏幕区域'),
        patches.Patch(color='lightblue', alpha=0.2, label='网格区域'),
        patches.Patch(color='red', alpha=0.3, label='结界矩形'),
        patches.Patch(color='red', alpha=0.6, label='点击区域'),
    ]
    ax.legend(handles=legend_elements, loc='upper right')
    
    # 添加网格
    ax.grid(True, alpha=0.3)
    
    # 保存图片
    plt.tight_layout()
    plt.savefig('realm_raid_3x3_grid.png', dpi=300, bbox_inches='tight')
    print("✅ 可视化图已保存为: realm_raid_3x3_grid.png")
    
    return fig

def demonstrate_selection_strategies(positions):
    """演示不同的选择策略"""
    print("\n🎯 选择策略演示")
    print("=" * 40)
    
    # 按索引分组
    all_indices = list(range(9))
    
    strategies = {
        "smart_grid": {
            "high": [0, 2, 6, 8],      # 四个角
            "medium": [1, 3, 5, 7],    # 四条边
            "low": [4]                 # 中心位置
        },
        "random": {
            "all": all_indices
        },
        "top_left_first": {
            "priority": [0, 1, 3, 2, 4, 6, 5, 7, 8]
        },
        "avoid_center": {
            "preferred": [0, 1, 2, 3, 5, 6, 7, 8],
            "avoided": [4]
        }
    }
    
    print("📋 各种选择策略:")
    
    for strategy_name, groups in strategies.items():
        print(f"\n🔸 {strategy_name} 策略:")
        
        if strategy_name == "smart_grid":
            print(f"  高优先级(角落): {groups['high']}")
            print(f"  中优先级(边缘): {groups['medium']}")
            print(f"  低优先级(中心): {groups['low']}")
            print("  选择逻辑: 优先选择角落 → 边缘 → 中心")
            
        elif strategy_name == "random":
            print(f"  随机选择: {groups['all']}")
            print("  选择逻辑: 完全随机，避免模式识别")
            
        elif strategy_name == "top_left_first":
            print(f"  优先顺序: {groups['priority']}")
            print("  选择逻辑: 从左上角开始，按行优先顺序")
            
        elif strategy_name == "avoid_center":
            print(f"  优选位置: {groups['preferred']}")
            print(f"  避免位置: {groups['avoided']}")
            print("  选择逻辑: 避免中心位置，优选周围8个位置")

def create_calculation_summary():
    """创建计算公式总结"""
    print("\n📐 3×3网格计算公式总结")
    print("=" * 50)
    
    formulas = """
🔢 核心计算公式:

1️⃣ 网格区域像素坐标:
   grid_left = grid_area.left × screen_width
   grid_top = grid_area.top × screen_height

2️⃣ 单个结界项像素尺寸:
   item_width = item_size.width × screen_width
   item_height = item_size.height × screen_height

3️⃣ 间距像素值:
   spacing_h = item_spacing.horizontal × screen_width
   spacing_v = item_spacing.vertical × screen_height

4️⃣ 结界位置计算:
   x = grid_left + col × (item_width + spacing_h)
   y = grid_top + row × (item_height + spacing_v)

5️⃣ 网格索引计算:
   index = row × columns + col
   (对于3×3网格: index = row × 3 + col)

6️⃣ 中心点计算:
   center_x = x + item_width ÷ 2
   center_y = y + item_height ÷ 2

7️⃣ 点击区域计算:
   click_left = x + margin
   click_top = y + margin
   click_right = x + item_width - margin
   click_bottom = y + item_height - margin
   (margin = 8px，避免误点边缘)

🎯 索引到坐标的映射:
   row = index ÷ 3
   col = index % 3
"""
    
    print(formulas)
    
    # 示例计算
    print("💡 示例计算 (1920×1080分辨率):")
    print("假设要计算索引5的位置 (第1行第2列):")
    print("  row = 5 ÷ 3 = 1")
    print("  col = 5 % 3 = 2")
    print("  x = 230 + 2 × (422 + 38) = 230 + 2 × 460 = 1150")
    print("  y = 216 + 1 × (162 + 32) = 216 + 194 = 410")
    print("  center = (1150 + 211, 410 + 81) = (1361, 491)")

def main():
    """主函数"""
    print("🚀 结界突破3×3网格计算完整解析")
    print("=" * 70)
    
    # 详细解释计算过程
    positions, screen_width, screen_height, grid_config = explain_grid_calculation()
    
    # 生成可视化图
    try:
        fig = visualize_grid(positions, screen_width, screen_height, grid_config)
        plt.show()
    except Exception as e:
        print(f"⚠️ 可视化生成失败: {e}")
        print("请确保安装了matplotlib: pip install matplotlib")
    
    # 演示选择策略
    demonstrate_selection_strategies(positions)
    
    # 计算公式总结
    create_calculation_summary()
    
    print(f"\n🎊 解析完成！")
    print("=" * 70)
    print("📋 总结:")
    print("• 3×3网格使用相对坐标系统，适配不同分辨率")
    print("• 通过数学公式精确计算每个结界的位置")
    print("• 支持多种智能选择策略")
    print("• 包含点击区域优化，避免误点边缘")
    print("• 索引0-8对应左上到右下的9个位置")

if __name__ == "__main__":
    main()
