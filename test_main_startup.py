#!/usr/bin/env python3
"""测试主程序启动问题"""

import sys
import os
import logging
import traceback

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试导入"""
    print("🔍 测试模块导入...")
    
    try:
        print("  ✓ 导入 PyQt5...")
        from PyQt5.QtWidgets import QApplication, QMessageBox
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QIcon
        print("  ✅ PyQt5 导入成功")
    except Exception as e:
        print(f"  ❌ PyQt5 导入失败: {e}")
        return False
    
    try:
        print("  ✓ 导入核心模块...")
        from core.logging_config import setup_logging
        print("  ✅ 日志配置导入成功")
    except Exception as e:
        print(f"  ❌ 日志配置导入失败: {e}")
        return False
    
    try:
        print("  ✓ 导入主窗口...")
        from core.main_window import MainWindow
        print("  ✅ 主窗口导入成功")
    except Exception as e:
        print(f"  ❌ 主窗口导入失败: {e}")
        print(f"  详细错误: {traceback.format_exc()}")
        return False
    
    return True

def test_realm_raid_import():
    """测试结界突破模块导入"""
    print("🏰 测试结界突破模块导入...")
    
    try:
        print("  ✓ 导入结界突破配置...")
        from features.realm_raid.realm_raid_config import REALM_GRID_CONFIG
        print("  ✅ 结界突破配置导入成功")
    except Exception as e:
        print(f"  ❌ 结界突破配置导入失败: {e}")
        return False
    
    try:
        print("  ✓ 导入结界突破GUI...")
        from features.realm_raid.realm_raid_gui_simple import RealmRaidGUI
        print("  ✅ 结界突破GUI导入成功")
    except Exception as e:
        print(f"  ❌ 结界突破GUI导入失败: {e}")
        print(f"  详细错误: {traceback.format_exc()}")
        return False
    
    try:
        print("  ✓ 导入交互式配置...")
        from features.realm_raid.interactive_grid_config import InteractiveGridConfigDialog
        print("  ✅ 交互式配置导入成功")
    except Exception as e:
        print(f"  ❌ 交互式配置导入失败: {e}")
        print(f"  详细错误: {traceback.format_exc()}")
        return False
    
    return True

def test_minimal_gui():
    """测试最小GUI"""
    print("🖥️ 测试最小GUI...")
    
    try:
        from PyQt5.QtWidgets import QApplication, QWidget, QLabel, QVBoxLayout
        
        app = QApplication(sys.argv)
        
        window = QWidget()
        window.setWindowTitle("测试窗口")
        window.setGeometry(100, 100, 300, 200)
        
        layout = QVBoxLayout()
        label = QLabel("✅ GUI测试成功！")
        layout.addWidget(label)
        window.setLayout(layout)
        
        window.show()
        print("  ✅ 最小GUI创建成功")
        
        # 立即关闭
        window.close()
        app.quit()
        return True
        
    except Exception as e:
        print(f"  ❌ 最小GUI测试失败: {e}")
        print(f"  详细错误: {traceback.format_exc()}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("🔧 主程序启动问题诊断")
    print("=" * 50)
    
    # 测试基础导入
    if not test_imports():
        print("❌ 基础模块导入失败，请检查依赖")
        return 1
    
    # 测试结界突破模块
    if not test_realm_raid_import():
        print("❌ 结界突破模块导入失败")
        return 1
    
    # 测试最小GUI
    if not test_minimal_gui():
        print("❌ GUI测试失败")
        return 1
    
    print("=" * 50)
    print("✅ 所有测试通过！")
    print("💡 建议:")
    print("  1. 检查是否有中文编码问题")
    print("  2. 尝试在命令行中设置编码: chcp 65001")
    print("  3. 检查是否有OCR模块冲突")
    print("=" * 50)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
