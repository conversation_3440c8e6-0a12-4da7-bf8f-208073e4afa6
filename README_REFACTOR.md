# 阴阳师自动化工具 - 重构版

本项目是对原有阴阳师探索自动化脚本的重构版本，采用模块化设计，将核心功能与具体功能实现分离，便于后续扩展其他功能。

## 项目结构

```
onmyoji_auto/ (主目录)
├── core/ (核心功能)
│   ├── __init__.py
│   ├── base_bot.py (基础自动化类)
│   ├── window_utils.py (窗口操作工具)
│   ├── image_utils.py (图像识别核心工具)
│   ├── template_manager.py (模板管理器)
│   ├── adaptive_template.py (自适应模板系统)
│   ├── screenshot.py (屏幕截图工具)
│   ├── auto_clicker.py (自动点击工具)
│   ├── background_operation.py (后台操作核心)
│   └── gui_common.py (GUI公共组件)
├── features/ (功能模块)
│   ├── __init__.py
│   └── explore/ (探索功能)
│       ├── __init__.py
│       ├── explore_bot.py (探索机器人)
│       ├── explore_gui.py (探索GUI组件)
│       └── explore_config.py (探索配置)
├── templates/ (模板图像，按功能分类)
│   ├── common/ (公共模板)
│   └── explore/ (探索专用模板)
├── assets/ (资源文件)
│   └── templates/ (核心功能模板)
├── main.py (命令行主程序)
└── main_gui.py (GUI主程序，支持多功能选择)
```

## 重构设计理念

1. **模块化设计**：将不同功能和组件分离到不同模块，便于维护和扩展
2. **核心与功能分离**：将核心功能和具体业务功能分离，避免代码重复
3. **统一接口**：提供统一的API接口，方便不同功能模块的实现
4. **配置与实现分离**：将配置参数与实现代码分离，便于调整和定制
5. **自适应设计**：通过自适应模板系统支持不同分辨率和屏幕尺寸
6. **后台操作支持**：通过后台操作模块支持窗口最小化或非前台状态下的自动化

## 模块说明

### 核心模块 (core)

- **base_bot.py**: 基础自动化机器人类，提供窗口控制、鼠标操作、图像识别等基础功能
- **window_utils.py**: 窗口操作工具类，提供窗口查找、获取和操作的方法
- **image_utils.py**: 图像处理工具类，提供图像识别、匹配和处理的方法
- **template_manager.py**: 模板管理器，管理所有模板图像和匹配参数
- **adaptive_template.py**: 自适应模板系统，支持不同分辨率下的模板匹配和相对坐标定位
- **screenshot.py**: 屏幕截图工具，提供高效的屏幕截图功能
- **auto_clicker.py**: 自动点击工具，提供鼠标操作功能
- **background_operation.py**: 后台操作核心，支持窗口非前台状态下的截图和操作
- **gui_common.py**: GUI公共组件，提供多个功能模块共用的界面组件

### 功能模块 (features)

每个功能模块包含以下组件：
- **xxxx_bot.py**: 功能机器人类，实现具体功能的自动化
- **xxxx_gui.py**: 功能GUI组件，提供该功能的界面
- **xxxx_config.py**: 功能配置，存储该功能的默认配置和设置

当前实现的功能模块：
- **explore**: 探索副本自动化功能

## 自适应模板系统

自适应模板系统是本项目的核心功能之一，它解决了不同分辨率和屏幕尺寸下的自动化问题。主要特点包括：

1. **动态缩放**：根据当前窗口分辨率自动缩放模板图像
2. **相对坐标**：使用相对坐标系统（0.0-1.0）代替绝对像素坐标
3. **自适应匹配**：根据不同分辨率动态调整匹配参数和阈值
4. **并行模板查找**：支持多核并行处理，提高模板匹配效率

### 使用方法

1. **基本用法**：

```python
# 初始化机器人时指定基准分辨率
bot = ExploreBot(hwnd=game_hwnd, base_resolution=(1920, 1080))

# 加载模板时指定相对点击位置
bot.load_template("button", "templates/button.png", rel_click_point=(0.5, 0.5))

# 查找并点击模板
bot.click_template("button")
```

2. **高级用法**：

```python
# 查找模板并获取位置
position = bot.find_template("button", threshold=0.75)
if position:
    bot.click(*position)

# 查找多个匹配
matches = bot.find_all_templates("icon", threshold=0.8, max_results=5)
for pos in matches:
    bot.click(*pos)
    
# 使用相对坐标进行点击
bot.click_template("button") 
# 或者直接使用相对坐标
center_pos = bot.get_random_screen_position(0.5, 0.5, 0.05)  # 屏幕中心位置±5%随机偏移
bot.click(*center_pos)

# 并行查找多个模板
templates = ["button1", "button2", "icon1", "icon2"]
results = bot.find_multiple_templates_parallel(templates)
for name, pos in results.items():
    print(f"找到模板 {name} 在位置 {pos}")
```

## 后台操作系统

后台操作系统允许在窗口最小化或不在前台的情况下执行自动化操作，避免窗口占用屏幕空间和干扰用户其他操作。主要特点包括：

1. **后台截图**：不需要将窗口设为前台即可获取窗口截图
2. **后台点击**：使用Windows消息系统向目标窗口发送鼠标消息
3. **后台键盘操作**：向目标窗口发送键盘消息实现后台按键
4. **后台拖拽**：实现在后台执行鼠标拖拽操作
5. **自动切换**：可根据窗口状态自动在前台模式和后台模式之间切换

### 使用方法

1. **启用后台模式**：

```python
# 初始化机器人
bot = ExploreBot(hwnd=game_hwnd)

# 启用后台操作模式
bot.enable_background_mode(True)

# 正常使用Bot的方法，它们会自动调用后台操作实现
bot.click(100, 200)  # 在后台执行点击
screenshot = bot.take_screenshot()  # 在后台获取截图
```

2. **后台操作示例**：

```python
# 后台点击
bot.click(100, 200)  # 自动使用后台点击

# 后台拖拽
bot.drag(100, 200, 300, 400)  # 自动使用后台拖拽

# 后台发送按键
bot.send_key(win32con.VK_ESCAPE)  # 发送ESC键
```

3. **直接使用BackgroundOperation**：

```python
# 直接使用后台操作模块
bg_op = BackgroundOperation(hwnd=game_hwnd)

# 后台截图
screenshot = bg_op.capture_background()

# 后台点击
bg_op.click_background(100, 200)

# 检查窗口状态
if bg_op.is_window_minimized():
    print("窗口已最小化")
```

## 使用方法

### 图形界面

运行图形界面程序：
```
python main_gui.py
```

### 命令行

使用探索功能：
```
python main.py explore --title "游戏窗口标题"
```

或者使用窗口句柄：
```
python main.py explore --hwnd 12345
```

查看帮助：
```
python main.py explore --help
```

## 如何添加新功能

1. 在 `features` 目录下创建新的功能模块目录，如 `features/new_feature/`
2. 创建功能所需的文件：
   - `__init__.py`: 包初始化文件
   - `new_feature_bot.py`: 继承 `BaseBot` 的功能机器人类
   - `new_feature_gui.py`: 功能GUI组件
   - `new_feature_config.py`: 功能配置文件
3. 将功能特有的模板图像放在 `templates/new_feature/` 目录
4. 在 `main_gui.py` 中添加新功能的标签页
5. 在 `main.py` 中添加新功能的命令处理

## 如何利用自适应模板系统

在开发新功能时，可以按照以下步骤利用自适应模板系统：

1. **继承 BaseBot**：确保您的功能机器人类继承自 `BaseBot`
2. **指定基准分辨率**：在初始化时指定基准分辨率，通常为开发时使用的分辨率
```python
def __init__(self, hwnd=None):
    super().__init__(hwnd, base_resolution=(1920, 1080))
```
3. **加载模板并指定点击位置**：加载模板时指定相对点击位置
```python
self.load_template("button", "path/to/button.png", (0.5, 0.5))  # 点击中心位置
```
4. **使用相对坐标**：使用相对坐标进行定位和操作
```python
# 获取屏幕上相对位置0.8, 0.5处的像素坐标
position = self.get_random_screen_position(0.8, 0.5, 0.1)
self.click(*position)
```
5. **并行模板查找**：使用并行查找功能提高效率
```python
# 定义多个需要查找的模板
templates = ["button1", "button2", "icon1"]
# 并行查找
results = self.find_multiple_templates_parallel(templates)
```

## 如何利用后台操作系统

使用后台操作系统只需要简单地设置一个标志即可：

1. **启用后台模式**：在机器人初始化后启用后台模式
```python
def __init__(self, hwnd=None):
    super().__init__(hwnd, base_resolution=(1920, 1080))
    # 启用后台操作模式
    self.enable_background_mode(True)
```

2. **根据条件自动切换模式**：根据窗口状态自动切换
```python
def auto_switch_background_mode(self):
    # 检查窗口是否最小化或不在前台
    if (self.background_operation.is_window_minimized() or 
        not self.background_operation.is_window_foreground()):
        # 如果窗口不可见，启用后台模式
        self.enable_background_mode(True)
    else:
        # 否则使用前台模式以获得更好的性能
        self.enable_background_mode(False)
```

3. **正常使用Bot方法**：无需修改其他代码，所有操作会自动使用后台模式
```python
# 这些方法会根据后台模式设置自动选择使用前台或后台实现
self.click(100, 200)
self.right_click(300, 400)
self.drag(100, 100, 200, 200)
self.take_screenshot()
```

## 其他可能的功能模块

以下是一些可以考虑实现的功能模块：

1. **御魂副本**: 自动刷御魂副本
2. **结界突破**: 自动进行结界突破
3. **式神育成**: 自动进行式神育成
4. **妖气封印**: 自动寻找和挑战妖气封印
5. **阴阳寮**: 自动进行阴阳寮相关活动

## 注意事项

- 使用脚本可能违反游戏规则，请谨慎使用
- 长时间运行脚本可能导致游戏检测，建议间歇性使用
- 请确保模板图像与实际游戏界面匹配 
- 后台操作模式在某些系统上可能受到限制，特别是高权限应用

## Cursor 历史下载链接

此处保留 Cursor 相关下载链接 